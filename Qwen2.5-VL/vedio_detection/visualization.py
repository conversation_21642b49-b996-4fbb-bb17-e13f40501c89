#!/usr/bin/env python3
"""
检测结果可视化模块
根据检测结果在图像上绘制边界框
"""

import os
import json
import logging
from pathlib import Path
from typing import List, Dict, Any, Tuple
from PIL import Image, ImageDraw, ImageFont
import colorsys

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DetectionVisualizer:
    """检测结果可视化器"""
    
    def __init__(self):
        """初始化可视化器"""
        # 目标类别映射
        self.class_mapping = {
            "0": "drone",
            "1": "car", 
            "2": "ship",
            "3": "bus",
            "4": "pedestrian",
            "5": "cyclist"
        }
        
        # 为每个类别生成不同颜色
        self.class_colors = self.generate_class_colors()
        
        # 尝试加载字体
        self.font = self.load_font()
    
    def generate_class_colors(self) -> Dict[str, Tuple[int, int, int]]:
        """为每个类别生成不同的颜色"""
        colors = {}
        num_classes = len(self.class_mapping)
        
        for i, (class_id, class_name) in enumerate(self.class_mapping.items()):
            # 使用HSV色彩空间生成均匀分布的颜色
            hue = i / num_classes
            saturation = 0.8
            value = 0.9
            
            # 转换为RGB
            rgb = colorsys.hsv_to_rgb(hue, saturation, value)
            colors[class_id] = tuple(int(c * 255) for c in rgb)
        
        return colors
    
    def load_font(self, font_size: int = 10):
        """加载字体（微小目标优化）"""
        try:
            # 尝试加载系统字体，使用较小字号
            font_paths = [
                "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",  # 使用普通字体而非粗体
                "/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf",
                "/System/Library/Fonts/Arial.ttf",  # macOS
                "C:/Windows/Fonts/arial.ttf",  # Windows
            ]

            for font_path in font_paths:
                if os.path.exists(font_path):
                    return ImageFont.truetype(font_path, font_size)

            # 如果没有找到字体，使用默认字体
            return ImageFont.load_default()

        except Exception as e:
            logger.warning(f"加载字体失败，使用默认字体: {e}")
            return ImageFont.load_default()
    
    def draw_bounding_box(self, draw: ImageDraw.Draw, bbox: List[float],
                         class_id: str, class_name: str, confidence: float,
                         line_width: int = 1):
        """
        绘制边界框（针对微小目标优化）

        Args:
            draw: PIL绘图对象
            bbox: 边界框坐标 [x1, y1, x2, y2]
            class_id: 类别ID
            class_name: 类别名称
            confidence: 置信度
            line_width: 线宽
        """
        x1, y1, x2, y2 = bbox

        # 确保坐标顺序正确
        x1, x2 = min(x1, x2), max(x1, x2)
        y1, y2 = min(y1, y2), max(y1, y2)

        # 获取颜色
        color = self.class_colors.get(class_id, (255, 0, 0))  # 默认红色

        # 绘制边界框（细线条）
        draw.rectangle(
            [(x1, y1), (x2, y2)],
            outline=color,
            width=line_width
        )

        # 准备标签文本（简化）
        label = f"{class_name[:3]}"  # 只显示类别名称前3个字符

        # 计算文本大小
        try:
            bbox_text = draw.textbbox((0, 0), label, font=self.font)
            text_width = bbox_text[2] - bbox_text[0]
            text_height = bbox_text[3] - bbox_text[1]
        except:
            # 兼容旧版本PIL
            text_width, text_height = draw.textsize(label, font=self.font)

        # 计算标签位置（放在框的右上角外侧，避免遮挡目标）
        label_x = x2 + 2  # 框右侧外2像素
        label_y = y1 - text_height - 2  # 框上方

        # 如果标签会超出图像边界，调整位置
        if label_y < 0:
            label_y = y2 + 2  # 放到框下方

        # 绘制标签文本（无背景，直接显示文字）
        draw.text(
            (label_x, label_y),
            label,
            fill=color,
            font=self.font
        )
    
    def visualize_frame(self, image_path: str, detections: List[Dict[str, Any]], 
                       output_path: str) -> bool:
        """
        可视化单帧检测结果
        
        Args:
            image_path: 输入图像路径
            detections: 检测结果列表
            output_path: 输出图像路径
            
        Returns:
            是否成功
        """
        try:
            # 加载图像
            image = Image.open(image_path)
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # 创建绘图对象
            draw = ImageDraw.Draw(image)
            
            # 绘制每个检测结果
            for detection in detections:
                bbox = detection.get('bbox', [])
                class_id = detection.get('class_id', '0')
                class_name = detection.get('class_name', 'unknown')
                confidence = detection.get('confidence', 0.0)
                
                if len(bbox) == 4:
                    self.draw_bounding_box(
                        draw, bbox, class_id, class_name, confidence
                    )
            
            # 保存结果
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            image.save(output_path)
            
            logger.info(f"可视化结果已保存: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"可视化失败 {image_path}: {e}")
            return False
    
    def visualize_sequence_results(self, detection_file: str, output_dir: str) -> bool:
        """
        可视化序列检测结果
        
        Args:
            detection_file: 检测结果JSON文件路径
            output_dir: 输出目录
            
        Returns:
            是否成功
        """
        try:
            # 加载检测结果
            with open(detection_file, 'r', encoding='utf-8') as f:
                results = json.load(f)
            
            frame_paths = results.get('frame_paths', [])
            detections = results.get('detections', {})
            sequence_id = results.get('sequence_id', 0)
            
            if not frame_paths:
                logger.warning(f"检测结果中没有帧路径信息: {detection_file}")
                return False
            
            # 创建序列输出目录
            sequence_output_dir = os.path.join(output_dir, f"sequence_{sequence_id:03d}")
            os.makedirs(sequence_output_dir, exist_ok=True)
            
            success_count = 0
            
            # 处理每一帧
            for i, frame_path in enumerate(frame_paths):
                # 使用原始帧ID作为键
                frame_id = Path(frame_path).stem
                frame_detections = detections.get(frame_id, [])

                # 构建输出路径
                frame_name = Path(frame_path).stem
                output_path = os.path.join(
                    sequence_output_dir,
                    f"{frame_name}_detected.jpg"
                )
                
                # 可视化
                if self.visualize_frame(frame_path, frame_detections, output_path):
                    success_count += 1
                
                logger.info(f"处理帧 {i+1}/{len(frame_paths)}: {frame_name} (检测到 {len(frame_detections)} 个目标)")
            
            logger.info(f"序列 {sequence_id} 可视化完成: {success_count}/{len(frame_paths)} 帧成功")
            return success_count > 0
            
        except Exception as e:
            logger.error(f"可视化序列结果失败: {e}")
            return False
    
    def visualize_all_results(self, results_dir: str, output_dir: str):
        """
        可视化所有检测结果
        
        Args:
            results_dir: 检测结果目录
            output_dir: 输出目录
        """
        try:
            results_dir = Path(results_dir)
            
            # 查找所有检测结果文件
            detection_files = list(results_dir.glob("detection_sequence_*.json"))
            
            if not detection_files:
                logger.warning(f"在 {results_dir} 中没有找到检测结果文件")
                return
            
            logger.info(f"找到 {len(detection_files)} 个检测结果文件")
            
            # 处理每个文件
            success_count = 0
            for detection_file in sorted(detection_files):
                logger.info(f"处理检测结果: {detection_file.name}")
                
                if self.visualize_sequence_results(str(detection_file), output_dir):
                    success_count += 1
            
            logger.info(f"可视化完成: {success_count}/{len(detection_files)} 个序列成功")
            
        except Exception as e:
            logger.error(f"可视化所有结果失败: {e}")

def main():
    """主函数"""
    # 配置路径
    results_dir = "./output"
    visualization_output_dir = "./output/visualizations"
    
    try:
        # 初始化可视化器
        logger.info("初始化可视化器...")
        visualizer = DetectionVisualizer()
        
        # 可视化所有结果
        logger.info("开始可视化检测结果...")
        visualizer.visualize_all_results(results_dir, visualization_output_dir)
        
        logger.info("可视化完成！")
        
    except Exception as e:
        logger.error(f"可视化过程出错: {e}")
        raise

if __name__ == "__main__":
    main()
