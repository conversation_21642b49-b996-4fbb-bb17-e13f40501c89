#!/usr/bin/env python3
"""
红外图像微小目标检测系统
使用Qwen2.5-VL模型对视频序列进行高帧频微小目标检测
"""

import os
import json
import torch
import logging
from pathlib import Path
from typing import List, Dict, Any, Tuple
from PIL import Image
import ast
import re
import cv2
import numpy as np

# 设置CUDA设备
os.environ["CUDA_VISIBLE_DEVICES"] = "0"

from transformers import Qwen2_5_VLForConditionalGeneration, AutoProcessor
from qwen_vl_utils import process_vision_info
from video_composer import VideoComposer

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class InfraredTargetDetector:
    """红外图像微小目标检测器"""
    
    def __init__(self, model_path: str = "../Qwen2.5-VL-7B-Instruct"):
        """
        初始化检测器

        Args:
            model_path: Qwen2.5-VL模型路径
        """
        self.model_path = model_path
        self.model = None
        self.processor = None

        # 目标类别映射
        self.class_mapping = {
            "0": "drone",
            "1": "car",
            "2": "ship",
            "3": "bus",
            "4": "pedestrian",
            "5": "cyclist"
        }

        # 反向映射
        self.label_to_id = {v: k for k, v in self.class_mapping.items()}

        # 初始化视频合成器
        self.video_composer = VideoComposer()

        self.load_model()
    
    def load_model(self):
        """加载Qwen2.5-VL模型和处理器"""
        try:
            logger.info(f"正在加载模型: {self.model_path}")
            
            # 加载模型
            self.model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
                self.model_path, 
                torch_dtype="auto", 
                device_map="auto"
            )
            
            # 加载处理器
            self.processor = AutoProcessor.from_pretrained(self.model_path)
            
            logger.info("模型加载完成")
            
        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            raise
    
    def get_frame_sequence(self, data_dir: str, sequence_name: str, frame_interval: int = 5,
                          sequence_length: int = 5, frame_percentage: float = 1.0) -> List[List[str]]:
        """
        获取帧序列

        Args:
            data_dir: 数据目录
            sequence_name: 序列名称 (如 'data04')
            frame_interval: 帧间隔
            sequence_length: 序列长度
            frame_percentage: 处理帧的百分比 (0.0-1.0)

        Returns:
            帧序列列表，每个序列包含sequence_length帧的路径
        """
        sequence_dir = Path(data_dir) / "images" / sequence_name
        if not sequence_dir.exists():
            raise ValueError(f"序列目录不存在: {sequence_dir}")

        # 获取所有帧文件并按顺序排序
        frame_files = []

        # 检查序列类型并相应处理
        if sequence_name in ['data23', 'data25', 'data26']:
            # 特殊命名格式的序列
            for f in sequence_dir.glob("*.jpg"):
                frame_files.append(f)
            # 按文件名排序（这些文件名已经包含序号）
            frame_files = sorted(frame_files, key=lambda x: x.name)
        else:
            # 标准数字命名的序列
            for f in sequence_dir.glob("*.bmp"):
                try:
                    # 确保文件名可以转换为整数
                    int(f.stem)
                    frame_files.append(f)
                except ValueError:
                    logger.warning(f"跳过非数字命名的文件: {f.name}")
            # 按数字顺序排序
            frame_files = sorted(frame_files, key=lambda x: int(x.stem))
        
        if len(frame_files) == 0:
            raise ValueError(f"序列目录中没有找到帧文件: {sequence_dir}")
        
        logger.info(f"找到 {len(frame_files)} 帧文件")

        # 计算要处理的帧数量
        total_frames_to_process = int(len(frame_files) * frame_percentage)
        logger.info(f"将处理前 {frame_percentage*100:.1f}% 的帧，共 {total_frames_to_process} 帧")

        # 只使用前N%的帧
        frame_files_to_process = frame_files[:total_frames_to_process]

        # 生成帧序列（分组滑动窗口方式）
        sequences = []
        group_size = frame_interval * sequence_length  # 每组的帧数 (5 * 5 = 25)

        # 按组处理
        group_start = 0
        while group_start < len(frame_files_to_process):
            # 计算当前组的结束位置
            group_end = min(group_start + group_size, len(frame_files_to_process))

            # 在当前组内生成序列（滑动窗口）
            for offset in range(frame_interval):  # offset: 0, 1, 2, 3, 4
                start_frame = group_start + offset

                # 计算当前序列的帧索引
                frame_indices = []
                for i in range(sequence_length):
                    frame_idx = start_frame + i * frame_interval
                    if frame_idx < group_end and frame_idx < len(frame_files_to_process):
                        frame_indices.append(frame_idx)
                    else:
                        break

                # 如果序列长度不足，但至少有2帧，也可以生成序列
                if len(frame_indices) >= 2:
                    # 获取帧路径
                    frame_paths = [str(frame_files_to_process[idx]) for idx in frame_indices]
                    sequences.append(frame_paths)
                    logger.info(f"生成序列 {len(sequences)}: 帧 {[Path(p).stem for p in frame_paths]}")
                else:
                    break

            # 移动到下一组
            group_start += group_size

        logger.info(f"生成了 {len(sequences)} 个帧序列")
        return sequences
    
    def create_detection_prompt(self, frame_paths: List[str]) -> str:
        """创建检测提示词"""
        # 提取帧ID
        frame_ids = [Path(path).stem for path in frame_paths]

        # 构建统一的示例输出格式
        example_output = "{\n"
        for i, frame_id in enumerate(frame_ids):
            example_output += f'  "{frame_id}": [\n'
            example_output += '    {\n'
            example_output += '      "bbox": [x1, y1, x2, y2],\n'
            example_output += '      "class_name": "drone",\n'
            example_output += '      "confidence": 0.95\n'
            example_output += '    }\n'
            if i < len(frame_ids) - 1:
                example_output += '  ],\n'
            else:
                example_output += '  ]\n'
        example_output += "}"

        prompt = f"""这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。

视频分析任务：
1. 这是一个视频序列，不是静态图片，请利用帧间时序信息
2. 检测每帧中的微小目标，特别是移动的小白点或小亮点
3. 通过观察目标在不同帧中的位置变化来确认真实目标
4. 对检测到的目标进行分类和定位

检测重点：
- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）
- 利用视频时序性识别运动模式
- 排除静态背景噪声
- 精确的空间定位

请以JSON格式输出每一帧的检测结果：
```json
{example_output}
```

目标类别：
- drone: 无人机
- car: 汽车
- ship: 船只
- bus: 公交车
- pedestrian: 行人
- cyclist: 骑行者

注意：这是视频序列分析，请仔细观察每帧（{', '.join(frame_ids)}）中的微小目标，利用时序信息提高检测准确性。"""

        return prompt
    
    def detect_sequence(self, frame_paths: List[str], sequence_name: str = "data04",
                       sequence_id: int = 0) -> Dict[str, Any]:
        """
        对帧序列进行目标检测

        Args:
            frame_paths: 帧文件路径列表
            sequence_name: 序列名称
            sequence_id: 序列ID

        Returns:
            检测结果字典
        """
        try:
            # 先合成视频
            logger.info("合成视频...")
            video_path = self.video_composer.create_video_from_frames(
                frame_paths, sequence_name, sequence_id
            )

            # 获取视频信息
            video_info = self.video_composer.get_video_info(video_path)
            logger.info(f"视频信息: {video_info}")

            # 根据序列类型设置max_pixels
            if sequence_name in ['data23', 'data25', 'data26']:
                max_pixels = 640 * 512  # 高分辨率序列
            else:
                max_pixels = 256 * 256  # 标准分辨率序列

            # 构建消息，使用视频文件
            messages = [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "video",
                            "video": f"file://{os.path.abspath(video_path)}",
                            "max_pixels": max_pixels,
                            "fps": 5.0,  # 视频帧率（每秒抽取的帧数）
                        },
                        {
                            "type": "text",
                            "text": self.create_detection_prompt(frame_paths)
                        }
                    ]
                }
            ]

            # 调试信息
            logger.info(f"使用视频文件: {video_path}")
            logger.info(f"构建的消息格式: {messages[0]['content'][0]}")

            # 处理输入
            text = self.processor.apply_chat_template(
                messages, tokenize=False, add_generation_prompt=True
            )

            image_inputs, video_inputs, video_kwargs = process_vision_info(
                messages, return_video_kwargs=True
            )

            inputs = self.processor(
                text=[text],
                images=image_inputs,
                videos=video_inputs,
                padding=True,
                return_tensors="pt",
                **video_kwargs,
            )
            inputs = inputs.to(self.model.device)

            # 推理
            logger.info("开始推理...")
            generated_ids = self.model.generate(
                **inputs,
                max_new_tokens=2048,
                temperature=0.1,
                do_sample=True
            )

            generated_ids_trimmed = [
                out_ids[len(in_ids):] for in_ids, out_ids in zip(inputs.input_ids, generated_ids)
            ]

            output_text = self.processor.batch_decode(
                generated_ids_trimmed, skip_special_tokens=True, clean_up_tokenization_spaces=False
            )

            logger.info("推理完成")
            result = self.parse_detection_result(output_text[0], frame_paths)
            result["video_path"] = video_path  # 添加视频路径信息
            return result

        except Exception as e:
            logger.error(f"检测失败: {e}")
            return {}
    
    def parse_detection_result(self, output_text: str, frame_paths: List[str]) -> Dict[str, Any]:
        """
        解析检测结果

        Args:
            output_text: 模型输出文本
            frame_paths: 帧路径列表

        Returns:
            解析后的检测结果
        """
        try:
            logger.info("解析检测结果...")
            logger.info(f"原始输出: {output_text}")

            # 提取JSON部分
            json_match = re.search(r'```json\s*(.*?)\s*```', output_text, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
            else:
                # 尝试直接解析
                json_str = output_text.strip()

            # 解析JSON
            try:
                result = json.loads(json_str)
            except json.JSONDecodeError:
                # 尝试使用ast.literal_eval
                result = ast.literal_eval(json_str)

            # 验证结果格式并确保所有帧都有对应的键
            frame_ids = [Path(path).stem for path in frame_paths]
            validated_result = {}

            for frame_id in frame_ids:
                if frame_id in result:
                    validated_result[frame_id] = result[frame_id]
                else:
                    # 如果某帧没有检测结果，添加空列表
                    validated_result[frame_id] = []
                    logger.warning(f"帧 {frame_id} 没有检测结果，添加空列表")

            # 添加帧路径信息
            parsed_result = {
                "frame_paths": frame_paths,
                "detections": validated_result
            }

            logger.info(f"成功解析检测结果，包含 {len(validated_result)} 帧")
            return parsed_result

        except Exception as e:
            logger.error(f"解析检测结果失败: {e}")
            # 返回空结果，使用原始帧ID
            frame_ids = [Path(path).stem for path in frame_paths]
            empty_detections = {frame_id: [] for frame_id in frame_ids}
            return {
                "frame_paths": frame_paths,
                "detections": empty_detections
            }

def main():
    """主函数"""
    # 配置路径
    data_dir = "../tiaozhanbei_datasets"
    sequence_name = "data04"
    output_dir = "./output"
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        # 初始化检测器
        logger.info("初始化检测器...")
        detector = InfraredTargetDetector()
        
        # 获取帧序列（处理前20%的帧）
        logger.info("获取帧序列...")
        sequences = detector.get_frame_sequence(data_dir, sequence_name, frame_percentage=0.2)
        
        # 处理每个序列
        all_results = []
        for i, frame_paths in enumerate(sequences):
            logger.info(f"处理序列 {i+1}/{len(sequences)}")
            logger.info(f"帧路径: {[Path(p).name for p in frame_paths]}")
            
            # 检测
            result = detector.detect_sequence(frame_paths, sequence_name, i)
            result["sequence_id"] = i
            all_results.append(result)
            
            # 保存单个序列结果
            sequence_output_file = os.path.join(output_dir, f"detection_sequence_{i:03d}.json")
            with open(sequence_output_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            
            logger.info(f"序列 {i+1} 检测完成，结果已保存到 {sequence_output_file}")
        
        # 保存所有结果
        all_results_file = os.path.join(output_dir, "all_detection_results.json")
        with open(all_results_file, 'w', encoding='utf-8') as f:
            json.dump(all_results, f, ensure_ascii=False, indent=2)
        
        logger.info(f"所有检测完成，结果已保存到 {all_results_file}")
        
    except Exception as e:
        logger.error(f"检测过程出错: {e}")
        raise

if __name__ == "__main__":
    main()
