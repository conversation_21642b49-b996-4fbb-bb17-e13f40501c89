#!/bin/bash

# 红外图像微小目标检测系统 - 全序列批量处理脚本
# 一键处理所有序列的前20%帧

echo "=========================================="
echo "🎯 红外图像微小目标检测系统"
echo "=========================================="

# 激活conda环境
source ~/miniconda3/etc/profile.d/conda.sh
conda activate Qwen2_5_VL

# 记录开始时间
START_TIME=$(date)
echo "⏰ 开始时间: $START_TIME"
echo ""

# 一键批量处理所有序列
echo "🚀 开始批量处理所有序列..."
python detect.py \
    --data_dir "../tiaozhanbei_datasets" \
    --output_dir "./batch_output" \
    --frame_percentage 0.2 \
    --frame_interval 5 \
    --sequence_length 5 \
    --model_path "../Qwen2.5-VL-7B-Instruct"

# 检查执行结果
if [ $? -eq 0 ]; then
    echo ""
    echo "✅ 批量处理完成！"
else
    echo ""
    echo "❌ 批量处理失败！"
    exit 1
fi

# 记录结束时间
END_TIME=$(date)
echo "=========================================="
echo "📊 处理完成统计"
echo "=========================================="
echo "⏰ 开始时间: $START_TIME"
echo "⏰ 结束时间: $END_TIME"
echo "📁 结果目录: ./batch_output"
echo ""

# 生成汇总报告
echo "生成汇总报告..."
python -c "
import os
import json
from pathlib import Path

output_dir = Path('./batch_output')
sequences = ['data01', 'data02', 'data04', 'data05', 'data06', 'data07', 'data23', 'data25', 'data26']

print('=' * 60)
print('批量处理汇总报告')
print('=' * 60)

total_sequences = 0
total_success = 0
total_detections = 0

for seq in sequences:
    seq_dir = output_dir / seq
    if seq_dir.exists():
        # 统计检测结果
        detection_files = list(seq_dir.glob('detection_sequence_*.json'))
        seq_detections = 0
        
        for det_file in detection_files:
            try:
                with open(det_file, 'r') as f:
                    data = json.load(f)
                    if 'detections' in data:
                        for frame_id, dets in data['detections'].items():
                            seq_detections += len(dets)
            except:
                pass
        
        print(f'{seq}: {len(detection_files)} 个序列, {seq_detections} 个检测')
        total_sequences += len(detection_files)
        if len(detection_files) > 0:
            total_success += len(detection_files)
        total_detections += seq_detections
    else:
        print(f'{seq}: 未处理')

print('-' * 60)
print(f'总计: {total_success} 个成功序列, {total_detections} 个检测目标')
print('=' * 60)
"

echo "汇总报告生成完成！"

# 生成详细分析报告
echo ""
echo "📊 生成详细分析报告..."
python analyze_results.py --results_dir "./batch_output"

echo ""
echo "🎉 所有处理完成！"
