"""
红外视频目标检测LoRA微调训练脚本（完全修复版）
"""

import os
import json
import logging
import torch
from pathlib import Path
from datasets import Dataset
from transformers import (
    AutoProcessor,
    Qwen2_5_VLForConditionalGeneration,
    TrainingArguments,
    Trainer,
    DataCollatorForLanguageModeling
)
from peft import LoraConfig, get_peft_model, TaskType

def setup_logger():
    """设置日志记录器"""
    logging.basicConfig(level=logging.INFO)
    return logging.getLogger(__name__)

def main():
    """主函数"""
    logger = setup_logger()
    
    logger.info("🚀 开始红外视频目标检测LoRA微调...")
    
    # 配置路径
    model_path = "/home/<USER>/Qwen/Qwen2.5-VL/Qwen2.5-VL-7B-Instruct"
    data_path = "data/infrared_video_train.json"
    output_path = "output/lora_checkpoints"
    
    # 创建输出目录
    Path(output_path).mkdir(parents=True, exist_ok=True)
    
    # 加载训练数据
    logger.info("📊 加载训练数据...")
    with open(data_path, 'r', encoding='utf-8') as f:
        train_data = json.load(f)
    
    logger.info(f"✅ 使用完整数据集: {len(train_data)} 个样本进行训练")
    
    # 加载processor
    logger.info("🔤 加载processor...")
    processor = AutoProcessor.from_pretrained(model_path, trust_remote_code=True)
    
    # 预处理数据
    logger.info("📝 预处理数据...")
    processed_data = []

    for item in train_data:
        try:
            conversations = item['conversations']
            video_path = item['video']

            # 构建消息格式
            messages = [
                {
                    "role": "user",
                    "content": [
                        {"type": "video", "video": video_path},
                        {"type": "text", "text": conversations[0]['value'].replace('<video>\n', '')}
                    ]
                },
                {
                    "role": "assistant", 
                    "content": conversations[1]['value']
                }
            ]

            # 使用processor处理
            text = processor.apply_chat_template(messages, tokenize=False, add_generation_prompt=False)
            processed_data.append({"text": text})

        except Exception as e:
            logger.warning(f"处理样本失败: {e}")
            continue

    # 创建数据集
    dataset = Dataset.from_list(processed_data)
    
    # Tokenize数据
    def tokenize_function(examples):
        tokenized = processor(
            text=examples["text"],
            padding=True,
            truncation=True,
            max_length=2048,
            return_tensors=None
        )
        tokenized["labels"] = tokenized["input_ids"].copy()
        return tokenized

    tokenized_dataset = dataset.map(tokenize_function, batched=True, remove_columns=["text"])
    
    # 加载模型
    logger.info("🤖 加载模型...")
    model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
        model_path,
        torch_dtype=torch.bfloat16,
        device_map={"": "cuda:0"},
        trust_remote_code=True,
        attn_implementation="flash_attention_2"
    )
    
    # 配置LoRA
    logger.info("⚙️ 配置LoRA...")
    lora_config = LoraConfig(
        task_type=TaskType.CAUSAL_LM,
        target_modules=["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"],
        inference_mode=False,
        r=64,
        lora_alpha=64,
        lora_dropout=0.05,
        bias="none",
    )
    
    # 应用LoRA
    model = get_peft_model(model, lora_config)
    model.print_trainable_parameters()
    
    # 训练参数（完全修复版）
    training_args = TrainingArguments(
        output_dir=output_path,
        per_device_train_batch_size=2,
        gradient_accumulation_steps=8,
        num_train_epochs=1,
        learning_rate=2e-5,
        weight_decay=0.01,
        warmup_ratio=0.03,
        max_grad_norm=1.0,
        lr_scheduler_type="cosine",
        logging_steps=10,
        save_steps=100,
        save_strategy="steps",
        save_total_limit=3,
        remove_unused_columns=False,
        bf16=True,
        dataloader_num_workers=4,
        gradient_checkpointing=True,
        report_to="none",
    )
    
    # 数据整理器
    data_collator = DataCollatorForLanguageModeling(
        tokenizer=processor.tokenizer,
        mlm=False,
    )
    
    # 创建训练器
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=tokenized_dataset,
        data_collator=data_collator,
        processing_class=processor,
    )
    
    # 开始训练
    logger.info("🎯 开始LoRA训练...")
    logger.info(f"📊 训练配置:")
    logger.info(f"   样本数: {len(dataset)}")
    logger.info(f"   批次大小: {training_args.per_device_train_batch_size}")
    logger.info(f"   梯度累积: {training_args.gradient_accumulation_steps}")
    logger.info(f"   有效批次大小: {training_args.per_device_train_batch_size * training_args.gradient_accumulation_steps}")
    logger.info(f"   学习率: {training_args.learning_rate}")
    logger.info(f"   训练轮数: {training_args.num_train_epochs}")
    
    trainer.train()
    
    # 保存模型
    logger.info("💾 保存LoRA权重...")
    trainer.save_model()
    processor.save_pretrained(output_path)
    
    logger.info("🎉 LoRA微调完成!")
    logger.info(f"📁 模型保存在: {output_path}")
    
    # 列出保存的文件
    saved_files = os.listdir(output_path)
    logger.info(f"📋 保存的文件: {saved_files}")

if __name__ == "__main__":
    main()
