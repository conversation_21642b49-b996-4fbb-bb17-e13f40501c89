"""
红外视频目标检测LoRA微调训练脚本（使用官方视频处理逻辑）
"""

import os
import json
import logging
import torch
import sys
import copy
import numpy as np
from pathlib import Path
from datasets import Dataset
from transformers import (
    AutoProcessor,
    Qwen2_5_VLForConditionalGeneration,
    TrainingArguments,
    Trainer,
    DataCollatorForLanguageModeling
)
from peft import LoraConfig, get_peft_model, TaskType
from decord import VideoReader
from PIL import Image

def setup_logger():
    """设置日志记录器"""
    logging.basicConfig(level=logging.INFO)
    return logging.getLogger(__name__)

class VideoDataset(Dataset):
    """视频数据集类，基于官方实现"""

    def __init__(self, data_list, processor, video_max_frames=8, video_min_frames=4, base_interval=4):
        self.data_list = data_list
        self.processor = processor
        self.video_max_frames = video_max_frames
        self.video_min_frames = video_min_frames
        self.base_interval = base_interval

    def __len__(self):
        return len(self.data_list)

    def process_video(self, video_file):
        """处理视频文件，基于官方实现"""
        if not os.path.exists(video_file):
            raise FileNotFoundError(f"Video file not found: {video_file}")

        try:
            vr = VideoReader(video_file, num_threads=4)
            total_frames = len(vr)
            avg_fps = vr.get_avg_fps()
            video_length = total_frames / avg_fps

            # 计算采样帧数
            num_frames_to_sample = round(video_length / self.base_interval)
            target_frames = min(
                max(num_frames_to_sample, self.video_min_frames),
                self.video_max_frames
            )

            # 均匀采样帧
            frame_idx = np.linspace(0, total_frames - 1, target_frames, dtype=int)
            frame_idx = np.unique(frame_idx)
            video_frames = vr.get_batch(frame_idx).asnumpy()

            # 处理视频帧
            fps = len(frame_idx) / video_length

            # 使用processor处理视频帧
            processor_copy = copy.deepcopy(self.processor.image_processor)
            # 设置视频特定参数
            processor_copy.max_pixels = getattr(processor_copy, 'max_pixels', 1280*28*28)
            processor_copy.min_pixels = getattr(processor_copy, 'min_pixels', 56*56)

            # 将numpy数组转换为PIL图像
            pil_frames = [Image.fromarray(frame) for frame in video_frames]

            # 处理视频帧
            visual_processed = processor_copy(images=pil_frames, return_tensors="pt")
            video_tensor = visual_processed["pixel_values"]

            # 计算grid_thw
            grid_thw = torch.tensor([target_frames, 28, 28])  # 简化的grid_thw计算

            return video_tensor, grid_thw, fps

        except Exception as e:
            print(f"Error processing video {video_file}: {e}")
            # 返回空的tensor作为fallback
            return torch.zeros(1, 3, 224, 224), torch.tensor([1, 28, 28]), 1.0

    def __getitem__(self, idx):
        """获取数据项"""
        item = self.data_list[idx]
        conversations = item['conversations']
        video_path = item['video']

        # 处理视频
        video_tensor, grid_thw, fps = self.process_video(video_path)

        # 构建消息格式
        user_content = conversations[0]['value']
        assistant_content = conversations[1]['value']

        # 确保内容是字符串
        if isinstance(user_content, list):
            user_content = ' '.join(str(item) for item in user_content)
        if isinstance(assistant_content, list):
            assistant_content = ' '.join(str(item) for item in assistant_content)

        # 移除<video>标记
        user_text = user_content.replace('<video>\n', '').strip()

        messages = [
            {
                "role": "user",
                "content": [
                    {"type": "video", "video": video_path},
                    {"type": "text", "text": user_text}
                ]
            },
            {
                "role": "assistant",
                "content": assistant_content
            }
        ]

        # 使用processor处理对话
        try:
            text = self.processor.apply_chat_template(messages, tokenize=False, add_generation_prompt=False)
        except Exception as e:
            print(f"Error in apply_chat_template: {e}")
            # Fallback: 简单的文本拼接
            text = f"User: {user_text}\nAssistant: {assistant_content}"

        # Tokenize
        self.processor.tokenizer.padding_side = 'left'
        tokenized = self.processor.tokenizer(
            text,
            padding=False,
            truncation=True,
            max_length=2048,
            return_tensors="pt"
        )

        return {
            'input_ids': tokenized['input_ids'].squeeze(),
            'attention_mask': tokenized['attention_mask'].squeeze(),
            'labels': tokenized['input_ids'].squeeze().clone(),
            'pixel_values': video_tensor,
            'grid_thw': grid_thw
        }

def main():
    """主函数"""
    logger = setup_logger()

    # 指定使用0号GPU
    os.environ["CUDA_VISIBLE_DEVICES"] = "0"
    logger.info("🎯 指定使用GPU 0")

    logger.info("🚀 开始红外视频目标检测LoRA微调...")

    # 配置路径
    model_path = "/home/<USER>/Qwen/Qwen2.5-VL/Qwen2.5-VL-7B-Instruct"
    data_path = "data/infrared_video_train.json"
    output_path = "output/lora_checkpoints"
    
    # 创建输出目录
    Path(output_path).mkdir(parents=True, exist_ok=True)
    
    # 加载训练数据
    logger.info("📊 加载训练数据...")
    with open(data_path, 'r', encoding='utf-8') as f:
        train_data = json.load(f)
    
    logger.info(f"✅ 使用完整数据集: {len(train_data)} 个样本进行训练")
    
    # 加载processor
    logger.info("🔤 加载processor...")
    processor = AutoProcessor.from_pretrained(model_path, trust_remote_code=True)
    processor.tokenizer.padding_side = 'left'  # 设置左对齐填充

    # 设置tokenizer的padding_side为left（Flash Attention要求）
    processor.tokenizer.padding_side = 'left'
    logger.info("✅ 设置tokenizer padding_side为left（Flash Attention要求）")

    # 创建视频数据集
    logger.info("📝 创建视频数据集...")
    dataset = VideoDataset(
        data_list=train_data,
        processor=processor,
        video_max_frames=8,
        video_min_frames=4,
        base_interval=4
    )

    logger.info(f"✅ 视频数据集创建完成，包含 {len(dataset)} 个样本")
    
    # 加载模型
    logger.info("🤖 加载模型...")
    model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
        model_path,
        torch_dtype=torch.bfloat16,
        device_map={"": "cuda:0"},
        trust_remote_code=True,
        attn_implementation="flash_attention_2"
    )
    
    # 配置LoRA
    logger.info("⚙️ 配置LoRA...")
    lora_config = LoraConfig(
        task_type=TaskType.CAUSAL_LM,
        target_modules=["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"],
        inference_mode=False,
        r=64,
        lora_alpha=64,
        lora_dropout=0.05,
        bias="none",
    )
    
    # 应用LoRA
    model = get_peft_model(model, lora_config)
    model.print_trainable_parameters()
    
    # 训练参数（增加训练强度版）
    training_args = TrainingArguments(
        output_dir=output_path,
        per_device_train_batch_size=1,  # 减小批次大小
        gradient_accumulation_steps=4,  # 减小梯度累积
        num_train_epochs=3,  # 增加训练轮数
        learning_rate=1e-5,  # 进一步降低学习率
        weight_decay=0.01,
        warmup_ratio=0.03,
        max_grad_norm=1.0,
        lr_scheduler_type="cosine",
        logging_steps=10,
        save_steps=100,
        save_strategy="steps",
        save_total_limit=3,
        remove_unused_columns=False,
        bf16=True,
        dataloader_num_workers=4,
        gradient_checkpointing=False,  # 关闭梯度检查点
        report_to="none",
        fp16_full_eval=False,  # 新增配置
        optim="adamw_torch_fused"  # 新增配置
    )
    
    # 自定义数据整理器
    def data_collator(features):
        """自定义数据整理器，处理视频数据"""
        # 确保padding_side为left
        processor.tokenizer.padding_side = 'left'

        # 提取各个字段
        input_ids = [f['input_ids'] for f in features]
        attention_masks = [f['attention_mask'] for f in features]
        labels = [f['labels'] for f in features]
        pixel_values = [f['pixel_values'] for f in features]
        grid_thws = [f['grid_thw'] for f in features]

        # Pad input_ids和attention_mask
        max_length = max(len(ids) for ids in input_ids)
        padded_input_ids = []
        padded_attention_masks = []
        padded_labels = []

        for i in range(len(input_ids)):
            pad_length = max_length - len(input_ids[i])
            # 左填充
            padded_input_ids.append(torch.cat([
                torch.full((pad_length,), processor.tokenizer.pad_token_id),
                input_ids[i]
            ]))
            padded_attention_masks.append(torch.cat([
                torch.zeros(pad_length),
                attention_masks[i]
            ]))
            padded_labels.append(torch.cat([
                torch.full((pad_length,), -100),  # 忽略填充部分的loss
                labels[i]
            ]))

        # 处理视频数据
        max_frames = max(pv.shape[0] for pv in pixel_values)
        max_height = max(pv.shape[-2] for pv in pixel_values)
        max_width = max(pv.shape[-1] for pv in pixel_values)

        padded_pixel_values = []
        for pv in pixel_values:
            # 填充视频帧
            pad_frames = max_frames - pv.shape[0]
            if pad_frames > 0:
                padding = torch.zeros(pad_frames, pv.shape[1], pv.shape[2], pv.shape[3])
                pv = torch.cat([pv, padding], dim=0)
            padded_pixel_values.append(pv)

        return {
            'input_ids': torch.stack(padded_input_ids),
            'attention_mask': torch.stack(padded_attention_masks),
            'labels': torch.stack(padded_labels),
            'pixel_values': torch.stack(padded_pixel_values),
            'grid_thw': torch.stack(grid_thws)
        }
    
    # 创建训练器
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=dataset,
        data_collator=data_collator,
        processing_class=processor,
    )

    # 训练前最后一次确保padding_side设置正确
    trainer.processing_class.tokenizer.padding_side = 'left'
    logger.info("🔧 最终确认tokenizer padding_side设置为left")

    # 开始训练
    logger.info("🎯 开始LoRA训练...")
    logger.info(f"📊 训练配置:")
    logger.info(f"   样本数: {len(dataset)}")
    logger.info(f"   批次大小: {training_args.per_device_train_batch_size}")
    logger.info(f"   梯度累积: {training_args.gradient_accumulation_steps}")
    logger.info(f"   有效批次大小: {training_args.per_device_train_batch_size * training_args.gradient_accumulation_steps}")
    logger.info(f"   学习率: {training_args.learning_rate}")
    logger.info(f"   训练轮数: {training_args.num_train_epochs}")
    
    trainer.train()
    
    # 保存模型
    logger.info("💾 保存LoRA权重...")
    trainer.save_model()
    processor.save_pretrained(output_path)
    
    logger.info("🎉 LoRA微调完成!")
    logger.info(f"📁 模型保存在: {output_path}")
    
    # 列出保存的文件
    saved_files = os.listdir(output_path)
    logger.info(f"📋 保存的文件: {saved_files}")

if __name__ == "__main__":
    main()
