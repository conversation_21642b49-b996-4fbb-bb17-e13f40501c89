"""
简化的视频微调训练脚本 - 避免复杂的模板处理
"""

import os
import json
import logging
import torch
import numpy as np
from pathlib import Path
from datasets import Dataset
from transformers import (
    AutoProcessor,
    Qwen2_5_VLForConditionalGeneration,
    TrainingArguments,
    Trainer
)
from peft import LoraConfig, get_peft_model, TaskType
from decord import VideoReader
from PIL import Image

def setup_logger():
    """设置日志记录器"""
    logging.basicConfig(level=logging.INFO)
    return logging.getLogger(__name__)

def load_video_frames(video_path, max_frames=8):
    """加载视频帧"""
    try:
        vr = VideoReader(video_path, num_threads=4)
        total_frames = len(vr)
        
        # 均匀采样帧
        if total_frames <= max_frames:
            frame_indices = list(range(total_frames))
        else:
            frame_indices = np.linspace(0, total_frames - 1, max_frames, dtype=int)
        
        frames = vr.get_batch(frame_indices).asnumpy()
        # 转换为PIL图像
        pil_frames = [Image.fromarray(frame) for frame in frames]
        
        return pil_frames
    except Exception as e:
        print(f"Error loading video {video_path}: {e}")
        # 返回空白图像作为fallback
        blank_image = Image.new('RGB', (224, 224), color='black')
        return [blank_image] * max_frames

class SimpleVideoDataset(Dataset):
    """简化的视频数据集"""
    
    def __init__(self, data_list, processor, max_frames=8):
        self.data_list = data_list
        self.processor = processor
        self.max_frames = max_frames
        
    def __len__(self):
        return len(self.data_list)
    
    def __getitem__(self, idx):
        item = self.data_list[idx]
        conversations = item['conversations']
        video_path = item['video']
        
        # 加载视频帧
        video_frames = load_video_frames(video_path, self.max_frames)
        
        # 构建简单的文本
        user_text = conversations[0]['value'].replace('<video>\n', '').strip()
        assistant_text = conversations[1]['value'].strip()
        
        # 简单的文本格式
        full_text = f"Human: {user_text}\nAssistant: {assistant_text}"
        
        # 处理视频和文本
        self.processor.tokenizer.padding_side = 'left'
        
        # 使用processor处理视频帧和文本
        inputs = self.processor(
            images=video_frames,
            text=full_text,
            padding=False,
            truncation=True,
            max_length=2048,
            return_tensors="pt"
        )
        
        return {
            'input_ids': inputs['input_ids'].squeeze(),
            'attention_mask': inputs['attention_mask'].squeeze(),
            'labels': inputs['input_ids'].squeeze().clone(),
            'pixel_values': inputs['pixel_values'].squeeze() if 'pixel_values' in inputs else torch.zeros(self.max_frames, 3, 224, 224)
        }

def data_collator(features):
    """数据整理器"""
    # 提取各个字段
    input_ids = [f['input_ids'] for f in features]
    attention_masks = [f['attention_mask'] for f in features]
    labels = [f['labels'] for f in features]
    pixel_values = [f['pixel_values'] for f in features]
    
    # Pad序列
    max_length = max(len(ids) for ids in input_ids)
    padded_input_ids = []
    padded_attention_masks = []
    padded_labels = []
    
    for i in range(len(input_ids)):
        pad_length = max_length - len(input_ids[i])
        # 左填充
        padded_input_ids.append(torch.cat([
            torch.full((pad_length,), 151643),  # Qwen的pad_token_id
            input_ids[i]
        ]))
        padded_attention_masks.append(torch.cat([
            torch.zeros(pad_length),
            attention_masks[i]
        ]))
        padded_labels.append(torch.cat([
            torch.full((pad_length,), -100),
            labels[i]
        ]))
    
    # 处理视频数据
    max_frames = max(pv.shape[0] if pv.dim() > 3 else 1 for pv in pixel_values)
    padded_pixel_values = []
    
    for pv in pixel_values:
        if pv.dim() == 3:  # 单帧
            pv = pv.unsqueeze(0)
        
        # 填充到相同帧数
        if pv.shape[0] < max_frames:
            pad_frames = max_frames - pv.shape[0]
            padding = torch.zeros(pad_frames, pv.shape[1], pv.shape[2], pv.shape[3])
            pv = torch.cat([pv, padding], dim=0)
        
        padded_pixel_values.append(pv)
    
    return {
        'input_ids': torch.stack(padded_input_ids),
        'attention_mask': torch.stack(padded_attention_masks),
        'labels': torch.stack(padded_labels),
        'pixel_values': torch.stack(padded_pixel_values)
    }

def main():
    """主函数"""
    logger = setup_logger()
    
    logger.info("🚀 开始红外视频目标检测LoRA微调（简化版）...")
    
    # 配置路径
    model_path = "/home/<USER>/Qwen/Qwen2.5-VL/Qwen2.5-VL-7B-Instruct"
    data_path = "data/infrared_video_train.json"
    output_path = "output/lora_checkpoints_simple"
    
    # 创建输出目录
    Path(output_path).mkdir(parents=True, exist_ok=True)
    
    # 加载训练数据
    logger.info("📊 加载训练数据...")
    with open(data_path, 'r', encoding='utf-8') as f:
        train_data = json.load(f)
    
    logger.info(f"✅ 使用完整数据集: {len(train_data)} 个样本进行训练")
    
    # 加载processor
    logger.info("🔤 加载processor...")
    processor = AutoProcessor.from_pretrained(model_path, trust_remote_code=True)
    processor.tokenizer.padding_side = 'left'
    
    # 创建数据集
    logger.info("📝 创建视频数据集...")
    dataset = SimpleVideoDataset(train_data, processor, max_frames=8)
    
    # 加载模型
    logger.info("🤖 加载模型...")
    model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
        model_path,
        torch_dtype=torch.bfloat16,
        device_map={"": "cuda:0"},
        trust_remote_code=True,
        attn_implementation="flash_attention_2"
    )
    
    # 配置LoRA
    logger.info("⚙️ 配置LoRA...")
    lora_config = LoraConfig(
        task_type=TaskType.CAUSAL_LM,
        target_modules=["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"],
        inference_mode=False,
        r=64,
        lora_alpha=64,
        lora_dropout=0.05,
        bias="none",
    )
    
    model = get_peft_model(model, lora_config)
    model.print_trainable_parameters()
    
    # 训练参数
    training_args = TrainingArguments(
        output_dir=output_path,
        per_device_train_batch_size=1,  # 降低批次大小
        gradient_accumulation_steps=16,
        num_train_epochs=1,
        learning_rate=2e-5,
        weight_decay=0.01,
        warmup_ratio=0.03,
        max_grad_norm=1.0,
        lr_scheduler_type="cosine",
        logging_steps=5,
        save_steps=50,
        save_strategy="steps",
        save_total_limit=3,
        remove_unused_columns=False,
        bf16=True,
        dataloader_num_workers=2,
        gradient_checkpointing=False,
        report_to="none",
        fp16_full_eval=False,
        optim="adamw_torch_fused"
    )
    
    # 创建训练器
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=dataset,
        data_collator=data_collator,
        processing_class=processor,
    )
    
    # 开始训练
    logger.info("🎯 开始LoRA训练...")
    logger.info(f"📊 训练配置:")
    logger.info(f"   样本数: {len(dataset)}")
    logger.info(f"   批次大小: {training_args.per_device_train_batch_size}")
    logger.info(f"   梯度累积: {training_args.gradient_accumulation_steps}")
    logger.info(f"   有效批次大小: {training_args.per_device_train_batch_size * training_args.gradient_accumulation_steps}")
    logger.info(f"   学习率: {training_args.learning_rate}")
    logger.info(f"   训练轮数: {training_args.num_train_epochs}")
    
    trainer.train()
    
    # 保存模型
    logger.info("💾 保存LoRA权重...")
    trainer.save_model()
    processor.save_pretrained(output_path)
    
    logger.info("🎉 LoRA微调完成!")
    logger.info(f"📁 模型保存在: {output_path}")

if __name__ == "__main__":
    main()
