#!/usr/bin/env python3
"""
简化的训练脚本
直接调用LLaMA-Factory的训练功能
"""

import os
import sys
import logging

# 添加LLaMA-Factory路径
sys.path.insert(0, '/home/<USER>/Qwen/Qwen2.5-VL/LLaMA-Factory/src')

# 设置环境变量
os.environ['CUDA_VISIBLE_DEVICES'] = '0'

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    """主训练函数"""
    try:
        # 设置训练参数
        sys.argv = [
            'train.py',
            '--model_name_or_path', '/home/<USER>/Qwen/Qwen2.5-VL/Qwen2.5-VL-7B-Instruct',
            '--stage', 'sft',
            '--do_train',
            '--finetuning_type', 'lora',
            '--dataset', 'infrared_detection',
            '--template', 'qwen2_vl',
            '--cutoff_len', '8192',
            '--learning_rate', '5e-05',
            '--num_train_epochs', '3.0',
            '--per_device_train_batch_size', '1',
            '--gradient_accumulation_steps', '8',
            '--lr_scheduler_type', 'cosine',
            '--warmup_ratio', '0.1',
            '--bf16',
            '--logging_steps', '10',
            '--save_steps', '500',
            '--output_dir', '/home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/output/checkpoints',
            '--lora_rank', '64',
            '--lora_alpha', '16',
            '--lora_dropout', '0.05',
            '--lora_target', 'q_proj,k_proj,v_proj,o_proj,gate_proj,up_proj,down_proj',
            '--max_new_tokens', '2048',
            '--plot_loss',
            '--overwrite_output_dir'
        ]
        
        logger.info("开始导入LLaMA-Factory...")
        from llamafactory.train.tuner import run_exp
        
        logger.info("开始训练...")
        run_exp()
        
        logger.info("训练完成！")
        
    except Exception as e:
        logger.error(f"训练失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
