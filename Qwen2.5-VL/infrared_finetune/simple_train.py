"""
简化的LoRA微调脚本
"""
import os
import json
import torch
import logging
from pathlib import Path
from transformers import (
    Qwen2_5_VLForConditionalGeneration,
    AutoProcessor,
    TrainingArguments,
    Trainer
)
from peft import LoraConfig, TaskType, get_peft_model
from datasets import Dataset

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    """简化的训练主函数"""
    
    # 设置路径
    model_path = "/home/<USER>/Qwen/Qwen2.5-VL/Qwen2.5-VL-7B-Instruct"
    data_path = "/home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/data"
    output_path = "/home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/output/lora_checkpoints"
    
    # 设置GPU
    os.environ["CUDA_VISIBLE_DEVICES"] = "2"
    device = "cuda:0"
    
    logger.info("🚀 开始简化LoRA微调...")
    
    # 创建输出目录
    Path(output_path).mkdir(parents=True, exist_ok=True)
    
    # 加载数据
    logger.info("📊 加载训练数据...")
    with open(f"{data_path}/infrared_video_train.json", 'r') as f:
        train_data = json.load(f)
    
    # 只使用前10个样本进行快速测试
    train_data = train_data[:10]
    logger.info(f"✅ 加载了 {len(train_data)} 个训练样本")
    
    # 加载模型
    logger.info("🤖 加载基础模型...")
    model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
        model_path,
        torch_dtype=torch.bfloat16,
        device_map={"": device},
        trust_remote_code=True,
        attn_implementation="flash_attention_2"
    )
    
    # 加载处理器
    processor = AutoProcessor.from_pretrained(
        model_path,
        trust_remote_code=True
    )
    
    # 配置LoRA
    logger.info("⚙️ 配置LoRA...")
    lora_config = LoraConfig(
        task_type=TaskType.CAUSAL_LM,
        target_modules=["q_proj", "k_proj", "v_proj", "o_proj"],
        inference_mode=False,
        r=16,  # 降低秩以加快训练
        lora_alpha=8,
        lora_dropout=0.05,
        bias="none",
    )
    
    # 应用LoRA
    model = get_peft_model(model, lora_config)
    model.config.use_cache = False
    model.print_trainable_parameters()
    
    # 准备数据集
    logger.info("📝 准备数据集...")
    def preprocess_function(examples):
        # 简化的数据预处理
        texts = []
        for conv in examples['conversations']:
            text = f"User: {conv[0]['value']}\nAssistant: {conv[1]['value']}"
            texts.append(text)
        
        # 使用处理器进行tokenization
        model_inputs = processor.tokenizer(
            texts,
            truncation=True,
            padding=True,
            max_length=512,  # 限制长度以加快训练
            return_tensors="pt"
        )
        
        # 设置labels
        model_inputs["labels"] = model_inputs["input_ids"].clone()
        
        return model_inputs
    
    # 转换为Dataset
    dataset = Dataset.from_list(train_data)
    tokenized_dataset = dataset.map(
        preprocess_function,
        batched=True,
        remove_columns=dataset.column_names
    )
    
    # 训练参数
    training_args = TrainingArguments(
        output_dir=output_path,
        per_device_train_batch_size=1,
        gradient_accumulation_steps=4,
        num_train_epochs=1,
        learning_rate=1e-4,
        logging_steps=5,
        save_steps=50,
        save_strategy="steps",
        save_total_limit=2,
        remove_unused_columns=False,
        bf16=True,
        dataloader_num_workers=0,
        report_to="none",
    )
    
    # 创建训练器
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=tokenized_dataset,
        tokenizer=processor.tokenizer,
    )
    
    # 开始训练
    logger.info("🎯 开始LoRA训练...")
    trainer.train()
    
    # 保存模型
    logger.info("💾 保存LoRA权重...")
    trainer.save_model()
    processor.save_pretrained(output_path)
    
    logger.info("🎉 LoRA微调完成!")
    logger.info(f"📁 模型保存在: {output_path}")

if __name__ == "__main__":
    main()
