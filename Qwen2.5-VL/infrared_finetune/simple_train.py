#!/usr/bin/env python3
"""
简化的红外图像微调训练脚本
确保在Qwen环境下可以成功运行
"""

import os
import sys
import json
import torch
import logging
from pathlib import Path
from typing import List, Dict, Any
from dataclasses import dataclass

# 设置环境
os.environ["CUDA_VISIBLE_DEVICES"] = "0"
os.environ["TOKENIZERS_PARALLELISM"] = "false"

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_environment():
    """检查环境"""
    logger.info("🔍 检查环境...")
    
    # 检查CUDA
    if not torch.cuda.is_available():
        logger.error("❌ CUDA不可用")
        return False
    
    logger.info(f"✅ CUDA可用，GPU: {torch.cuda.get_device_name()}")
    
    # 检查关键包
    try:
        from transformers import Qwen2VLForConditionalGeneration, Qwen2VLProcessor
        from peft import LoraConfig, get_peft_model, TaskType
        logger.info("✅ 所有必需包导入成功")
        return True
    except ImportError as e:
        logger.error(f"❌ 包导入失败: {e}")
        return False

def load_data(data_path: str, max_samples: int = 10):
    """加载数据（限制样本数量用于测试）"""
    logger.info(f"📂 加载数据: {data_path}")
    
    with open(data_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 限制样本数量
    data = data[:max_samples]
    logger.info(f"✅ 加载了 {len(data)} 个样本")
    
    return data

def setup_model():
    """设置模型"""
    logger.info("🤖 设置模型...")
    
    model_path = "/home/<USER>/Qwen/Qwen2.5-VL/Qwen2.5-VL-7B-Instruct"
    
    try:
        # 加载处理器
        from transformers import Qwen2VLProcessor
        processor = Qwen2VLProcessor.from_pretrained(model_path)
        logger.info("✅ 处理器加载成功")
        
        # 加载模型
        from transformers import Qwen2VLForConditionalGeneration
        model = Qwen2VLForConditionalGeneration.from_pretrained(
            model_path,
            torch_dtype=torch.float16,
            device_map="auto",
            low_cpu_mem_usage=True
        )
        logger.info("✅ 模型加载成功")
        
        # 配置LoRA
        from peft import LoraConfig, get_peft_model, TaskType
        lora_config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            r=32,  # 较小的rank
            lora_alpha=16,
            lora_dropout=0.1,
            target_modules=["q_proj", "k_proj", "v_proj", "o_proj"],
            bias="none"
        )
        
        model = get_peft_model(model, lora_config)
        model.print_trainable_parameters()
        logger.info("✅ LoRA配置成功")
        
        return model, processor
        
    except Exception as e:
        logger.error(f"❌ 模型设置失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None

def simple_training():
    """简单训练流程"""
    logger.info("🚀 开始简单训练流程...")
    
    # 检查环境
    if not check_environment():
        return False
    
    # 检查数据
    data_path = "/home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/data/infrared_detection_train.json"
    if not os.path.exists(data_path):
        logger.error(f"❌ 数据文件不存在: {data_path}")
        return False
    
    # 加载数据
    data = load_data(data_path, max_samples=5)  # 只用5个样本测试
    
    # 设置模型
    model, processor = setup_model()
    if model is None:
        return False
    
    # 简单的训练循环（不使用Trainer）
    logger.info("🎯 开始简单训练...")
    
    model.train()
    optimizer = torch.optim.AdamW(model.parameters(), lr=1e-5)
    
    for epoch in range(1):  # 只训练1个epoch
        logger.info(f"📚 Epoch {epoch + 1}")
        
        for i, item in enumerate(data[:3]):  # 只用前3个样本
            try:
                logger.info(f"  处理样本 {i + 1}")
                
                # 简单的文本处理（跳过视频）
                user_text = item['messages'][0]['content']
                assistant_text = item['messages'][1]['content']
                
                # 构建简单的文本输入
                text = f"用户: {user_text}\n助手: {assistant_text}"
                
                # 处理输入
                inputs = processor(
                    text=[text],
                    return_tensors="pt",
                    padding=True,
                    truncation=True,
                    max_length=512
                )
                
                # 移动到GPU
                inputs = {k: v.to(model.device) for k, v in inputs.items()}
                
                # 前向传播
                outputs = model(**inputs, labels=inputs["input_ids"])
                loss = outputs.loss
                
                logger.info(f"    损失: {loss.item():.4f}")
                
                # 反向传播
                optimizer.zero_grad()
                loss.backward()
                optimizer.step()
                
            except Exception as e:
                logger.warning(f"  样本 {i + 1} 处理失败: {e}")
                continue
    
    # 保存模型
    output_dir = "/home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/output/simple_checkpoints"
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        model.save_pretrained(output_dir)
        processor.save_pretrained(output_dir)
        logger.info(f"✅ 模型保存成功: {output_dir}")
        return True
    except Exception as e:
        logger.error(f"❌ 模型保存失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("=" * 60)
    logger.info("🎯 红外图像微调 - 简化训练")
    logger.info("=" * 60)
    
    success = simple_training()
    
    if success:
        logger.info("🎉 训练成功完成！")
        logger.info("📁 检查点保存在: /home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/output/simple_checkpoints")
    else:
        logger.error("💥 训练失败！")
    
    logger.info("=" * 60)

if __name__ == "__main__":
    main()
