#!/bin/bash

# 红外视频目标检测完整流程运行脚本

echo "🚀 开始红外视频目标检测完整流程..."

# 激活conda环境
source /home/<USER>/miniconda3/etc/profile.d/conda.sh
conda activate Qwen

echo "✅ 环境激活成功"

# 1. 数据预处理
echo "📊 步骤1: 数据预处理..."
python data_processor.py
if [ $? -eq 0 ]; then
    echo "✅ 数据预处理完成"
else
    echo "❌ 数据预处理失败"
    exit 1
fi

# 2. LoRA微调训练
echo "🎯 步骤2: LoRA微调训练..."
python train_lora.py
if [ $? -eq 0 ]; then
    echo "✅ LoRA微调训练完成"
else
    echo "❌ LoRA微调训练失败"
    exit 1
fi

# 3. 模型检测测试（单个视频）
echo "🔍 步骤3: 模型检测测试..."
python detect.py
if [ $? -eq 0 ]; then
    echo "✅ 模型检测测试完成"
else
    echo "❌ 模型检测测试失败"
    exit 1
fi

# 4. 批量检测完整测试集（全部300个样本）
echo "📊 步骤4: 批量检测完整测试集..."
python batch_detect.py
if [ $? -eq 0 ]; then
    echo "✅ 批量检测完成"
else
    echo "❌ 批量检测失败"
    exit 1
fi

# 5. 性能评估（基于完整测试集）
echo "📈 步骤5: 性能评估..."
python evaluate.py --real
if [ $? -eq 0 ]; then
    echo "✅ 性能评估完成"
else
    echo "❌ 性能评估失败"
    exit 1
fi

echo "🎉 完整流程执行完成！"
echo "📁 LoRA权重保存在: output/lora_checkpoints/"
echo "📊 检测结果保存在: output/detection_results.json"
echo "📈 评估结果基于完整测试集(300个样本)的性能"
echo ""
echo "📋 数据文件说明:"
echo "   - infrared_video_train.json: 训练集(210个样本)"
echo "   - infrared_video_val.json: 验证集(90个样本)"
echo "   - infrared_video_test_full.json: 完整测试集(300个样本)"
