# 🚀 快速开始指南

## 问题解决

如果您遇到以下错误：
```
av.error.FileNotFoundError: [Errno 2] No such file or directory: 'videos/data01_seq_040.mp4'
```

这是视频路径问题，请按照以下步骤解决：

## 解决方案

### 方法1: 快速修复（推荐）

```bash
cd /home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune
python3 quick_fix.py
```

### 方法2: 完整修复

```bash
cd /home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune
python3 fix_video_paths.py
```

### 方法3: 手动修复

```bash
python3 -c "
import json
import os

print('开始修复视频路径...')

train_file = '/home/<USER>/Qwen/Qwen2.5-VL/LLaMA-Factory/data/infrared_detection_train.json'
video_base_path = '/home/<USER>/Qwen/Qwen2.5-VL/LLaMA-Factory/data/videos'

with open(train_file, 'r', encoding='utf-8') as f:
    train_data = json.load(f)

fixed_count = 0
for item in train_data:
    if 'videos' in item:
        new_videos = []
        for video_path in item['videos']:
            if video_path.startswith('videos/'):
                video_name = video_path.replace('videos/', '')
                absolute_path = os.path.join(video_base_path, video_name)
                new_videos.append(absolute_path)
                fixed_count += 1
            else:
                new_videos.append(video_path)
        item['videos'] = new_videos

with open(train_file, 'w', encoding='utf-8') as f:
    json.dump(train_data, f, ensure_ascii=False, indent=2)

print(f'路径修复完成，修复了 {fixed_count} 个路径')

# 验证修复结果
sample_video = train_data[0]['videos'][0]
exists = os.path.exists(sample_video)
print(f'示例视频路径: {sample_video}')
print(f'文件存在: {exists}')
"
```

## 开始训练

路径修复完成后，运行训练：

```bash
cd /home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune
./train.sh
```

## 预期输出

修复成功后，您应该看到类似以下输出：
```
🔧 开始修复视频路径...
✅ 路径修复完成，修复了 244 个路径
📁 示例视频路径: /home/<USER>/Qwen/Qwen2.5-VL/LLaMA-Factory/data/videos/data01_seq_000.mp4
✅ 文件存在: True

🚀 现在可以运行训练了:
   ./train.sh
```

## 训练完成后

训练完成后，您可以在以下位置找到模型文件：
- 检查点: `/home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/output/checkpoints/`
- LoRA适配器: `adapter_model.safetensors`
- 配置文件: `adapter_config.json`

## 评估模型

```bash
python3 evaluate_model.py --lora-path ./output/checkpoints
```
