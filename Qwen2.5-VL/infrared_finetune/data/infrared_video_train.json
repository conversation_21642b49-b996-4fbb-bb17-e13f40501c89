[{"video": "videos/data01_seq_053.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"253\":[{\"bbox\":[66,111,71,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"258\":[{\"bbox\":[74,111,79,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"263\":[{\"bbox\":[71,112,76,117],\"class_name\":\"drone\",\"confidence\":1.0}],\"268\":[{\"bbox\":[68,112,73,117],\"class_name\":\"drone\",\"confidence\":1.0}],\"273\":[{\"bbox\":[74,111,79,116],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data01_seq_020.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"100\":[{\"bbox\":[68,110,73,115],\"class_name\":\"drone\",\"confidence\":1.0}],\"105\":[{\"bbox\":[65,110,70,115],\"class_name\":\"drone\",\"confidence\":1.0}],\"110\":[{\"bbox\":[61,109,66,114],\"class_name\":\"drone\",\"confidence\":1.0}],\"115\":[{\"bbox\":[68,109,73,114],\"class_name\":\"drone\",\"confidence\":1.0}],\"120\":[{\"bbox\":[65,109,70,114],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data01_seq_043.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"203\":[{\"bbox\":[67,111,72,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"208\":[{\"bbox\":[74,111,79,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"213\":[{\"bbox\":[74,111,79,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"218\":[{\"bbox\":[71,111,76,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"223\":[{\"bbox\":[68,111,73,116],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data01_seq_004.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"4\":[{\"bbox\":[67,106,72,111],\"class_name\":\"drone\",\"confidence\":1.0}],\"9\":[{\"bbox\":[63,106,68,111],\"class_name\":\"drone\",\"confidence\":1.0}],\"14\":[{\"bbox\":[68,106,73,111],\"class_name\":\"drone\",\"confidence\":1.0}],\"19\":[{\"bbox\":[68,106,73,111],\"class_name\":\"drone\",\"confidence\":1.0}],\"24\":[{\"bbox\":[64,106,69,111],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data01_seq_057.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"277\":[{\"bbox\":[74,112,79,117],\"class_name\":\"drone\",\"confidence\":1.0}],\"282\":[{\"bbox\":[71,111,76,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"287\":[{\"bbox\":[71,112,76,117],\"class_name\":\"drone\",\"confidence\":1.0}],\"292\":[{\"bbox\":[74,112,79,117],\"class_name\":\"drone\",\"confidence\":1.0}],\"297\":[{\"bbox\":[73,112,78,117],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data01_seq_008.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"28\":[{\"bbox\":[61,107,66,112],\"class_name\":\"drone\",\"confidence\":1.0}],\"33\":[{\"bbox\":[69,106,74,111],\"class_name\":\"drone\",\"confidence\":1.0}],\"38\":[{\"bbox\":[65,106,70,111],\"class_name\":\"drone\",\"confidence\":1.0}],\"43\":[{\"bbox\":[60,105,65,110],\"class_name\":\"drone\",\"confidence\":1.0}],\"48\":[{\"bbox\":[57,106,62,111],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data01_seq_013.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"53\":[{\"bbox\":[56,106,61,111],\"class_name\":\"drone\",\"confidence\":1.0}],\"58\":[{\"bbox\":[59,107,64,112],\"class_name\":\"drone\",\"confidence\":1.0}],\"63\":[{\"bbox\":[62,107,67,112],\"class_name\":\"drone\",\"confidence\":1.0}],\"68\":[{\"bbox\":[68,108,73,113],\"class_name\":\"drone\",\"confidence\":1.0}],\"73\":[{\"bbox\":[65,107,70,112],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data01_seq_051.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"251\":[{\"bbox\":[68,111,73,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"256\":[{\"bbox\":[71,111,76,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"261\":[{\"bbox\":[73,112,78,117],\"class_name\":\"drone\",\"confidence\":1.0}],\"266\":[{\"bbox\":[69,112,74,117],\"class_name\":\"drone\",\"confidence\":1.0}],\"271\":[{\"bbox\":[69,112,74,117],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data01_seq_039.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"179\":[{\"bbox\":[71,111,76,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"184\":[{\"bbox\":[68,111,73,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"189\":[{\"bbox\":[65,111,70,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"194\":[{\"bbox\":[62,111,67,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"199\":[{\"bbox\":[67,111,72,116],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data01_seq_006.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"26\":[{\"bbox\":[62,106,67,111],\"class_name\":\"drone\",\"confidence\":1.0}],\"31\":[{\"bbox\":[70,106,75,111],\"class_name\":\"drone\",\"confidence\":1.0}],\"36\":[{\"bbox\":[67,106,72,111],\"class_name\":\"drone\",\"confidence\":1.0}],\"41\":[{\"bbox\":[62,106,67,111],\"class_name\":\"drone\",\"confidence\":1.0}],\"46\":[{\"bbox\":[66,106,71,111],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data01_seq_033.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"153\":[{\"bbox\":[66,110,71,115],\"class_name\":\"drone\",\"confidence\":1.0}],\"158\":[{\"bbox\":[63,110,68,115],\"class_name\":\"drone\",\"confidence\":1.0}],\"163\":[{\"bbox\":[72,110,77,115],\"class_name\":\"drone\",\"confidence\":1.0}],\"168\":[{\"bbox\":[68,111,73,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"173\":[{\"bbox\":[65,111,70,116],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data01_seq_015.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"75\":[{\"bbox\":[62,108,67,113],\"class_name\":\"drone\",\"confidence\":1.0}],\"80\":[{\"bbox\":[64,109,69,114],\"class_name\":\"drone\",\"confidence\":1.0}],\"85\":[{\"bbox\":[65,109,70,114],\"class_name\":\"drone\",\"confidence\":1.0}],\"90\":[{\"bbox\":[62,109,67,114],\"class_name\":\"drone\",\"confidence\":1.0}],\"95\":[{\"bbox\":[64,109,69,114],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data01_seq_027.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"127\":[{\"bbox\":[65,110,70,115],\"class_name\":\"drone\",\"confidence\":1.0}],\"132\":[{\"bbox\":[71,110,76,115],\"class_name\":\"drone\",\"confidence\":1.0}],\"137\":[{\"bbox\":[66,110,71,115],\"class_name\":\"drone\",\"confidence\":1.0}],\"142\":[{\"bbox\":[64,109,69,114],\"class_name\":\"drone\",\"confidence\":1.0}],\"147\":[{\"bbox\":[68,109,73,114],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data01_seq_056.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"276\":[{\"bbox\":[74,111,79,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"281\":[{\"bbox\":[71,111,76,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"286\":[{\"bbox\":[68,111,73,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"291\":[{\"bbox\":[74,112,79,117],\"class_name\":\"drone\",\"confidence\":1.0}],\"296\":[{\"bbox\":[73,112,78,117],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data01_seq_001.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"1\":[{\"bbox\":[68,106,73,111],\"class_name\":\"drone\",\"confidence\":1.0}],\"6\":[{\"bbox\":[65,106,70,111],\"class_name\":\"drone\",\"confidence\":1.0}],\"11\":[{\"bbox\":[62,106,67,111],\"class_name\":\"drone\",\"confidence\":1.0}],\"16\":[{\"bbox\":[69,106,74,111],\"class_name\":\"drone\",\"confidence\":1.0}],\"21\":[{\"bbox\":[65,106,70,111],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data01_seq_007.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"27\":[{\"bbox\":[61,106,66,111],\"class_name\":\"drone\",\"confidence\":1.0}],\"32\":[{\"bbox\":[70,106,75,111],\"class_name\":\"drone\",\"confidence\":1.0}],\"37\":[{\"bbox\":[66,106,71,111],\"class_name\":\"drone\",\"confidence\":1.0}],\"42\":[{\"bbox\":[62,106,67,111],\"class_name\":\"drone\",\"confidence\":1.0}],\"47\":[{\"bbox\":[68,106,73,111],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data01_seq_017.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"77\":[{\"bbox\":[61,108,66,113],\"class_name\":\"drone\",\"confidence\":1.0}],\"82\":[{\"bbox\":[66,109,71,114],\"class_name\":\"drone\",\"confidence\":1.0}],\"87\":[{\"bbox\":[64,109,69,114],\"class_name\":\"drone\",\"confidence\":1.0}],\"92\":[{\"bbox\":[62,109,67,114],\"class_name\":\"drone\",\"confidence\":1.0}],\"97\":[{\"bbox\":[69,110,74,115],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data01_seq_022.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"102\":[{\"bbox\":[66,110,71,115],\"class_name\":\"drone\",\"confidence\":1.0}],\"107\":[{\"bbox\":[62,110,67,115],\"class_name\":\"drone\",\"confidence\":1.0}],\"112\":[{\"bbox\":[66,109,71,114],\"class_name\":\"drone\",\"confidence\":1.0}],\"117\":[{\"bbox\":[66,109,71,114],\"class_name\":\"drone\",\"confidence\":1.0}],\"122\":[{\"bbox\":[65,109,70,114],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data01_seq_003.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"3\":[{\"bbox\":[66,106,71,111],\"class_name\":\"drone\",\"confidence\":1.0}],\"8\":[{\"bbox\":[64,106,69,111],\"class_name\":\"drone\",\"confidence\":1.0}],\"13\":[{\"bbox\":[64,106,69,111],\"class_name\":\"drone\",\"confidence\":1.0}],\"18\":[{\"bbox\":[68,106,73,111],\"class_name\":\"drone\",\"confidence\":1.0}],\"23\":[{\"bbox\":[65,106,70,111],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data01_seq_040.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"200\":[{\"bbox\":[68,111,73,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"205\":[{\"bbox\":[65,111,70,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"210\":[{\"bbox\":[76,111,81,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"215\":[{\"bbox\":[73,111,78,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"220\":[{\"bbox\":[68,111,73,116],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data01_seq_035.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"175\":[{\"bbox\":[66,111,71,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"180\":[{\"bbox\":[70,111,75,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"185\":[{\"bbox\":[68,111,73,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"190\":[{\"bbox\":[65,111,70,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"195\":[{\"bbox\":[62,111,67,116],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data01_seq_002.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"2\":[{\"bbox\":[67,106,72,111],\"class_name\":\"drone\",\"confidence\":1.0}],\"7\":[{\"bbox\":[65,107,70,112],\"class_name\":\"drone\",\"confidence\":1.0}],\"12\":[{\"bbox\":[62,106,67,111],\"class_name\":\"drone\",\"confidence\":1.0}],\"17\":[{\"bbox\":[68,106,73,111],\"class_name\":\"drone\",\"confidence\":1.0}],\"22\":[{\"bbox\":[65,106,70,111],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data01_seq_024.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"104\":[{\"bbox\":[65,110,70,115],\"class_name\":\"drone\",\"confidence\":1.0}],\"109\":[{\"bbox\":[61,109,66,114],\"class_name\":\"drone\",\"confidence\":1.0}],\"114\":[{\"bbox\":[68,109,73,114],\"class_name\":\"drone\",\"confidence\":1.0}],\"119\":[{\"bbox\":[65,108,70,113],\"class_name\":\"drone\",\"confidence\":1.0}],\"124\":[{\"bbox\":[63,109,68,114],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data01_seq_000.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"0\":[{\"bbox\":[68,106,73,111],\"class_name\":\"drone\",\"confidence\":1.0}],\"5\":[{\"bbox\":[66,106,71,111],\"class_name\":\"drone\",\"confidence\":1.0}],\"10\":[{\"bbox\":[62,106,67,111],\"class_name\":\"drone\",\"confidence\":1.0}],\"15\":[{\"bbox\":[70,106,75,111],\"class_name\":\"drone\",\"confidence\":1.0}],\"20\":[{\"bbox\":[67,106,72,111],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data01_seq_041.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"201\":[{\"bbox\":[67,111,72,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"206\":[{\"bbox\":[67,111,72,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"211\":[{\"bbox\":[75,111,80,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"216\":[{\"bbox\":[71,111,76,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"221\":[{\"bbox\":[68,111,73,116],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data01_seq_054.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"254\":[{\"bbox\":[66,111,71,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"259\":[{\"bbox\":[74,111,79,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"264\":[{\"bbox\":[71,112,76,117],\"class_name\":\"drone\",\"confidence\":1.0}],\"269\":[{\"bbox\":[68,112,73,117],\"class_name\":\"drone\",\"confidence\":1.0}],\"274\":[{\"bbox\":[74,111,79,116],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data01_seq_029.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"129\":[{\"bbox\":[71,110,76,115],\"class_name\":\"drone\",\"confidence\":1.0}],\"134\":[{\"bbox\":[69,110,74,115],\"class_name\":\"drone\",\"confidence\":1.0}],\"139\":[{\"bbox\":[65,109,70,114],\"class_name\":\"drone\",\"confidence\":1.0}],\"144\":[{\"bbox\":[67,109,72,114],\"class_name\":\"drone\",\"confidence\":1.0}],\"149\":[{\"bbox\":[68,110,73,115],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data01_seq_019.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"79\":[{\"bbox\":[62,108,67,113],\"class_name\":\"drone\",\"confidence\":1.0}],\"84\":[{\"bbox\":[65,109,70,114],\"class_name\":\"drone\",\"confidence\":1.0}],\"89\":[{\"bbox\":[64,109,69,114],\"class_name\":\"drone\",\"confidence\":1.0}],\"94\":[{\"bbox\":[61,109,66,114],\"class_name\":\"drone\",\"confidence\":1.0}],\"99\":[{\"bbox\":[69,110,74,115],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data01_seq_034.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"154\":[{\"bbox\":[65,110,70,115],\"class_name\":\"drone\",\"confidence\":1.0}],\"159\":[{\"bbox\":[66,110,71,115],\"class_name\":\"drone\",\"confidence\":1.0}],\"164\":[{\"bbox\":[71,110,76,115],\"class_name\":\"drone\",\"confidence\":1.0}],\"169\":[{\"bbox\":[68,111,73,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"174\":[{\"bbox\":[65,111,70,116],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data01_seq_036.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"176\":[{\"bbox\":[68,111,73,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"181\":[{\"bbox\":[69,111,74,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"186\":[{\"bbox\":[66,111,71,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"191\":[{\"bbox\":[66,111,71,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"196\":[{\"bbox\":[62,111,67,116],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data01_seq_046.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"226\":[{\"bbox\":[71,111,76,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"231\":[{\"bbox\":[70,111,75,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"236\":[{\"bbox\":[66,111,71,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"241\":[{\"bbox\":[71,111,76,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"246\":[{\"bbox\":[71,111,76,116],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data01_seq_047.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"227\":[{\"bbox\":[71,111,76,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"232\":[{\"bbox\":[70,111,75,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"237\":[{\"bbox\":[66,111,71,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"242\":[{\"bbox\":[72,111,77,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"247\":[{\"bbox\":[71,111,76,116],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data01_seq_016.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"76\":[{\"bbox\":[62,108,67,113],\"class_name\":\"drone\",\"confidence\":1.0}],\"81\":[{\"bbox\":[66,109,71,114],\"class_name\":\"drone\",\"confidence\":1.0}],\"86\":[{\"bbox\":[65,108,70,113],\"class_name\":\"drone\",\"confidence\":1.0}],\"91\":[{\"bbox\":[62,109,67,114],\"class_name\":\"drone\",\"confidence\":1.0}],\"96\":[{\"bbox\":[68,109,73,114],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data01_seq_037.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"177\":[{\"bbox\":[70,111,75,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"182\":[{\"bbox\":[69,111,74,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"187\":[{\"bbox\":[65,110,70,115],\"class_name\":\"drone\",\"confidence\":1.0}],\"192\":[{\"bbox\":[63,111,68,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"197\":[{\"bbox\":[62,111,67,116],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data01_seq_011.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"51\":[{\"bbox\":[55,106,60,111],\"class_name\":\"drone\",\"confidence\":1.0}],\"56\":[{\"bbox\":[59,106,64,111],\"class_name\":\"drone\",\"confidence\":1.0}],\"61\":[{\"bbox\":[57,106,62,111],\"class_name\":\"drone\",\"confidence\":1.0}],\"66\":[{\"bbox\":[71,107,76,112],\"class_name\":\"drone\",\"confidence\":1.0}],\"71\":[{\"bbox\":[66,107,71,112],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data01_seq_028.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"128\":[{\"bbox\":[69,110,74,115],\"class_name\":\"drone\",\"confidence\":1.0}],\"133\":[{\"bbox\":[70,110,75,115],\"class_name\":\"drone\",\"confidence\":1.0}],\"138\":[{\"bbox\":[65,110,70,115],\"class_name\":\"drone\",\"confidence\":1.0}],\"143\":[{\"bbox\":[65,109,70,114],\"class_name\":\"drone\",\"confidence\":1.0}],\"148\":[{\"bbox\":[68,110,73,115],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data01_seq_010.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"50\":[{\"bbox\":[55,107,60,112],\"class_name\":\"drone\",\"confidence\":1.0}],\"55\":[{\"bbox\":[59,106,64,111],\"class_name\":\"drone\",\"confidence\":1.0}],\"60\":[{\"bbox\":[59,106,64,111],\"class_name\":\"drone\",\"confidence\":1.0}],\"65\":[{\"bbox\":[70,107,75,112],\"class_name\":\"drone\",\"confidence\":1.0}],\"70\":[{\"bbox\":[67,107,72,112],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data01_seq_031.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"151\":[{\"bbox\":[66,110,71,115],\"class_name\":\"drone\",\"confidence\":1.0}],\"156\":[{\"bbox\":[63,110,68,115],\"class_name\":\"drone\",\"confidence\":1.0}],\"161\":[{\"bbox\":[73,110,78,115],\"class_name\":\"drone\",\"confidence\":1.0}],\"166\":[{\"bbox\":[69,111,74,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"171\":[{\"bbox\":[66,111,71,116],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data01_seq_021.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"101\":[{\"bbox\":[67,110,72,115],\"class_name\":\"drone\",\"confidence\":1.0}],\"106\":[{\"bbox\":[64,110,69,115],\"class_name\":\"drone\",\"confidence\":1.0}],\"111\":[{\"bbox\":[63,109,68,114],\"class_name\":\"drone\",\"confidence\":1.0}],\"116\":[{\"bbox\":[67,109,72,114],\"class_name\":\"drone\",\"confidence\":1.0}],\"121\":[{\"bbox\":[65,109,70,114],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data01_seq_044.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"204\":[{\"bbox\":[66,111,71,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"209\":[{\"bbox\":[77,111,82,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"214\":[{\"bbox\":[74,110,79,115],\"class_name\":\"drone\",\"confidence\":1.0}],\"219\":[{\"bbox\":[70,111,75,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"224\":[{\"bbox\":[70,111,75,116],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data01_seq_023.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"103\":[{\"bbox\":[66,110,71,115],\"class_name\":\"drone\",\"confidence\":1.0}],\"108\":[{\"bbox\":[62,109,67,114],\"class_name\":\"drone\",\"confidence\":1.0}],\"113\":[{\"bbox\":[68,109,73,114],\"class_name\":\"drone\",\"confidence\":1.0}],\"118\":[{\"bbox\":[65,109,70,114],\"class_name\":\"drone\",\"confidence\":1.0}],\"123\":[{\"bbox\":[63,109,68,114],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data01_seq_014.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"54\":[{\"bbox\":[58,106,63,111],\"class_name\":\"drone\",\"confidence\":1.0}],\"59\":[{\"bbox\":[59,106,64,111],\"class_name\":\"drone\",\"confidence\":1.0}],\"64\":[{\"bbox\":[68,107,73,112],\"class_name\":\"drone\",\"confidence\":1.0}],\"69\":[{\"bbox\":[68,108,73,113],\"class_name\":\"drone\",\"confidence\":1.0}],\"74\":[{\"bbox\":[63,107,68,112],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data01_seq_048.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"228\":[{\"bbox\":[71,111,76,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"233\":[{\"bbox\":[68,111,73,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"238\":[{\"bbox\":[66,111,71,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"243\":[{\"bbox\":[71,111,76,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"248\":[{\"bbox\":[70,111,75,116],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data01_seq_009.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"29\":[{\"bbox\":[64,106,69,111],\"class_name\":\"drone\",\"confidence\":1.0}],\"34\":[{\"bbox\":[68,106,73,111],\"class_name\":\"drone\",\"confidence\":1.0}],\"39\":[{\"bbox\":[65,106,70,111],\"class_name\":\"drone\",\"confidence\":1.0}],\"44\":[{\"bbox\":[60,106,65,111],\"class_name\":\"drone\",\"confidence\":1.0}],\"49\":[{\"bbox\":[56,106,61,111],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data01_seq_055.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"275\":[{\"bbox\":[74,111,79,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"280\":[{\"bbox\":[71,111,76,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"285\":[{\"bbox\":[68,112,73,117],\"class_name\":\"drone\",\"confidence\":1.0}],\"290\":[{\"bbox\":[75,112,80,117],\"class_name\":\"drone\",\"confidence\":1.0}],\"295\":[{\"bbox\":[74,112,79,117],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data01_seq_052.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"252\":[{\"bbox\":[67,111,72,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"257\":[{\"bbox\":[73,111,78,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"262\":[{\"bbox\":[72,112,77,117],\"class_name\":\"drone\",\"confidence\":1.0}],\"267\":[{\"bbox\":[69,112,74,117],\"class_name\":\"drone\",\"confidence\":1.0}],\"272\":[{\"bbox\":[71,112,76,117],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data01_seq_038.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"178\":[{\"bbox\":[71,111,76,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"183\":[{\"bbox\":[68,111,73,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"188\":[{\"bbox\":[65,111,70,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"193\":[{\"bbox\":[62,111,67,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"198\":[{\"bbox\":[65,111,70,116],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data01_seq_045.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"225\":[{\"bbox\":[71,111,76,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"230\":[{\"bbox\":[70,111,75,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"235\":[{\"bbox\":[68,111,73,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"240\":[{\"bbox\":[71,111,76,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"245\":[{\"bbox\":[71,111,76,116],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data02_seq_016.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"76\":[{\"bbox\":[98,88,103,93],\"class_name\":\"drone\",\"confidence\":1.0}],\"81\":[{\"bbox\":[99,89,104,94],\"class_name\":\"drone\",\"confidence\":1.0}],\"86\":[{\"bbox\":[99,94,104,99],\"class_name\":\"drone\",\"confidence\":1.0}],\"91\":[{\"bbox\":[99,92,104,97],\"class_name\":\"drone\",\"confidence\":1.0}],\"96\":[{\"bbox\":[98,91,103,96],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data02_seq_006.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"26\":[{\"bbox\":[96,89,101,94],\"class_name\":\"drone\",\"confidence\":1.0}],\"31\":[{\"bbox\":[97,89,102,94],\"class_name\":\"drone\",\"confidence\":1.0}],\"36\":[{\"bbox\":[97,88,102,93],\"class_name\":\"drone\",\"confidence\":1.0}],\"41\":[{\"bbox\":[96,90,101,95],\"class_name\":\"drone\",\"confidence\":1.0}],\"46\":[{\"bbox\":[98,91,103,96],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data02_seq_025.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"125\":[{\"bbox\":[95,92,100,97],\"class_name\":\"drone\",\"confidence\":1.0}],\"130\":[{\"bbox\":[95,92,100,97],\"class_name\":\"drone\",\"confidence\":1.0}],\"135\":[{\"bbox\":[95,91,100,96],\"class_name\":\"drone\",\"confidence\":1.0}],\"140\":[{\"bbox\":[94,90,99,95],\"class_name\":\"drone\",\"confidence\":1.0}],\"145\":[{\"bbox\":[94,89,99,94],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data02_seq_027.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"127\":[{\"bbox\":[94,91,99,96],\"class_name\":\"drone\",\"confidence\":1.0}],\"132\":[{\"bbox\":[95,92,100,97],\"class_name\":\"drone\",\"confidence\":1.0}],\"137\":[{\"bbox\":[95,91,100,96],\"class_name\":\"drone\",\"confidence\":1.0}],\"142\":[{\"bbox\":[94,90,99,95],\"class_name\":\"drone\",\"confidence\":1.0}],\"147\":[{\"bbox\":[95,94,100,99],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data02_seq_003.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"3\":[{\"bbox\":[97,87,102,92],\"class_name\":\"drone\",\"confidence\":1.0}],\"8\":[{\"bbox\":[96,87,101,92],\"class_name\":\"drone\",\"confidence\":1.0}],\"13\":[{\"bbox\":[96,90,101,95],\"class_name\":\"drone\",\"confidence\":1.0}],\"18\":[{\"bbox\":[96,89,101,94],\"class_name\":\"drone\",\"confidence\":1.0}],\"23\":[{\"bbox\":[96,88,101,93],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data02_seq_000.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"0\":[{\"bbox\":[97,88,102,93],\"class_name\":\"drone\",\"confidence\":1.0}],\"5\":[{\"bbox\":[97,87,102,92],\"class_name\":\"drone\",\"confidence\":1.0}],\"10\":[{\"bbox\":[96,90,101,95],\"class_name\":\"drone\",\"confidence\":1.0}],\"15\":[{\"bbox\":[96,90,101,95],\"class_name\":\"drone\",\"confidence\":1.0}],\"20\":[{\"bbox\":[96,89,101,94],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data02_seq_010.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"50\":[{\"bbox\":[98,89,103,94],\"class_name\":\"drone\",\"confidence\":1.0}],\"55\":[{\"bbox\":[97,89,102,94],\"class_name\":\"drone\",\"confidence\":1.0}],\"60\":[{\"bbox\":[99,91,104,96],\"class_name\":\"drone\",\"confidence\":1.0}],\"65\":[{\"bbox\":[99,90,104,95],\"class_name\":\"drone\",\"confidence\":1.0}],\"70\":[{\"bbox\":[99,89,104,94],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data02_seq_005.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"25\":[{\"bbox\":[96,89,101,94],\"class_name\":\"drone\",\"confidence\":1.0}],\"30\":[{\"bbox\":[97,89,102,94],\"class_name\":\"drone\",\"confidence\":1.0}],\"35\":[{\"bbox\":[96,89,101,94],\"class_name\":\"drone\",\"confidence\":1.0}],\"40\":[{\"bbox\":[96,88,101,93],\"class_name\":\"drone\",\"confidence\":1.0}],\"45\":[{\"bbox\":[98,91,103,96],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data02_seq_026.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"126\":[{\"bbox\":[95,92,100,97],\"class_name\":\"drone\",\"confidence\":1.0}],\"131\":[{\"bbox\":[95,92,100,97],\"class_name\":\"drone\",\"confidence\":1.0}],\"136\":[{\"bbox\":[94,91,99,96],\"class_name\":\"drone\",\"confidence\":1.0}],\"141\":[{\"bbox\":[94,90,99,95],\"class_name\":\"drone\",\"confidence\":1.0}],\"146\":[{\"bbox\":[95,92,100,97],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data02_seq_020.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"100\":[{\"bbox\":[98,92,103,97],\"class_name\":\"drone\",\"confidence\":1.0}],\"105\":[{\"bbox\":[96,92,101,97],\"class_name\":\"drone\",\"confidence\":1.0}],\"110\":[{\"bbox\":[96,91,101,96],\"class_name\":\"drone\",\"confidence\":1.0}],\"115\":[{\"bbox\":[96,94,101,99],\"class_name\":\"drone\",\"confidence\":1.0}],\"120\":[{\"bbox\":[96,93,101,98],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data02_seq_011.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"51\":[{\"bbox\":[97,89,102,94],\"class_name\":\"drone\",\"confidence\":1.0}],\"56\":[{\"bbox\":[97,89,102,94],\"class_name\":\"drone\",\"confidence\":1.0}],\"61\":[{\"bbox\":[99,91,104,96],\"class_name\":\"drone\",\"confidence\":1.0}],\"66\":[{\"bbox\":[99,90,104,95],\"class_name\":\"drone\",\"confidence\":1.0}],\"71\":[{\"bbox\":[99,89,104,94],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data02_seq_022.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"102\":[{\"bbox\":[96,93,101,98],\"class_name\":\"drone\",\"confidence\":1.0}],\"107\":[{\"bbox\":[97,91,102,96],\"class_name\":\"drone\",\"confidence\":1.0}],\"112\":[{\"bbox\":[96,91,101,96],\"class_name\":\"drone\",\"confidence\":1.0}],\"117\":[{\"bbox\":[96,93,101,98],\"class_name\":\"drone\",\"confidence\":1.0}],\"122\":[{\"bbox\":[95,93,100,98],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data02_seq_028.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"128\":[{\"bbox\":[95,91,100,96],\"class_name\":\"drone\",\"confidence\":1.0}],\"133\":[{\"bbox\":[94,92,99,97],\"class_name\":\"drone\",\"confidence\":1.0}],\"138\":[{\"bbox\":[94,90,99,95],\"class_name\":\"drone\",\"confidence\":1.0}],\"143\":[{\"bbox\":[94,89,99,94],\"class_name\":\"drone\",\"confidence\":1.0}],\"148\":[{\"bbox\":[94,93,99,98],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data02_seq_012.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"52\":[{\"bbox\":[97,89,102,94],\"class_name\":\"drone\",\"confidence\":1.0}],\"57\":[{\"bbox\":[98,89,103,94],\"class_name\":\"drone\",\"confidence\":1.0}],\"62\":[{\"bbox\":[99,91,104,96],\"class_name\":\"drone\",\"confidence\":1.0}],\"67\":[{\"bbox\":[99,90,104,95],\"class_name\":\"drone\",\"confidence\":1.0}],\"72\":[{\"bbox\":[96,88,101,93],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data02_seq_017.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"77\":[{\"bbox\":[99,88,104,93],\"class_name\":\"drone\",\"confidence\":1.0}],\"82\":[{\"bbox\":[99,91,104,96],\"class_name\":\"drone\",\"confidence\":1.0}],\"87\":[{\"bbox\":[99,94,104,99],\"class_name\":\"drone\",\"confidence\":1.0}],\"92\":[{\"bbox\":[98,92,103,97],\"class_name\":\"drone\",\"confidence\":1.0}],\"97\":[{\"bbox\":[97,91,102,96],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data02_seq_019.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"79\":[{\"bbox\":[99,88,104,93],\"class_name\":\"drone\",\"confidence\":1.0}],\"84\":[{\"bbox\":[99,95,104,100],\"class_name\":\"drone\",\"confidence\":1.0}],\"89\":[{\"bbox\":[99,93,104,98],\"class_name\":\"drone\",\"confidence\":1.0}],\"94\":[{\"bbox\":[98,92,103,97],\"class_name\":\"drone\",\"confidence\":1.0}],\"99\":[{\"bbox\":[97,92,102,97],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data02_seq_024.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"104\":[{\"bbox\":[96,92,101,97],\"class_name\":\"drone\",\"confidence\":1.0}],\"109\":[{\"bbox\":[96,91,101,96],\"class_name\":\"drone\",\"confidence\":1.0}],\"114\":[{\"bbox\":[96,92,101,97],\"class_name\":\"drone\",\"confidence\":1.0}],\"119\":[{\"bbox\":[95,93,100,98],\"class_name\":\"drone\",\"confidence\":1.0}],\"124\":[{\"bbox\":[95,92,100,97],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data02_seq_002.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"2\":[{\"bbox\":[97,87,102,92],\"class_name\":\"drone\",\"confidence\":1.0}],\"7\":[{\"bbox\":[96,86,101,91],\"class_name\":\"drone\",\"confidence\":1.0}],\"12\":[{\"bbox\":[96,90,101,95],\"class_name\":\"drone\",\"confidence\":1.0}],\"17\":[{\"bbox\":[96,90,101,95],\"class_name\":\"drone\",\"confidence\":1.0}],\"22\":[{\"bbox\":[96,88,101,93],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data02_seq_007.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"27\":[{\"bbox\":[96,89,101,94],\"class_name\":\"drone\",\"confidence\":1.0}],\"32\":[{\"bbox\":[97,89,102,94],\"class_name\":\"drone\",\"confidence\":1.0}],\"37\":[{\"bbox\":[96,88,101,93],\"class_name\":\"drone\",\"confidence\":1.0}],\"42\":[{\"bbox\":[97,91,102,96],\"class_name\":\"drone\",\"confidence\":1.0}],\"47\":[{\"bbox\":[97,90,102,95],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data02_seq_001.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"1\":[{\"bbox\":[97,88,102,93],\"class_name\":\"drone\",\"confidence\":1.0}],\"6\":[{\"bbox\":[97,86,102,91],\"class_name\":\"drone\",\"confidence\":1.0}],\"11\":[{\"bbox\":[96,91,101,96],\"class_name\":\"drone\",\"confidence\":1.0}],\"16\":[{\"bbox\":[96,89,101,94],\"class_name\":\"drone\",\"confidence\":1.0}],\"21\":[{\"bbox\":[96,88,101,93],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data02_seq_023.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"103\":[{\"bbox\":[97,92,102,97],\"class_name\":\"drone\",\"confidence\":1.0}],\"108\":[{\"bbox\":[97,91,102,96],\"class_name\":\"drone\",\"confidence\":1.0}],\"113\":[{\"bbox\":[96,91,101,96],\"class_name\":\"drone\",\"confidence\":1.0}],\"118\":[{\"bbox\":[96,94,101,99],\"class_name\":\"drone\",\"confidence\":1.0}],\"123\":[{\"bbox\":[96,92,101,97],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data02_seq_015.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"75\":[{\"bbox\":[97,87,102,92],\"class_name\":\"drone\",\"confidence\":1.0}],\"80\":[{\"bbox\":[99,88,104,93],\"class_name\":\"drone\",\"confidence\":1.0}],\"85\":[{\"bbox\":[99,94,104,99],\"class_name\":\"drone\",\"confidence\":1.0}],\"90\":[{\"bbox\":[99,92,104,97],\"class_name\":\"drone\",\"confidence\":1.0}],\"95\":[{\"bbox\":[98,91,103,96],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data02_seq_029.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"129\":[{\"bbox\":[95,92,100,97],\"class_name\":\"drone\",\"confidence\":1.0}],\"134\":[{\"bbox\":[95,91,100,96],\"class_name\":\"drone\",\"confidence\":1.0}],\"139\":[{\"bbox\":[95,90,100,95],\"class_name\":\"drone\",\"confidence\":1.0}],\"144\":[{\"bbox\":[94,89,99,94],\"class_name\":\"drone\",\"confidence\":1.0}],\"149\":[{\"bbox\":[95,94,100,99],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data02_seq_009.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"29\":[{\"bbox\":[97,89,102,94],\"class_name\":\"drone\",\"confidence\":1.0}],\"34\":[{\"bbox\":[96,89,101,94],\"class_name\":\"drone\",\"confidence\":1.0}],\"39\":[{\"bbox\":[96,88,101,93],\"class_name\":\"drone\",\"confidence\":1.0}],\"44\":[{\"bbox\":[98,91,103,96],\"class_name\":\"drone\",\"confidence\":1.0}],\"49\":[{\"bbox\":[98,90,103,95],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data04_seq_011.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"51\":[{\"bbox\":[101,84,106,89],\"class_name\":\"drone\",\"confidence\":1.0}],\"56\":[{\"bbox\":[103,82,108,87],\"class_name\":\"drone\",\"confidence\":1.0}],\"61\":[{\"bbox\":[103,80,108,85],\"class_name\":\"drone\",\"confidence\":1.0}],\"66\":[{\"bbox\":[103,83,108,88],\"class_name\":\"drone\",\"confidence\":1.0}],\"71\":[{\"bbox\":[102,84,107,89],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data04_seq_005.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"25\":[{\"bbox\":[100,82,105,87],\"class_name\":\"drone\",\"confidence\":1.0}],\"30\":[{\"bbox\":[100,80,105,85],\"class_name\":\"drone\",\"confidence\":1.0}],\"35\":[{\"bbox\":[103,84,108,89],\"class_name\":\"drone\",\"confidence\":1.0}],\"40\":[{\"bbox\":[104,83,109,88],\"class_name\":\"drone\",\"confidence\":1.0}],\"45\":[{\"bbox\":[103,80,108,85],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data04_seq_007.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"27\":[{\"bbox\":[100,81,105,86],\"class_name\":\"drone\",\"confidence\":1.0}],\"32\":[{\"bbox\":[100,79,105,84],\"class_name\":\"drone\",\"confidence\":1.0}],\"37\":[{\"bbox\":[104,84,109,89],\"class_name\":\"drone\",\"confidence\":1.0}],\"42\":[{\"bbox\":[103,81,108,86],\"class_name\":\"drone\",\"confidence\":1.0}],\"47\":[{\"bbox\":[102,79,107,84],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data04_seq_003.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"3\":[{\"bbox\":[102,82,107,87],\"class_name\":\"drone\",\"confidence\":1.0}],\"8\":[{\"bbox\":[104,81,109,86],\"class_name\":\"drone\",\"confidence\":1.0}],\"13\":[{\"bbox\":[103,78,108,83],\"class_name\":\"drone\",\"confidence\":1.0}],\"18\":[{\"bbox\":[101,81,106,86],\"class_name\":\"drone\",\"confidence\":1.0}],\"23\":[{\"bbox\":[101,83,106,88],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data04_seq_010.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"50\":[{\"bbox\":[101,83,106,88],\"class_name\":\"drone\",\"confidence\":1.0}],\"55\":[{\"bbox\":[103,82,108,87],\"class_name\":\"drone\",\"confidence\":1.0}],\"60\":[{\"bbox\":[103,80,108,85],\"class_name\":\"drone\",\"confidence\":1.0}],\"65\":[{\"bbox\":[103,79,108,84],\"class_name\":\"drone\",\"confidence\":1.0}],\"70\":[{\"bbox\":[102,84,107,89],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data04_seq_009.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"29\":[{\"bbox\":[100,80,105,85],\"class_name\":\"drone\",\"confidence\":1.0}],\"34\":[{\"bbox\":[102,82,107,87],\"class_name\":\"drone\",\"confidence\":1.0}],\"39\":[{\"bbox\":[104,83,109,88],\"class_name\":\"drone\",\"confidence\":1.0}],\"44\":[{\"bbox\":[103,80,108,85],\"class_name\":\"drone\",\"confidence\":1.0}],\"49\":[{\"bbox\":[102,79,107,84],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data04_seq_000.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"0\":[{\"bbox\":[97,78,102,83],\"class_name\":\"drone\",\"confidence\":1.0}],\"5\":[{\"bbox\":[104,82,109,87],\"class_name\":\"drone\",\"confidence\":1.0}],\"10\":[{\"bbox\":[104,79,109,84],\"class_name\":\"drone\",\"confidence\":1.0}],\"15\":[{\"bbox\":[102,78,107,83],\"class_name\":\"drone\",\"confidence\":1.0}],\"20\":[{\"bbox\":[101,85,106,90],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data04_seq_002.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"2\":[{\"bbox\":[100,80,105,85],\"class_name\":\"drone\",\"confidence\":1.0}],\"7\":[{\"bbox\":[104,81,109,86],\"class_name\":\"drone\",\"confidence\":1.0}],\"12\":[{\"bbox\":[103,79,108,84],\"class_name\":\"drone\",\"confidence\":1.0}],\"17\":[{\"bbox\":[102,78,107,83],\"class_name\":\"drone\",\"confidence\":1.0}],\"22\":[{\"bbox\":[101,84,106,89],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data04_seq_004.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"4\":[{\"bbox\":[104,82,109,87],\"class_name\":\"drone\",\"confidence\":1.0}],\"9\":[{\"bbox\":[104,80,109,85],\"class_name\":\"drone\",\"confidence\":1.0}],\"14\":[{\"bbox\":[102,77,107,82],\"class_name\":\"drone\",\"confidence\":1.0}],\"19\":[{\"bbox\":[101,85,106,90],\"class_name\":\"drone\",\"confidence\":1.0}],\"24\":[{\"bbox\":[101,83,106,88],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data04_seq_008.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"28\":[{\"bbox\":[100,81,105,86],\"class_name\":\"drone\",\"confidence\":1.0}],\"33\":[{\"bbox\":[101,79,106,84],\"class_name\":\"drone\",\"confidence\":1.0}],\"38\":[{\"bbox\":[104,83,109,88],\"class_name\":\"drone\",\"confidence\":1.0}],\"43\":[{\"bbox\":[103,81,108,86],\"class_name\":\"drone\",\"confidence\":1.0}],\"48\":[{\"bbox\":[102,79,107,84],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data04_seq_012.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"52\":[{\"bbox\":[102,84,107,89],\"class_name\":\"drone\",\"confidence\":1.0}],\"57\":[{\"bbox\":[103,82,108,87],\"class_name\":\"drone\",\"confidence\":1.0}],\"62\":[{\"bbox\":[103,79,108,84],\"class_name\":\"drone\",\"confidence\":1.0}],\"67\":[{\"bbox\":[103,85,108,90],\"class_name\":\"drone\",\"confidence\":1.0}],\"72\":[{\"bbox\":[102,83,107,88],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data04_seq_001.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"1\":[{\"bbox\":[98,78,103,83],\"class_name\":\"drone\",\"confidence\":1.0}],\"6\":[{\"bbox\":[104,81,109,86],\"class_name\":\"drone\",\"confidence\":1.0}],\"11\":[{\"bbox\":[103,79,108,84],\"class_name\":\"drone\",\"confidence\":1.0}],\"16\":[{\"bbox\":[102,77,107,82],\"class_name\":\"drone\",\"confidence\":1.0}],\"21\":[{\"bbox\":[101,84,106,89],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data05_seq_012.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"52\":[{\"bbox\":[159,20,194,55],\"class_name\":\"drone\",\"confidence\":1.0}],\"57\":[{\"bbox\":[157,13,192,48],\"class_name\":\"drone\",\"confidence\":1.0}],\"62\":[{\"bbox\":[138,34,173,69],\"class_name\":\"drone\",\"confidence\":1.0}],\"67\":[{\"bbox\":[135,24,170,59],\"class_name\":\"drone\",\"confidence\":1.0}],\"72\":[{\"bbox\":[132,16,167,51],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data05_seq_008.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"28\":[{\"bbox\":[178,53,213,88],\"class_name\":\"drone\",\"confidence\":1.0}],\"33\":[{\"bbox\":[172,44,207,79],\"class_name\":\"drone\",\"confidence\":1.0}],\"38\":[{\"bbox\":[171,34,206,69],\"class_name\":\"drone\",\"confidence\":1.0}],\"43\":[{\"bbox\":[169,27,204,62],\"class_name\":\"drone\",\"confidence\":1.0}],\"48\":[{\"bbox\":[161,27,196,62],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data05_seq_004.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"4\":[{\"bbox\":[199,98,234,133],\"class_name\":\"drone\",\"confidence\":1.0}],\"9\":[{\"bbox\":[203,89,238,124],\"class_name\":\"drone\",\"confidence\":1.0}],\"14\":[{\"bbox\":[179,79,214,114],\"class_name\":\"drone\",\"confidence\":1.0}],\"19\":[{\"bbox\":[179,70,214,105],\"class_name\":\"drone\",\"confidence\":1.0}],\"24\":[{\"bbox\":[181,61,216,96],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data05_seq_014.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"54\":[{\"bbox\":[158,17,193,52],\"class_name\":\"drone\",\"confidence\":1.0}],\"59\":[{\"bbox\":[155,14,190,49],\"class_name\":\"drone\",\"confidence\":1.0}],\"64\":[{\"bbox\":[136,29,171,64],\"class_name\":\"drone\",\"confidence\":1.0}],\"69\":[{\"bbox\":[134,21,169,56],\"class_name\":\"drone\",\"confidence\":1.0}],\"74\":[{\"bbox\":[131,13,166,48],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data05_seq_010.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"50\":[{\"bbox\":[160,24,195,59],\"class_name\":\"drone\",\"confidence\":1.0}],\"55\":[{\"bbox\":[157,17,192,52],\"class_name\":\"drone\",\"confidence\":1.0}],\"60\":[{\"bbox\":[149,27,184,62],\"class_name\":\"drone\",\"confidence\":1.0}],\"65\":[{\"bbox\":[135,28,170,63],\"class_name\":\"drone\",\"confidence\":1.0}],\"70\":[{\"bbox\":[133,19,168,54],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data05_seq_001.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"1\":[{\"bbox\":[197,104,232,139],\"class_name\":\"drone\",\"confidence\":1.0}],\"6\":[{\"bbox\":[201,95,236,130],\"class_name\":\"drone\",\"confidence\":1.0}],\"11\":[{\"bbox\":[203,85,238,120],\"class_name\":\"drone\",\"confidence\":1.0}],\"16\":[{\"bbox\":[179,75,214,110],\"class_name\":\"drone\",\"confidence\":1.0}],\"21\":[{\"bbox\":[180,66,215,101],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data05_seq_013.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"53\":[{\"bbox\":[159,19,194,54],\"class_name\":\"drone\",\"confidence\":1.0}],\"58\":[{\"bbox\":[157,11,192,46],\"class_name\":\"drone\",\"confidence\":1.0}],\"63\":[{\"bbox\":[137,31,172,66],\"class_name\":\"drone\",\"confidence\":1.0}],\"68\":[{\"bbox\":[134,22,169,57],\"class_name\":\"drone\",\"confidence\":1.0}],\"73\":[{\"bbox\":[132,15,167,50],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data05_seq_000.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"0\":[{\"bbox\":[197,106,232,141],\"class_name\":\"drone\",\"confidence\":1.0}],\"5\":[{\"bbox\":[200,96,235,131],\"class_name\":\"drone\",\"confidence\":1.0}],\"10\":[{\"bbox\":[204,86,239,121],\"class_name\":\"drone\",\"confidence\":1.0}],\"15\":[{\"bbox\":[178,77,213,112],\"class_name\":\"drone\",\"confidence\":1.0}],\"20\":[{\"bbox\":[179,68,214,103],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data05_seq_003.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"3\":[{\"bbox\":[199,100,234,135],\"class_name\":\"drone\",\"confidence\":1.0}],\"8\":[{\"bbox\":[203,91,238,126],\"class_name\":\"drone\",\"confidence\":1.0}],\"13\":[{\"bbox\":[185,81,220,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"18\":[{\"bbox\":[179,71,214,106],\"class_name\":\"drone\",\"confidence\":1.0}],\"23\":[{\"bbox\":[181,62,216,97],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data05_seq_009.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"29\":[{\"bbox\":[174,51,209,86],\"class_name\":\"drone\",\"confidence\":1.0}],\"34\":[{\"bbox\":[172,42,207,77],\"class_name\":\"drone\",\"confidence\":1.0}],\"39\":[{\"bbox\":[170,33,205,68],\"class_name\":\"drone\",\"confidence\":1.0}],\"44\":[{\"bbox\":[166,30,201,65],\"class_name\":\"drone\",\"confidence\":1.0}],\"49\":[{\"bbox\":[161,26,196,61],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data05_seq_007.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"27\":[{\"bbox\":[182,55,217,90],\"class_name\":\"drone\",\"confidence\":1.0}],\"32\":[{\"bbox\":[172,46,207,81],\"class_name\":\"drone\",\"confidence\":1.0}],\"37\":[{\"bbox\":[171,37,206,72],\"class_name\":\"drone\",\"confidence\":1.0}],\"42\":[{\"bbox\":[171,28,206,63],\"class_name\":\"drone\",\"confidence\":1.0}],\"47\":[{\"bbox\":[161,28,196,63],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data05_seq_002.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"2\":[{\"bbox\":[198,102,233,137],\"class_name\":\"drone\",\"confidence\":1.0}],\"7\":[{\"bbox\":[202,93,237,128],\"class_name\":\"drone\",\"confidence\":1.0}],\"12\":[{\"bbox\":[194,83,229,118],\"class_name\":\"drone\",\"confidence\":1.0}],\"17\":[{\"bbox\":[179,73,214,108],\"class_name\":\"drone\",\"confidence\":1.0}],\"22\":[{\"bbox\":[182,65,217,100],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data06_seq_014.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"54\":[{\"bbox\":[93,122,98,127],\"class_name\":\"drone\",\"confidence\":1.0}],\"59\":[{\"bbox\":[18,161,23,166],\"class_name\":\"drone\",\"confidence\":1.0}],\"64\":[{\"bbox\":[49,134,54,139],\"class_name\":\"drone\",\"confidence\":1.0}],\"69\":[{\"bbox\":[96,120,101,125],\"class_name\":\"drone\",\"confidence\":1.0}],\"74\":[{\"bbox\":[95,121,100,126],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data06_seq_012.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"52\":[{\"bbox\":[94,121,99,126],\"class_name\":\"drone\",\"confidence\":1.0}],\"57\":[{\"bbox\":[92,120,97,125],\"class_name\":\"drone\",\"confidence\":1.0}],\"62\":[{\"bbox\":[19,162,24,167],\"class_name\":\"drone\",\"confidence\":1.0}],\"67\":[{\"bbox\":[96,119,101,124],\"class_name\":\"drone\",\"confidence\":1.0}],\"72\":[{\"bbox\":[95,121,100,126],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data06_seq_006.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"26\":[{\"bbox\":[91,122,96,127],\"class_name\":\"drone\",\"confidence\":1.0}],\"31\":[{\"bbox\":[91,123,96,128],\"class_name\":\"drone\",\"confidence\":1.0}],\"36\":[{\"bbox\":[90,124,95,129],\"class_name\":\"drone\",\"confidence\":1.0}],\"41\":[{\"bbox\":[91,120,96,125],\"class_name\":\"drone\",\"confidence\":1.0}],\"46\":[{\"bbox\":[93,120,98,125],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data06_seq_013.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"53\":[{\"bbox\":[93,122,98,127],\"class_name\":\"drone\",\"confidence\":1.0}],\"58\":[{\"bbox\":[92,120,97,125],\"class_name\":\"drone\",\"confidence\":1.0}],\"63\":[{\"bbox\":[24,155,29,160],\"class_name\":\"drone\",\"confidence\":1.0}],\"68\":[{\"bbox\":[95,120,100,125],\"class_name\":\"drone\",\"confidence\":1.0}],\"73\":[{\"bbox\":[95,121,100,126],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data06_seq_007.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"27\":[{\"bbox\":[91,122,96,127],\"class_name\":\"drone\",\"confidence\":1.0}],\"32\":[{\"bbox\":[91,123,96,128],\"class_name\":\"drone\",\"confidence\":1.0}],\"37\":[{\"bbox\":[90,124,95,129],\"class_name\":\"drone\",\"confidence\":1.0}],\"42\":[{\"bbox\":[92,119,97,124],\"class_name\":\"drone\",\"confidence\":1.0}],\"47\":[{\"bbox\":[93,120,98,125],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data06_seq_011.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"51\":[{\"bbox\":[94,121,99,126],\"class_name\":\"drone\",\"confidence\":1.0}],\"56\":[{\"bbox\":[92,121,97,126],\"class_name\":\"drone\",\"confidence\":1.0}],\"61\":[{\"bbox\":[19,162,24,167],\"class_name\":\"drone\",\"confidence\":1.0}],\"66\":[{\"bbox\":[94,116,99,121],\"class_name\":\"drone\",\"confidence\":1.0}],\"71\":[{\"bbox\":[96,120,101,125],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data06_seq_002.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"2\":[{\"bbox\":[92,123,97,128],\"class_name\":\"drone\",\"confidence\":1.0}],\"7\":[{\"bbox\":[90,124,95,129],\"class_name\":\"drone\",\"confidence\":1.0}],\"12\":[{\"bbox\":[90,122,95,127],\"class_name\":\"drone\",\"confidence\":1.0}],\"17\":[{\"bbox\":[89,123,94,128],\"class_name\":\"drone\",\"confidence\":1.0}],\"22\":[{\"bbox\":[90,124,95,129],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data06_seq_008.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"28\":[{\"bbox\":[90,123,95,128],\"class_name\":\"drone\",\"confidence\":1.0}],\"33\":[{\"bbox\":[91,123,96,128],\"class_name\":\"drone\",\"confidence\":1.0}],\"38\":[{\"bbox\":[89,124,94,129],\"class_name\":\"drone\",\"confidence\":1.0}],\"43\":[{\"bbox\":[93,119,98,124],\"class_name\":\"drone\",\"confidence\":1.0}],\"48\":[{\"bbox\":[93,121,98,126],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data06_seq_004.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"4\":[{\"bbox\":[91,124,96,129],\"class_name\":\"drone\",\"confidence\":1.0}],\"9\":[{\"bbox\":[90,122,95,127],\"class_name\":\"drone\",\"confidence\":1.0}],\"14\":[{\"bbox\":[90,123,95,128],\"class_name\":\"drone\",\"confidence\":1.0}],\"19\":[{\"bbox\":[89,123,94,128],\"class_name\":\"drone\",\"confidence\":1.0}],\"24\":[{\"bbox\":[90,123,95,128],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data06_seq_000.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"0\":[{\"bbox\":[92,123,97,128],\"class_name\":\"drone\",\"confidence\":1.0}],\"5\":[{\"bbox\":[90,124,95,129],\"class_name\":\"drone\",\"confidence\":1.0}],\"10\":[{\"bbox\":[90,122,95,127],\"class_name\":\"drone\",\"confidence\":1.0}],\"15\":[{\"bbox\":[89,123,94,128],\"class_name\":\"drone\",\"confidence\":1.0}],\"20\":[{\"bbox\":[90,124,95,129],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data06_seq_005.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"25\":[{\"bbox\":[91,122,96,127],\"class_name\":\"drone\",\"confidence\":1.0}],\"30\":[{\"bbox\":[91,123,96,128],\"class_name\":\"drone\",\"confidence\":1.0}],\"35\":[{\"bbox\":[90,124,95,129],\"class_name\":\"drone\",\"confidence\":1.0}],\"40\":[{\"bbox\":[90,121,95,126],\"class_name\":\"drone\",\"confidence\":1.0}],\"45\":[{\"bbox\":[93,120,98,125],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data06_seq_001.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"1\":[{\"bbox\":[92,123,97,128],\"class_name\":\"drone\",\"confidence\":1.0}],\"6\":[{\"bbox\":[90,124,95,129],\"class_name\":\"drone\",\"confidence\":1.0}],\"11\":[{\"bbox\":[90,122,95,127],\"class_name\":\"drone\",\"confidence\":1.0}],\"16\":[{\"bbox\":[89,123,94,128],\"class_name\":\"drone\",\"confidence\":1.0}],\"21\":[{\"bbox\":[90,124,95,129],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data07_seq_008.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"28\":[{\"bbox\":[137,84,168,114],\"class_name\":\"drone\",\"confidence\":1.0}],\"33\":[{\"bbox\":[135,73,165,104],\"class_name\":\"drone\",\"confidence\":1.0}],\"38\":[{\"bbox\":[130,64,160,95],\"class_name\":\"drone\",\"confidence\":1.0}],\"43\":[{\"bbox\":[125,55,156,86],\"class_name\":\"drone\",\"confidence\":1.0}],\"48\":[{\"bbox\":[120,47,151,77],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data07_seq_013.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"53\":[{\"bbox\":[116,37,146,68],\"class_name\":\"drone\",\"confidence\":1.0}],\"58\":[{\"bbox\":[109,28,140,59],\"class_name\":\"drone\",\"confidence\":1.0}],\"63\":[{\"bbox\":[105,20,136,50],\"class_name\":\"drone\",\"confidence\":1.0}],\"68\":[{\"bbox\":[100,9,130,40],\"class_name\":\"drone\",\"confidence\":1.0}],\"73\":[{\"bbox\":[98,28,128,58],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data07_seq_007.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"27\":[{\"bbox\":[138,85,168,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"32\":[{\"bbox\":[135,76,165,106],\"class_name\":\"drone\",\"confidence\":1.0}],\"37\":[{\"bbox\":[132,66,162,96],\"class_name\":\"drone\",\"confidence\":1.0}],\"42\":[{\"bbox\":[127,57,157,88],\"class_name\":\"drone\",\"confidence\":1.0}],\"47\":[{\"bbox\":[122,48,152,79],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data07_seq_002.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"2\":[{\"bbox\":[162,133,192,164],\"class_name\":\"drone\",\"confidence\":1.0}],\"7\":[{\"bbox\":[159,124,189,154],\"class_name\":\"drone\",\"confidence\":1.0}],\"12\":[{\"bbox\":[148,114,178,144],\"class_name\":\"drone\",\"confidence\":1.0}],\"17\":[{\"bbox\":[144,104,175,135],\"class_name\":\"drone\",\"confidence\":1.0}],\"22\":[{\"bbox\":[141,95,172,125],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data07_seq_005.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"25\":[{\"bbox\":[140,89,170,120],\"class_name\":\"drone\",\"confidence\":1.0}],\"30\":[{\"bbox\":[136,80,167,110],\"class_name\":\"drone\",\"confidence\":1.0}],\"35\":[{\"bbox\":[132,71,163,101],\"class_name\":\"drone\",\"confidence\":1.0}],\"40\":[{\"bbox\":[128,60,159,91],\"class_name\":\"drone\",\"confidence\":1.0}],\"45\":[{\"bbox\":[124,51,155,82],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data07_seq_001.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"1\":[{\"bbox\":[162,136,192,166],\"class_name\":\"drone\",\"confidence\":1.0}],\"6\":[{\"bbox\":[160,127,190,157],\"class_name\":\"drone\",\"confidence\":1.0}],\"11\":[{\"bbox\":[148,116,178,147],\"class_name\":\"drone\",\"confidence\":1.0}],\"16\":[{\"bbox\":[144,106,175,136],\"class_name\":\"drone\",\"confidence\":1.0}],\"21\":[{\"bbox\":[143,96,173,127],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data07_seq_009.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"29\":[{\"bbox\":[137,81,168,112],\"class_name\":\"drone\",\"confidence\":1.0}],\"34\":[{\"bbox\":[133,72,164,102],\"class_name\":\"drone\",\"confidence\":1.0}],\"39\":[{\"bbox\":[129,63,160,93],\"class_name\":\"drone\",\"confidence\":1.0}],\"44\":[{\"bbox\":[124,53,155,84],\"class_name\":\"drone\",\"confidence\":1.0}],\"49\":[{\"bbox\":[120,44,150,75],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data07_seq_004.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"4\":[{\"bbox\":[160,129,191,160],\"class_name\":\"drone\",\"confidence\":1.0}],\"9\":[{\"bbox\":[151,120,181,151],\"class_name\":\"drone\",\"confidence\":1.0}],\"14\":[{\"bbox\":[146,111,176,141],\"class_name\":\"drone\",\"confidence\":1.0}],\"19\":[{\"bbox\":[144,100,174,131],\"class_name\":\"drone\",\"confidence\":1.0}],\"24\":[{\"bbox\":[140,90,171,120],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data07_seq_006.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"26\":[{\"bbox\":[140,88,170,118],\"class_name\":\"drone\",\"confidence\":1.0}],\"31\":[{\"bbox\":[136,77,166,108],\"class_name\":\"drone\",\"confidence\":1.0}],\"36\":[{\"bbox\":[132,68,162,99],\"class_name\":\"drone\",\"confidence\":1.0}],\"41\":[{\"bbox\":[128,58,158,88],\"class_name\":\"drone\",\"confidence\":1.0}],\"46\":[{\"bbox\":[122,49,152,80],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data07_seq_012.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"52\":[{\"bbox\":[116,40,147,70],\"class_name\":\"drone\",\"confidence\":1.0}],\"57\":[{\"bbox\":[112,31,142,61],\"class_name\":\"drone\",\"confidence\":1.0}],\"62\":[{\"bbox\":[105,20,136,51],\"class_name\":\"drone\",\"confidence\":1.0}],\"67\":[{\"bbox\":[100,11,131,42],\"class_name\":\"drone\",\"confidence\":1.0}],\"72\":[{\"bbox\":[100,29,130,60],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data07_seq_010.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"50\":[{\"bbox\":[119,42,149,72],\"class_name\":\"drone\",\"confidence\":1.0}],\"55\":[{\"bbox\":[113,34,144,64],\"class_name\":\"drone\",\"confidence\":1.0}],\"60\":[{\"bbox\":[108,24,138,55],\"class_name\":\"drone\",\"confidence\":1.0}],\"65\":[{\"bbox\":[103,15,133,46],\"class_name\":\"drone\",\"confidence\":1.0}],\"70\":[{\"bbox\":[100,24,130,55],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data07_seq_014.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"54\":[{\"bbox\":[114,36,144,66],\"class_name\":\"drone\",\"confidence\":1.0}],\"59\":[{\"bbox\":[108,26,139,56],\"class_name\":\"drone\",\"confidence\":1.0}],\"64\":[{\"bbox\":[104,16,134,47],\"class_name\":\"drone\",\"confidence\":1.0}],\"69\":[{\"bbox\":[98,12,128,43],\"class_name\":\"drone\",\"confidence\":1.0}],\"74\":[{\"bbox\":[98,25,128,56],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data23_seq_015.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_new2_train_000076\":[{\"bbox\":[348,240,355,247],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000081\":[{\"bbox\":[348,240,356,248],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000086\":[{\"bbox\":[348,242,356,248],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000091\":[{\"bbox\":[351,242,357,249],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000096\":[{\"bbox\":[348,241,355,248],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data23_seq_008.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_new2_train_000029\":[{\"bbox\":[348,245,355,252],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000034\":[{\"bbox\":[348,246,356,253],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000039\":[{\"bbox\":[348,245,355,252],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000044\":[{\"bbox\":[348,245,355,252],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000049\":[{\"bbox\":[348,244,356,251],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data23_seq_010.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_new2_train_000051\":[{\"bbox\":[348,243,356,250],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000056\":[{\"bbox\":[350,245,356,252],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000061\":[{\"bbox\":[350,246,356,253],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000066\":[{\"bbox\":[348,245,355,252],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000071\":[{\"bbox\":[348,243,356,249],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data23_seq_013.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_new2_train_000054\":[{\"bbox\":[348,245,358,252],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000059\":[{\"bbox\":[350,245,356,252],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000064\":[{\"bbox\":[348,246,355,253],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000069\":[{\"bbox\":[347,246,355,253],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000074\":[{\"bbox\":[348,242,355,248],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data23_seq_002.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_new2_train_000003\":[{\"bbox\":[348,244,356,251],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000008\":[{\"bbox\":[348,244,355,251],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000013\":[{\"bbox\":[348,244,355,251],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000018\":[{\"bbox\":[350,244,356,250],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000023\":[{\"bbox\":[349,246,355,251],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data23_seq_006.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_new2_train_000027\":[{\"bbox\":[348,245,355,252],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000032\":[{\"bbox\":[348,245,355,252],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000037\":[{\"bbox\":[348,245,355,252],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000042\":[{\"bbox\":[348,245,355,252],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000047\":[{\"bbox\":[348,242,356,249],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data23_seq_026.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_new2_train_000127\":[{\"bbox\":[350,244,356,251],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000132\":[{\"bbox\":[348,243,355,250],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000137\":[{\"bbox\":[348,243,355,250],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000142\":[{\"bbox\":[348,244,356,252],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000147\":[{\"bbox\":[350,245,358,252],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data23_seq_016.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_new2_train_000077\":[{\"bbox\":[348,240,356,247],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000082\":[{\"bbox\":[348,239,355,245],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000087\":[{\"bbox\":[350,242,358,249],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000092\":[{\"bbox\":[351,242,357,248],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000097\":[{\"bbox\":[348,240,355,248],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data23_seq_027.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_new2_train_000128\":[{\"bbox\":[348,243,356,250],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000133\":[{\"bbox\":[348,243,356,250],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000138\":[{\"bbox\":[348,243,355,250],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000143\":[{\"bbox\":[350,243,358,250],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000148\":[{\"bbox\":[350,245,358,252],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data23_seq_019.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_new2_train_000080\":[{\"bbox\":[348,240,355,246],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000085\":[{\"bbox\":[348,240,355,246],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000090\":[{\"bbox\":[351,243,359,249],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000095\":[{\"bbox\":[350,243,356,249],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000100\":[{\"bbox\":[347,241,356,251],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data23_seq_024.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_new2_train_000105\":[{\"bbox\":[345,242,355,250],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000110\":[{\"bbox\":[348,242,356,250],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000115\":[{\"bbox\":[349,242,359,251],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000120\":[{\"bbox\":[351,243,360,251],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000125\":[{\"bbox\":[351,244,357,251],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data23_seq_017.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_new2_train_000078\":[{\"bbox\":[348,239,356,245],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000083\":[{\"bbox\":[348,240,355,247],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000088\":[{\"bbox\":[348,243,355,248],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000093\":[{\"bbox\":[350,241,358,248],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000098\":[{\"bbox\":[348,242,355,248],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data23_seq_030.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含4帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_new2_train_000151\":[{\"bbox\":[350,242,356,249],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000156\":[{\"bbox\":[348,242,356,249],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000161\":[{\"bbox\":[350,242,356,249],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000166\":[{\"bbox\":[348,243,356,251],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data23_seq_003.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_new2_train_000004\":[{\"bbox\":[348,245,355,252],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000009\":[{\"bbox\":[349,245,355,252],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000014\":[{\"bbox\":[349,244,355,251],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000019\":[{\"bbox\":[350,244,356,251],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000024\":[{\"bbox\":[349,243,355,250],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data23_seq_029.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_new2_train_000130\":[{\"bbox\":[348,243,356,250],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000135\":[{\"bbox\":[348,244,356,250],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000140\":[{\"bbox\":[348,244,356,250],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000145\":[{\"bbox\":[350,243,356,250],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000150\":[{\"bbox\":[350,243,356,250],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data23_seq_028.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_new2_train_000129\":[{\"bbox\":[348,243,355,250],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000134\":[{\"bbox\":[348,244,356,250],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000139\":[{\"bbox\":[348,243,356,250],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000144\":[{\"bbox\":[351,244,357,251],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000149\":[{\"bbox\":[350,245,358,252],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data23_seq_011.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_new2_train_000052\":[{\"bbox\":[350,245,358,252],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000057\":[{\"bbox\":[348,244,356,251],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000062\":[{\"bbox\":[348,246,355,253],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000067\":[{\"bbox\":[348,245,355,252],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000072\":[{\"bbox\":[348,241,356,248],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data23_seq_021.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_new2_train_000102\":[{\"bbox\":[348,241,355,248],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000107\":[{\"bbox\":[347,242,355,250],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000112\":[{\"bbox\":[348,242,358,249],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000117\":[{\"bbox\":[351,243,360,251],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000122\":[{\"bbox\":[349,242,359,251],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data23_seq_031.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含4帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_new2_train_000152\":[{\"bbox\":[350,242,356,249],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000157\":[{\"bbox\":[350,244,356,250],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000162\":[{\"bbox\":[350,242,356,249],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000167\":[{\"bbox\":[348,243,355,251],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data23_seq_023.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_new2_train_000104\":[{\"bbox\":[346,240,356,249],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000109\":[{\"bbox\":[347,241,356,250],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000114\":[{\"bbox\":[349,243,359,251],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000119\":[{\"bbox\":[351,243,360,251],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000124\":[{\"bbox\":[351,244,357,251],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data23_seq_033.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含4帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_new2_train_000154\":[{\"bbox\":[350,244,356,250],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000159\":[{\"bbox\":[350,242,356,249],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000164\":[{\"bbox\":[350,242,356,249],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000169\":[{\"bbox\":[350,243,356,250],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data23_seq_012.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_new2_train_000053\":[{\"bbox\":[350,245,358,252],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000058\":[{\"bbox\":[350,245,356,252],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000063\":[{\"bbox\":[348,245,356,252],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000068\":[{\"bbox\":[347,244,356,252],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000073\":[{\"bbox\":[348,241,356,248],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data23_seq_001.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_new2_train_000002\":[{\"bbox\":[348,244,355,251],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000007\":[{\"bbox\":[349,247,355,252],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000012\":[{\"bbox\":[349,245,355,252],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000017\":[{\"bbox\":[351,243,355,248],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000022\":[{\"bbox\":[350,244,356,251],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data23_seq_032.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含4帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_new2_train_000153\":[{\"bbox\":[349,244,355,250],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000158\":[{\"bbox\":[350,242,356,249],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000163\":[{\"bbox\":[348,242,356,250],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000168\":[{\"bbox\":[348,241,356,250],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data23_seq_005.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_new2_train_000026\":[{\"bbox\":[349,246,355,251],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000031\":[{\"bbox\":[348,246,356,253],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000036\":[{\"bbox\":[348,245,355,252],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000041\":[{\"bbox\":[348,244,355,251],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000046\":[{\"bbox\":[348,244,356,251],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data23_seq_025.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_new2_train_000126\":[{\"bbox\":[350,244,356,251],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000131\":[{\"bbox\":[348,244,355,252],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000136\":[{\"bbox\":[348,242,356,249],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000141\":[{\"bbox\":[350,243,356,250],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000146\":[{\"bbox\":[351,245,357,252],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data23_seq_020.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_new2_train_000101\":[{\"bbox\":[347,241,355,249],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000106\":[{\"bbox\":[347,242,355,249],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000111\":[{\"bbox\":[350,242,358,249],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000116\":[{\"bbox\":[351,243,360,251],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000121\":[{\"bbox\":[349,244,359,252],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data23_seq_014.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_new2_train_000055\":[{\"bbox\":[350,244,356,251],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000060\":[{\"bbox\":[348,246,355,253],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000065\":[{\"bbox\":[348,245,355,252],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000070\":[{\"bbox\":[348,244,355,251],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000075\":[{\"bbox\":[350,240,356,246],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data25_seq_020.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000101\":[{\"bbox\":[471,139,485,147],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000106\":[{\"bbox\":[448,139,460,145],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000111\":[{\"bbox\":[421,140,434,146],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000116\":[{\"bbox\":[401,141,415,148],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000121\":[{\"bbox\":[402,141,413,150],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data25_seq_059.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000280\":[{\"bbox\":[455,176,461,181],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000285\":[{\"bbox\":[452,175,459,180],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000290\":[{\"bbox\":[451,175,457,180],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000295\":[{\"bbox\":[445,174,451,179],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000300\":[{\"bbox\":[442,176,446,179],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data25_seq_014.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000055\":[{\"bbox\":[497,150,511,159],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000060\":[{\"bbox\":[470,149,482,158],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000065\":[{\"bbox\":[448,147,464,155],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000070\":[{\"bbox\":[448,144,462,152],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000075\":[{\"bbox\":[449,144,461,152],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data25_seq_054.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000255\":[{\"bbox\":[463,175,471,181],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000260\":[{\"bbox\":[463,175,471,181],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000265\":[{\"bbox\":[462,174,469,180],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000270\":[{\"bbox\":[461,175,467,180],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000275\":[{\"bbox\":[458,175,465,180],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data25_seq_006.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000027\":[{\"bbox\":[529,159,543,165],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000032\":[{\"bbox\":[511,158,525,164],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000037\":[{\"bbox\":[494,156,509,163],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000042\":[{\"bbox\":[488,154,502,163],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000047\":[{\"bbox\":[495,153,508,163],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data25_seq_022.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000103\":[{\"bbox\":[466,138,478,144],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000108\":[{\"bbox\":[439,139,452,145],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000113\":[{\"bbox\":[409,140,423,146],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000118\":[{\"bbox\":[399,141,413,150],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000123\":[{\"bbox\":[401,142,415,151],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data25_seq_005.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000026\":[{\"bbox\":[527,160,541,166],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000031\":[{\"bbox\":[516,159,528,165],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000036\":[{\"bbox\":[497,156,511,163],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000041\":[{\"bbox\":[485,156,499,162],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000046\":[{\"bbox\":[493,154,507,164],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data25_seq_034.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000155\":[{\"bbox\":[412,136,424,144],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000160\":[{\"bbox\":[412,136,424,144],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000165\":[{\"bbox\":[412,138,424,144],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000170\":[{\"bbox\":[412,140,423,145],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000175\":[{\"bbox\":[412,143,423,149],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data25_seq_058.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000279\":[{\"bbox\":[456,175,463,180],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000284\":[{\"bbox\":[453,175,459,180],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000289\":[{\"bbox\":[451,175,457,180],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000294\":[{\"bbox\":[447,174,453,177],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000299\":[{\"bbox\":[443,175,449,178],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data25_seq_002.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000003\":[{\"bbox\":[483,168,497,180],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000008\":[{\"bbox\":[490,166,505,174],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000013\":[{\"bbox\":[498,164,510,174],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000018\":[{\"bbox\":[509,164,523,173],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000023\":[{\"bbox\":[520,162,535,168],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data25_seq_057.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000278\":[{\"bbox\":[456,175,463,180],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000283\":[{\"bbox\":[453,175,459,180],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000288\":[{\"bbox\":[451,175,457,180],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000293\":[{\"bbox\":[450,175,454,178],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000298\":[{\"bbox\":[443,176,449,179],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data25_seq_026.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000127\":[{\"bbox\":[401,142,415,151],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000132\":[{\"bbox\":[401,142,415,149],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000137\":[{\"bbox\":[404,140,416,147],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000142\":[{\"bbox\":[408,140,420,146],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000147\":[{\"bbox\":[411,137,423,144],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data25_seq_027.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000128\":[{\"bbox\":[401,142,415,151],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000133\":[{\"bbox\":[403,142,416,149],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000138\":[{\"bbox\":[405,140,417,147],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000143\":[{\"bbox\":[409,140,421,146],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000148\":[{\"bbox\":[411,136,423,144],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data25_seq_050.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000251\":[{\"bbox\":[463,175,471,181],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000256\":[{\"bbox\":[463,175,471,181],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000261\":[{\"bbox\":[463,175,471,181],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000266\":[{\"bbox\":[462,174,469,180],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000271\":[{\"bbox\":[460,175,466,180],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data25_seq_009.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000030\":[{\"bbox\":[520,159,532,165],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000035\":[{\"bbox\":[500,157,516,164],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000040\":[{\"bbox\":[484,156,499,162],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000045\":[{\"bbox\":[494,154,505,164],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000050\":[{\"bbox\":[497,151,511,161],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data25_seq_032.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000153\":[{\"bbox\":[412,136,424,144],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000158\":[{\"bbox\":[412,136,424,144],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000163\":[{\"bbox\":[412,137,424,144],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000168\":[{\"bbox\":[412,140,423,147],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000173\":[{\"bbox\":[412,141,423,148],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data25_seq_035.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000176\":[{\"bbox\":[412,143,423,149],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000181\":[{\"bbox\":[410,144,422,152],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000186\":[{\"bbox\":[410,148,422,156],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000191\":[{\"bbox\":[412,150,424,158],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000196\":[{\"bbox\":[415,155,427,163],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data25_seq_044.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000205\":[{\"bbox\":[422,161,434,169],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000210\":[{\"bbox\":[427,163,439,170],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000215\":[{\"bbox\":[434,166,446,173],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000220\":[{\"bbox\":[440,168,451,175],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000225\":[{\"bbox\":[446,170,456,177],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data25_seq_016.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000077\":[{\"bbox\":[450,144,462,151],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000082\":[{\"bbox\":[451,142,465,148],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000087\":[{\"bbox\":[458,142,470,147],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000092\":[{\"bbox\":[464,140,476,146],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000097\":[{\"bbox\":[469,139,483,145],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data25_seq_003.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000004\":[{\"bbox\":[484,168,499,178],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000009\":[{\"bbox\":[490,166,506,175],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000014\":[{\"bbox\":[502,164,513,174],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000019\":[{\"bbox\":[511,164,525,173],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000024\":[{\"bbox\":[521,160,537,166],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data25_seq_033.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000154\":[{\"bbox\":[412,136,424,144],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000159\":[{\"bbox\":[412,136,424,144],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000164\":[{\"bbox\":[412,138,424,144],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000169\":[{\"bbox\":[412,140,423,147],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000174\":[{\"bbox\":[412,142,423,148],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data25_seq_025.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000126\":[{\"bbox\":[401,142,415,151],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000131\":[{\"bbox\":[401,142,415,149],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000136\":[{\"bbox\":[404,141,416,148],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000141\":[{\"bbox\":[406,140,418,147],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000146\":[{\"bbox\":[410,138,422,144],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data25_seq_021.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000102\":[{\"bbox\":[469,139,483,147],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000107\":[{\"bbox\":[443,139,457,145],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000112\":[{\"bbox\":[415,140,429,146],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000117\":[{\"bbox\":[401,141,414,148],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000122\":[{\"bbox\":[402,141,413,150],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data25_seq_045.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000226\":[{\"bbox\":[447,170,456,177],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000231\":[{\"bbox\":[453,175,462,180],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000236\":[{\"bbox\":[457,174,466,180],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000241\":[{\"bbox\":[458,175,468,181],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000246\":[{\"bbox\":[461,174,471,180],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data25_seq_030.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000151\":[{\"bbox\":[412,136,424,144],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000156\":[{\"bbox\":[412,136,424,144],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000161\":[{\"bbox\":[412,136,424,144],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000166\":[{\"bbox\":[412,139,424,145],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000171\":[{\"bbox\":[412,142,423,147],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data25_seq_008.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000029\":[{\"bbox\":[522,159,534,165],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000034\":[{\"bbox\":[505,157,519,164],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000039\":[{\"bbox\":[486,156,502,162],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000044\":[{\"bbox\":[492,154,503,164],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000049\":[{\"bbox\":[495,152,509,162],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data25_seq_001.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000002\":[{\"bbox\":[481,168,495,180],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000007\":[{\"bbox\":[488,166,503,174],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000012\":[{\"bbox\":[495,164,511,174],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000017\":[{\"bbox\":[507,164,521,173],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000022\":[{\"bbox\":[516,162,532,168],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data25_seq_015.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000076\":[{\"bbox\":[450,144,462,151],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000081\":[{\"bbox\":[451,142,465,148],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000086\":[{\"bbox\":[455,141,469,148],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000091\":[{\"bbox\":[462,140,474,146],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000096\":[{\"bbox\":[468,140,482,146],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data25_seq_055.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000276\":[{\"bbox\":[458,175,465,180],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000281\":[{\"bbox\":[455,175,461,180],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000286\":[{\"bbox\":[452,175,459,180],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000291\":[{\"bbox\":[450,175,454,178],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000296\":[{\"bbox\":[445,175,451,180],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data25_seq_019.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000080\":[{\"bbox\":[451,143,465,150],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000085\":[{\"bbox\":[455,141,469,148],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000090\":[{\"bbox\":[462,140,473,145],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000095\":[{\"bbox\":[465,139,479,145],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000100\":[{\"bbox\":[471,139,485,147],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data25_seq_049.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000230\":[{\"bbox\":[452,175,462,180],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000235\":[{\"bbox\":[455,174,465,180],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000240\":[{\"bbox\":[458,175,468,181],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000245\":[{\"bbox\":[459,174,469,180],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000250\":[{\"bbox\":[463,175,471,181],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data25_seq_036.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000177\":[{\"bbox\":[410,144,422,151],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000182\":[{\"bbox\":[410,145,422,154],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000187\":[{\"bbox\":[410,148,422,156],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000192\":[{\"bbox\":[412,152,424,160],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000197\":[{\"bbox\":[416,156,428,164],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data25_seq_031.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000152\":[{\"bbox\":[412,136,424,144],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000157\":[{\"bbox\":[412,136,424,144],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000162\":[{\"bbox\":[412,137,424,144],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000167\":[{\"bbox\":[412,139,424,145],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000172\":[{\"bbox\":[412,142,423,147],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data25_seq_028.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000129\":[{\"bbox\":[401,142,415,151],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000134\":[{\"bbox\":[404,142,415,149],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000139\":[{\"bbox\":[405,140,418,147],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000144\":[{\"bbox\":[409,140,421,146],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000149\":[{\"bbox\":[411,136,423,144],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data25_seq_053.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000254\":[{\"bbox\":[463,175,471,181],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000259\":[{\"bbox\":[463,175,471,181],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000264\":[{\"bbox\":[462,174,469,180],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000269\":[{\"bbox\":[460,174,466,180],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000274\":[{\"bbox\":[458,175,465,180],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data25_seq_000.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000001\":[{\"bbox\":[481,168,494,180],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000006\":[{\"bbox\":[486,166,502,174],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000011\":[{\"bbox\":[494,164,509,174],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000016\":[{\"bbox\":[505,165,518,174],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000021\":[{\"bbox\":[514,162,530,171],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data25_seq_011.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000052\":[{\"bbox\":[497,151,511,161],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000057\":[{\"bbox\":[488,150,500,159],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000062\":[{\"bbox\":[457,148,471,156],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000067\":[{\"bbox\":[447,146,461,153],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000072\":[{\"bbox\":[447,144,461,152],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data25_seq_037.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000178\":[{\"bbox\":[410,144,422,151],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000183\":[{\"bbox\":[410,145,422,154],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000188\":[{\"bbox\":[412,149,423,158],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000193\":[{\"bbox\":[414,152,425,160],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000198\":[{\"bbox\":[416,156,428,164],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data25_seq_042.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000203\":[{\"bbox\":[420,158,432,166],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000208\":[{\"bbox\":[426,162,438,169],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000213\":[{\"bbox\":[432,165,444,172],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000218\":[{\"bbox\":[438,168,450,175],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000223\":[{\"bbox\":[442,171,454,177],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data25_seq_029.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000130\":[{\"bbox\":[401,142,415,151],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000135\":[{\"bbox\":[404,142,415,149],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000140\":[{\"bbox\":[406,140,418,147],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000145\":[{\"bbox\":[410,139,422,145],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000150\":[{\"bbox\":[412,136,424,144],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data25_seq_010.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000051\":[{\"bbox\":[497,151,511,161],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000056\":[{\"bbox\":[495,150,509,159],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000061\":[{\"bbox\":[464,149,476,158],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000066\":[{\"bbox\":[446,146,462,153],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000071\":[{\"bbox\":[448,144,462,152],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data25_seq_041.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000202\":[{\"bbox\":[420,158,432,166],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000207\":[{\"bbox\":[424,161,436,168],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000212\":[{\"bbox\":[430,165,442,172],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000217\":[{\"bbox\":[436,168,447,175],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000222\":[{\"bbox\":[442,168,453,176],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data25_seq_039.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000180\":[{\"bbox\":[410,144,422,152],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000185\":[{\"bbox\":[410,148,422,156],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000190\":[{\"bbox\":[412,150,423,158],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000195\":[{\"bbox\":[414,154,426,162],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000200\":[{\"bbox\":[418,158,430,167],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data25_seq_040.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000201\":[{\"bbox\":[418,158,430,167],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000206\":[{\"bbox\":[422,160,434,168],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000211\":[{\"bbox\":[430,165,441,172],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000216\":[{\"bbox\":[435,167,447,174],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000221\":[{\"bbox\":[440,169,452,175],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data25_seq_056.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000277\":[{\"bbox\":[457,175,463,180],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000282\":[{\"bbox\":[454,175,461,180],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000287\":[{\"bbox\":[451,175,457,180],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000292\":[{\"bbox\":[450,175,454,178],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000297\":[{\"bbox\":[444,176,450,179],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data25_seq_024.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000105\":[{\"bbox\":[456,140,467,146],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000110\":[{\"bbox\":[426,140,440,146],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000115\":[{\"bbox\":[401,140,415,147],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000120\":[{\"bbox\":[402,141,413,150],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000125\":[{\"bbox\":[401,142,414,151],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data25_seq_017.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000078\":[{\"bbox\":[449,143,463,150],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000083\":[{\"bbox\":[452,141,468,148],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000088\":[{\"bbox\":[458,142,470,147],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000093\":[{\"bbox\":[465,140,477,146],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000098\":[{\"bbox\":[470,139,484,145],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data25_seq_018.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000079\":[{\"bbox\":[450,143,464,150],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000084\":[{\"bbox\":[454,141,468,148],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000089\":[{\"bbox\":[460,140,471,145],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000094\":[{\"bbox\":[465,140,479,146],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000099\":[{\"bbox\":[470,139,484,145],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data26_seq_048.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000229\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000234\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000239\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000244\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000249\":[]}"}]}, {"video": "videos/data26_seq_012.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000053\":[{\"bbox\":[531,80,533,83],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000058\":[{\"bbox\":[530,81,533,84],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000063\":[{\"bbox\":[529,80,531,83],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000068\":[{\"bbox\":[528,79,530,82],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000073\":[{\"bbox\":[525,77,527,80],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data26_seq_047.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000228\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000233\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000238\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000243\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000248\":[]}"}]}, {"video": "videos/data26_seq_053.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000254\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000259\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000264\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000269\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000274\":[]}"}]}, {"video": "videos/data26_seq_043.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000204\":[{\"bbox\":[435,0,437,1],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000209\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000214\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000219\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000224\":[]}"}]}, {"video": "videos/data26_seq_025.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000126\":[{\"bbox\":[492,47,494,50],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000131\":[{\"bbox\":[487,44,489,46],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000136\":[{\"bbox\":[483,40,485,43],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000141\":[{\"bbox\":[479,36,481,38],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000146\":[{\"bbox\":[476,34,479,36],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data26_seq_039.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000180\":[{\"bbox\":[452,12,454,15],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000185\":[{\"bbox\":[448,9,451,11],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000190\":[{\"bbox\":[444,4,447,7],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000195\":[{\"bbox\":[439,1,443,5],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000200\":[{\"bbox\":[435,0,437,1],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data26_seq_003.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000004\":[{\"bbox\":[534,84,537,88],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000009\":[{\"bbox\":[532,83,534,85],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000014\":[{\"bbox\":[531,82,533,84],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000019\":[{\"bbox\":[531,82,533,84],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000024\":[{\"bbox\":[531,81,533,84],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data26_seq_037.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000178\":[{\"bbox\":[454,15,457,18],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000183\":[{\"bbox\":[449,12,451,14],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000188\":[{\"bbox\":[445,8,447,9],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000193\":[{\"bbox\":[440,3,443,5],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000198\":[{\"bbox\":[437,0,439,3],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data26_seq_019.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000080\":[{\"bbox\":[522,75,524,77],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000085\":[{\"bbox\":[520,72,523,75],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000090\":[{\"bbox\":[517,71,519,74],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000095\":[{\"bbox\":[514,67,517,69],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000100\":[{\"bbox\":[510,64,513,67],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data26_seq_027.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000128\":[{\"bbox\":[490,45,493,48],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000133\":[{\"bbox\":[486,43,489,45],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000138\":[{\"bbox\":[482,39,484,42],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000143\":[{\"bbox\":[478,35,481,37],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000148\":[{\"bbox\":[474,33,477,36],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data26_seq_000.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000001\":[{\"bbox\":[535,83,539,87],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000006\":[{\"bbox\":[533,83,535,85],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000011\":[{\"bbox\":[532,82,534,84],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000016\":[{\"bbox\":[531,82,533,84],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000021\":[{\"bbox\":[531,82,533,84],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data26_seq_057.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000278\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000283\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000288\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000293\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000298\":[]}"}]}, {"video": "videos/data26_seq_021.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000102\":[{\"bbox\":[510,63,513,66],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000107\":[{\"bbox\":[506,60,509,62],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000112\":[{\"bbox\":[503,58,505,60],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000117\":[{\"bbox\":[499,53,501,56],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000122\":[{\"bbox\":[495,50,497,52],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data26_seq_050.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000251\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000256\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000261\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000266\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000271\":[]}"}]}, {"video": "videos/data26_seq_030.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000151\":[{\"bbox\":[472,31,474,34],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000156\":[{\"bbox\":[469,28,471,31],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000161\":[{\"bbox\":[465,26,467,28],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000166\":[{\"bbox\":[462,23,464,26],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000171\":[{\"bbox\":[459,20,461,22],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data26_seq_052.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000253\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000258\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000263\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000268\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000273\":[]}"}]}, {"video": "videos/data26_seq_007.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000028\":[{\"bbox\":[531,81,533,84],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000033\":[{\"bbox\":[532,81,534,84],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000038\":[{\"bbox\":[532,81,534,84],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000043\":[{\"bbox\":[531,81,533,84],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000048\":[{\"bbox\":[531,81,533,84],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data26_seq_045.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000226\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000231\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000236\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000241\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000246\":[]}"}]}, {"video": "videos/data26_seq_023.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000104\":[{\"bbox\":[508,63,511,64],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000109\":[{\"bbox\":[505,59,507,61],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000114\":[{\"bbox\":[500,56,503,59],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000119\":[{\"bbox\":[497,52,499,54],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000124\":[{\"bbox\":[493,48,495,51],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data26_seq_049.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000230\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000235\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000240\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000245\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000250\":[]}"}]}, {"video": "videos/data26_seq_026.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000127\":[{\"bbox\":[490,47,493,50],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000132\":[{\"bbox\":[487,43,489,45],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000137\":[{\"bbox\":[482,39,484,42],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000142\":[{\"bbox\":[478,35,481,37],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000147\":[{\"bbox\":[475,33,477,36],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data26_seq_042.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000203\":[{\"bbox\":[435,0,437,1],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000208\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000213\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000218\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000223\":[]}"}]}, {"video": "videos/data26_seq_032.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000153\":[{\"bbox\":[470,29,473,32],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000158\":[{\"bbox\":[467,27,469,29],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000163\":[{\"bbox\":[464,25,467,28],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000168\":[{\"bbox\":[460,21,463,24],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000173\":[{\"bbox\":[457,19,459,21],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data26_seq_036.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000177\":[{\"bbox\":[454,16,457,19],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000182\":[{\"bbox\":[450,12,453,14],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000187\":[{\"bbox\":[446,8,449,9],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000192\":[{\"bbox\":[442,4,444,6],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000197\":[{\"bbox\":[438,1,441,4],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data26_seq_031.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000152\":[{\"bbox\":[470,29,473,32],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000157\":[{\"bbox\":[468,28,471,30],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000162\":[{\"bbox\":[465,25,467,28],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000167\":[{\"bbox\":[460,21,463,24],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000172\":[{\"bbox\":[458,19,461,21],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data26_seq_014.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000055\":[{\"bbox\":[531,81,533,84],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000060\":[{\"bbox\":[530,80,533,83],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000065\":[{\"bbox\":[529,80,531,83],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000070\":[{\"bbox\":[527,79,529,82],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000075\":[{\"bbox\":[524,76,527,78],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data26_seq_018.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000079\":[{\"bbox\":[523,75,525,77],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000084\":[{\"bbox\":[520,73,523,76],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000089\":[{\"bbox\":[518,71,520,74],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000094\":[{\"bbox\":[515,68,517,70],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000099\":[{\"bbox\":[512,66,514,68],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data26_seq_035.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000176\":[{\"bbox\":[455,17,457,20],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000181\":[{\"bbox\":[450,12,453,15],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000186\":[{\"bbox\":[447,8,449,10],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000191\":[{\"bbox\":[442,4,446,7],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000196\":[{\"bbox\":[439,1,441,4],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data26_seq_034.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000155\":[{\"bbox\":[470,28,473,31],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000160\":[{\"bbox\":[466,26,469,28],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000165\":[{\"bbox\":[463,23,465,26],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000170\":[{\"bbox\":[460,20,463,23],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000175\":[{\"bbox\":[456,17,459,20],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data26_seq_020.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000101\":[{\"bbox\":[510,64,513,67],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000106\":[{\"bbox\":[507,62,509,64],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000111\":[{\"bbox\":[503,58,505,60],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000116\":[{\"bbox\":[500,53,503,56],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000121\":[{\"bbox\":[496,51,499,53],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data26_seq_002.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000003\":[{\"bbox\":[535,84,537,88],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000008\":[{\"bbox\":[532,83,534,85],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000013\":[{\"bbox\":[531,82,533,84],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000018\":[{\"bbox\":[531,82,533,84],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000023\":[{\"bbox\":[530,81,533,84],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data26_seq_058.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000279\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000284\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000289\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000294\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000299\":[]}"}]}, {"video": "videos/data26_seq_059.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000280\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000285\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000290\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000295\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000300\":[]}"}]}, {"video": "videos/data26_seq_041.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000202\":[{\"bbox\":[435,0,437,1],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000207\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000212\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000217\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000222\":[]}"}]}, {"video": "videos/data26_seq_024.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000105\":[{\"bbox\":[507,62,509,64],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000110\":[{\"bbox\":[503,59,505,61],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000115\":[{\"bbox\":[500,55,503,58],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000120\":[{\"bbox\":[497,52,499,54],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000125\":[{\"bbox\":[492,48,494,51],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data26_seq_006.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000027\":[{\"bbox\":[531,81,533,84],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000032\":[{\"bbox\":[531,81,533,84],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000037\":[{\"bbox\":[532,81,534,84],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000042\":[{\"bbox\":[531,81,533,84],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000047\":[{\"bbox\":[531,81,533,84],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data26_seq_011.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000052\":[{\"bbox\":[531,80,533,83],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000057\":[{\"bbox\":[531,81,533,84],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000062\":[{\"bbox\":[529,80,531,83],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000067\":[{\"bbox\":[528,79,530,82],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000072\":[{\"bbox\":[526,77,529,80],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data26_seq_016.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000077\":[{\"bbox\":[524,76,527,78],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000082\":[{\"bbox\":[522,74,524,76],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000087\":[{\"bbox\":[519,72,521,75],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000092\":[{\"bbox\":[516,68,519,71],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000097\":[{\"bbox\":[513,67,515,69],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data26_seq_040.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000201\":[{\"bbox\":[435,0,437,1],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000206\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000211\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000216\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000221\":[]}"}]}, {"video": "videos/data26_seq_051.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000252\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000257\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000262\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000267\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000272\":[]}"}]}, {"video": "videos/data26_seq_017.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000078\":[{\"bbox\":[523,76,525,78],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000083\":[{\"bbox\":[521,73,523,76],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000088\":[{\"bbox\":[519,71,521,74],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000093\":[{\"bbox\":[515,68,517,70],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000098\":[{\"bbox\":[512,66,514,68],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data26_seq_015.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000076\":[{\"bbox\":[524,76,527,78],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000081\":[{\"bbox\":[522,75,524,77],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000086\":[{\"bbox\":[519,72,521,75],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000091\":[{\"bbox\":[517,69,519,72],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000096\":[{\"bbox\":[514,67,517,69],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data26_seq_008.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000029\":[{\"bbox\":[531,81,533,84],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000034\":[{\"bbox\":[532,81,534,84],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000039\":[{\"bbox\":[532,81,534,84],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000044\":[{\"bbox\":[531,81,533,84],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000049\":[{\"bbox\":[531,81,533,84],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data26_seq_029.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000130\":[{\"bbox\":[488,44,491,46],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000135\":[{\"bbox\":[484,41,487,44],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000140\":[{\"bbox\":[480,36,483,39],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000145\":[{\"bbox\":[476,34,479,36],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000150\":[{\"bbox\":[472,31,474,34],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data26_seq_055.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000276\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000281\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000286\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000291\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000296\":[]}"}]}, {"video": "videos/data26_seq_054.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000255\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000260\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000265\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000270\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000275\":[]}"}]}, {"video": "videos/data26_seq_056.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000277\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000282\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000287\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000292\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000297\":[]}"}]}]