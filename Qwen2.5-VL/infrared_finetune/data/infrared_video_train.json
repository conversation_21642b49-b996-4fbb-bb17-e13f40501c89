[{"id": "train_data25_seq_008", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [331, 198, 377, 244],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [331, 208, 377, 254],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [326, 209, 372, 255],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [324, 212, 370, 258],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [323, 212, 369, 258],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.83\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_008.mp4"}, {"id": "train_data26_seq_029", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [218, 276, 266, 302],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [225, 269, 273, 295],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [217, 278, 265, 304],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [226, 273, 274, 299],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [221, 271, 269, 297],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.84\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_029.mp4"}, {"id": "train_data25_seq_025", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [347, 225, 387, 249],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [340, 221, 380, 245],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [330, 223, 370, 247],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [329, 231, 369, 255],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [335, 232, 375, 256],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.84\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_025.mp4"}, {"id": "train_data06_seq_007", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [41, 132, 71, 162],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [46, 125, 76, 155],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [36, 126, 66, 156],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [36, 124, 66, 154],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [29, 127, 59, 157],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.94\n    }\n  ]\n}"}], "video": "data/videos/data06_seq_007.mp4"}, {"id": "train_data05_seq_004", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [164, 68, 188, 124],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [167, 75, 191, 131],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [166, 79, 190, 135],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [169, 73, 193, 129],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [160, 70, 184, 126],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.85\n    }\n  ]\n}"}], "video": "data/videos/data05_seq_004.mp4"}, {"id": "train_data25_seq_040", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [378, 157, 432, 215],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [382, 160, 436, 218],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [376, 162, 430, 220],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [370, 171, 424, 229],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [360, 174, 414, 232],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.76\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_040.mp4"}, {"id": "train_data25_seq_056", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [303, 272, 353, 316],\n      \"class_name\": \"car\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [312, 276, 362, 320],\n      \"class_name\": \"car\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [312, 281, 362, 325],\n      \"class_name\": \"car\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [315, 276, 365, 320],\n      \"class_name\": \"car\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [305, 268, 355, 312],\n      \"class_name\": \"car\",\n      \"confidence\": 0.86\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_056.mp4"}, {"id": "train_data07_seq_011", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [207, 205, 253, 245],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [201, 209, 247, 249],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [191, 199, 237, 239],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [189, 206, 235, 246],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [199, 206, 245, 246],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.84\n    }\n  ]\n}"}], "video": "data/videos/data07_seq_011.mp4"}, {"id": "train_data25_seq_021", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [250, 309, 284, 345],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [257, 315, 291, 351],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [267, 324, 301, 360],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [267, 330, 301, 366],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [266, 340, 300, 376],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.72\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_021.mp4"}, {"id": "train_data26_seq_004", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [249, 191, 305, 221],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [248, 181, 304, 211],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [253, 184, 309, 214],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [255, 188, 311, 218],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [264, 187, 320, 217],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.82\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_004.mp4"}, {"id": "train_data25_seq_045", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [299, 303, 333, 341],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [304, 313, 338, 351],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [296, 314, 330, 352],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [299, 324, 333, 362],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [292, 332, 326, 370],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.92\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_045.mp4"}, {"id": "train_data25_seq_006", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [389, 295, 411, 339],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [385, 300, 407, 344],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [383, 307, 405, 351],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [380, 310, 402, 354],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [389, 300, 411, 344],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.79\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_006.mp4"}, {"id": "train_data04_seq_014", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [116, 102, 138, 158],\n      \"class_name\": \"car\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [106, 100, 128, 156],\n      \"class_name\": \"car\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [98, 110, 120, 166],\n      \"class_name\": \"car\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [91, 110, 113, 166],\n      \"class_name\": \"car\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [90, 105, 112, 161],\n      \"class_name\": \"car\",\n      \"confidence\": 0.76\n    }\n  ]\n}"}], "video": "data/videos/data04_seq_014.mp4"}, {"id": "train_data07_seq_004", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [103, 166, 131, 226],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [107, 162, 135, 222],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [114, 172, 142, 232],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [109, 169, 137, 229],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [103, 161, 131, 221],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.76\n    }\n  ]\n}"}], "video": "data/videos/data07_seq_004.mp4"}, {"id": "train_data25_seq_054", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [347, 225, 387, 261],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [349, 222, 389, 258],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [346, 220, 386, 256],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [356, 218, 396, 254],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [357, 225, 397, 261],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.82\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_054.mp4"}, {"id": "train_data04_seq_002", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [48, 54, 76, 84],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [39, 45, 67, 75],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [38, 45, 66, 75],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [47, 35, 75, 65],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [47, 27, 75, 57],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.92\n    }\n  ]\n}"}], "video": "data/videos/data04_seq_002.mp4"}, {"id": "train_data02_seq_014", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [16, 191, 68, 241],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [10, 186, 62, 236],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [13, 181, 65, 231],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [17, 191, 69, 241],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.7\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [24, 193, 76, 243],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.81\n    }\n  ]\n}"}], "video": "data/videos/data02_seq_014.mp4"}, {"id": "train_data26_seq_013", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [215, 262, 251, 308],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [222, 265, 258, 311],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [222, 268, 258, 314],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [232, 270, 268, 316],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [235, 272, 271, 318],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.82\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_013.mp4"}, {"id": "train_data02_seq_009", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [143, 201, 185, 227],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [145, 194, 187, 220],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [153, 185, 195, 211],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [146, 194, 188, 220],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [148, 187, 190, 213],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.86\n    }\n  ]\n}"}], "video": "data/videos/data02_seq_009.mp4"}, {"id": "train_data25_seq_013", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [237, 163, 277, 183],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [244, 168, 284, 188],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [236, 167, 276, 187],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [237, 161, 277, 181],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [247, 161, 287, 181],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.76\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_013.mp4"}, {"id": "train_data25_seq_005", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [261, 244, 291, 298],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [263, 236, 293, 290],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [263, 237, 293, 291],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [255, 233, 285, 287],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [251, 228, 281, 282],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.83\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_005.mp4"}, {"id": "train_data25_seq_043", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [196, 260, 252, 300],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [195, 255, 251, 295],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [198, 245, 254, 285],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [206, 248, 262, 288],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [198, 253, 254, 293],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.94\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_043.mp4"}, {"id": "train_data25_seq_049", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [404, 288, 440, 310],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [401, 281, 437, 303],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [395, 275, 431, 297],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [393, 283, 429, 305],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [387, 288, 423, 310],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.88\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_049.mp4"}, {"id": "train_data25_seq_052", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [320, 166, 374, 226],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [325, 159, 379, 219],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [322, 164, 376, 224],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [331, 171, 385, 231],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [321, 164, 375, 224],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.77\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_052.mp4"}, {"id": "train_data26_seq_020", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [362, 317, 404, 337],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [370, 319, 412, 339],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [372, 325, 414, 345],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [378, 315, 420, 335],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [383, 323, 425, 343],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.84\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_020.mp4"}, {"id": "train_data26_seq_044", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [274, 230, 302, 270],\n      \"class_name\": \"car\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [282, 232, 310, 272],\n      \"class_name\": \"car\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [285, 236, 313, 276],\n      \"class_name\": \"car\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [295, 236, 323, 276],\n      \"class_name\": \"car\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [304, 230, 332, 270],\n      \"class_name\": \"car\",\n      \"confidence\": 0.8\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_044.mp4"}, {"id": "train_data07_seq_000", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [106, 157, 136, 189],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [98, 151, 128, 183],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [97, 141, 127, 173],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [107, 134, 137, 166],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [102, 139, 132, 171],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.88\n    }\n  ]\n}"}], "video": "data/videos/data07_seq_000.mp4"}, {"id": "train_data26_seq_030", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [340, 217, 392, 275],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [341, 220, 393, 278],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [348, 230, 400, 288],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [355, 224, 407, 282],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [358, 228, 410, 286],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.83\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_030.mp4"}, {"id": "train_data25_seq_033", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [291, 290, 311, 324],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [283, 293, 303, 327],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [283, 286, 303, 320],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [277, 279, 297, 313],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [286, 269, 306, 303],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.89\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_033.mp4"}, {"id": "train_data26_seq_024", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [301, 318, 325, 358],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [308, 323, 332, 363],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [305, 325, 329, 365],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [295, 316, 319, 356],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [292, 321, 316, 361],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.81\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_024.mp4"}, {"id": "train_data02_seq_017", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [200, 199, 232, 221],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [200, 196, 232, 218],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [209, 186, 241, 208],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [219, 192, 251, 214],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [210, 197, 242, 219],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.92\n    }\n  ]\n}"}], "video": "data/videos/data02_seq_017.mp4"}, {"id": "train_data05_seq_000", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [144, 81, 182, 133],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [146, 91, 184, 143],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [154, 92, 192, 144],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [158, 91, 196, 143],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [149, 85, 187, 137],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.78\n    }\n  ]\n}"}], "video": "data/videos/data05_seq_000.mp4"}, {"id": "train_data25_seq_020", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [230, 168, 282, 216],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [223, 176, 275, 224],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [229, 178, 281, 226],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [222, 186, 274, 234],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [225, 194, 277, 242],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.92\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_020.mp4"}, {"id": "train_data06_seq_003", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [204, 112, 234, 140],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [209, 105, 239, 133],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [211, 96, 241, 124],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [219, 96, 249, 124],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [220, 88, 250, 116],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.76\n    }\n  ]\n}"}], "video": "data/videos/data06_seq_003.mp4"}, {"id": "train_data25_seq_048", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [229, 221, 269, 277],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [237, 220, 277, 276],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [237, 228, 277, 284],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [236, 231, 276, 287],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [229, 221, 269, 277],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.92\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_048.mp4"}, {"id": "train_data25_seq_023", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [381, 217, 411, 265],\n      \"class_name\": \"car\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [390, 209, 420, 257],\n      \"class_name\": \"car\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [397, 215, 427, 263],\n      \"class_name\": \"car\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [392, 208, 422, 256],\n      \"class_name\": \"car\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [402, 209, 432, 257],\n      \"class_name\": \"car\",\n      \"confidence\": 0.8\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_023.mp4"}, {"id": "train_data25_seq_022", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [245, 239, 293, 293],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [250, 238, 298, 292],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [256, 230, 304, 284],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [260, 236, 308, 290],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [255, 235, 303, 289],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.92\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_022.mp4"}, {"id": "train_data05_seq_010", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [181, 41, 205, 89],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [183, 31, 207, 79],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [176, 36, 200, 84],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [179, 31, 203, 79],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [171, 26, 195, 74],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.71\n    }\n  ]\n}"}], "video": "data/videos/data05_seq_010.mp4"}, {"id": "train_data04_seq_006", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [79, 77, 115, 129],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [80, 85, 116, 137],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [71, 91, 107, 143],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [80, 92, 116, 144],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [88, 85, 124, 137],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.9\n    }\n  ]\n}"}], "video": "data/videos/data04_seq_006.mp4"}, {"id": "train_data25_seq_009", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [387, 158, 409, 198],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [387, 155, 409, 195],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [384, 147, 406, 187],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [374, 138, 396, 178],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [373, 139, 395, 179],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.77\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_009.mp4"}, {"id": "train_data26_seq_048", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [363, 255, 417, 303],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [372, 253, 426, 301],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [369, 259, 423, 307],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [362, 263, 416, 311],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [372, 262, 426, 310],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.71\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_048.mp4"}, {"id": "train_data25_seq_059", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [227, 301, 279, 361],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [217, 296, 269, 356],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.7\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [211, 302, 263, 362],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [207, 301, 259, 361],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [207, 298, 259, 358],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.94\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_059.mp4"}, {"id": "train_data26_seq_045", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [296, 191, 338, 245],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [297, 184, 339, 238],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [292, 177, 334, 231],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [298, 184, 340, 238],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [307, 189, 349, 243],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.86\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_045.mp4"}, {"id": "train_data06_seq_004", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [137, 193, 169, 217],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [141, 193, 173, 217],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [134, 198, 166, 222],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [131, 206, 163, 230],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [121, 197, 153, 221],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.76\n    }\n  ]\n}"}], "video": "data/videos/data06_seq_004.mp4"}, {"id": "train_data07_seq_002", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [87, 200, 111, 256],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [97, 205, 121, 256],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [98, 200, 122, 256],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [101, 190, 125, 246],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [92, 190, 116, 246],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.84\n    }\n  ]\n}"}], "video": "data/videos/data07_seq_002.mp4"}, {"id": "train_data25_seq_018", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [288, 303, 326, 343],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [284, 310, 322, 350],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [290, 319, 328, 359],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [281, 327, 319, 367],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [279, 336, 317, 376],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.77\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_018.mp4"}, {"id": "train_data06_seq_014", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [131, 125, 161, 169],\n      \"class_name\": \"car\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [124, 132, 154, 176],\n      \"class_name\": \"car\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [128, 130, 158, 174],\n      \"class_name\": \"car\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [138, 126, 168, 170],\n      \"class_name\": \"car\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [137, 122, 167, 166],\n      \"class_name\": \"car\",\n      \"confidence\": 0.94\n    }\n  ]\n}"}], "video": "data/videos/data06_seq_014.mp4"}, {"id": "train_data04_seq_010", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [140, 204, 176, 242],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [135, 198, 171, 236],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [132, 203, 168, 241],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [125, 205, 161, 243],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [135, 215, 171, 253],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.76\n    }\n  ]\n}"}], "video": "data/videos/data04_seq_010.mp4"}, {"id": "train_data26_seq_034", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [245, 330, 295, 354],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [237, 320, 287, 344],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [232, 316, 282, 340],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [233, 310, 283, 334],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [235, 309, 285, 333],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.84\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_034.mp4"}, {"id": "train_data25_seq_038", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [299, 260, 327, 300],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [302, 252, 330, 292],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [300, 256, 328, 296],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [308, 251, 336, 291],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [309, 257, 337, 297],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.74\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_038.mp4"}, {"id": "train_data02_seq_027", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [141, 143, 195, 197],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [134, 151, 188, 205],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [129, 143, 183, 197],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [139, 143, 193, 197],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [129, 153, 183, 207],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.92\n    }\n  ]\n}"}], "video": "data/videos/data02_seq_027.mp4"}, {"id": "train_data26_seq_018", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [364, 222, 420, 278],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [369, 214, 425, 270],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [373, 216, 429, 272],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [379, 225, 435, 281],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [378, 234, 434, 290],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.88\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_018.mp4"}, {"id": "train_data02_seq_019", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [172, 41, 202, 81],\n      \"class_name\": \"car\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [170, 40, 200, 80],\n      \"class_name\": \"car\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [171, 35, 201, 75],\n      \"class_name\": \"car\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [161, 31, 191, 71],\n      \"class_name\": \"car\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [155, 24, 185, 64],\n      \"class_name\": \"car\",\n      \"confidence\": 0.82\n    }\n  ]\n}"}], "video": "data/videos/data02_seq_019.mp4"}, {"id": "train_data07_seq_008", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [35, 49, 83, 93],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [29, 56, 77, 100],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [38, 46, 86, 90],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [29, 36, 77, 80],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [34, 35, 82, 79],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.85\n    }\n  ]\n}"}], "video": "data/videos/data07_seq_008.mp4"}, {"id": "train_data02_seq_028", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [198, 91, 218, 121],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [202, 85, 222, 115],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [211, 94, 231, 124],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [211, 92, 231, 122],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.7\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [207, 84, 227, 114],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.94\n    }\n  ]\n}"}], "video": "data/videos/data02_seq_028.mp4"}, {"id": "train_data06_seq_001", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [211, 63, 239, 119],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [201, 69, 229, 125],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.7\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [192, 77, 220, 133],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [191, 76, 219, 132],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [184, 84, 212, 140],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.73\n    }\n  ]\n}"}], "video": "data/videos/data06_seq_001.mp4"}, {"id": "train_data02_seq_005", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [187, 101, 227, 137],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [194, 109, 234, 145],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [184, 103, 224, 139],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [186, 96, 226, 132],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [181, 100, 221, 136],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.77\n    }\n  ]\n}"}], "video": "data/videos/data02_seq_005.mp4"}, {"id": "train_data05_seq_008", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [33, 153, 61, 195],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [36, 157, 64, 199],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [39, 158, 67, 200],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [32, 168, 60, 210],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [35, 161, 63, 203],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.89\n    }\n  ]\n}"}], "video": "data/videos/data05_seq_008.mp4"}, {"id": "train_data26_seq_014", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [312, 291, 350, 333],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [308, 288, 346, 330],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [311, 284, 349, 326],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [312, 290, 350, 332],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [312, 291, 350, 333],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.85\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_014.mp4"}, {"id": "train_data02_seq_002", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [130, 129, 152, 171],\n      \"class_name\": \"car\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [132, 126, 154, 168],\n      \"class_name\": \"car\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [136, 122, 158, 164],\n      \"class_name\": \"car\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [146, 124, 168, 166],\n      \"class_name\": \"car\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [136, 131, 158, 173],\n      \"class_name\": \"car\",\n      \"confidence\": 0.93\n    }\n  ]\n}"}], "video": "data/videos/data02_seq_002.mp4"}, {"id": "train_data04_seq_013", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [80, 140, 136, 160],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [85, 133, 141, 153],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [95, 140, 151, 160],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [96, 145, 152, 165],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [95, 139, 151, 159],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.94\n    }\n  ]\n}"}], "video": "data/videos/data04_seq_013.mp4"}, {"id": "train_data26_seq_049", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [315, 182, 375, 238],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [314, 173, 374, 229],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [304, 175, 364, 231],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [301, 181, 361, 237],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [311, 191, 371, 247],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.72\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_049.mp4"}, {"id": "train_data04_seq_009", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [38, 149, 96, 183],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [38, 155, 96, 189],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [41, 154, 99, 188],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [45, 160, 103, 194],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [47, 170, 105, 204],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.92\n    }\n  ]\n}"}], "video": "data/videos/data04_seq_009.mp4"}, {"id": "train_data25_seq_002", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [211, 138, 245, 166],\n      \"class_name\": \"car\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [216, 132, 250, 160],\n      \"class_name\": \"car\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [208, 140, 242, 168],\n      \"class_name\": \"car\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [211, 133, 245, 161],\n      \"class_name\": \"car\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [216, 135, 250, 163],\n      \"class_name\": \"car\",\n      \"confidence\": 0.86\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_002.mp4"}, {"id": "train_data02_seq_008", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [9, 78, 49, 118],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [5, 82, 45, 122],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [0, 76, 35, 116],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [0, 71, 35, 111],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [0, 81, 27, 121],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.78\n    }\n  ]\n}"}], "video": "data/videos/data02_seq_008.mp4"}, {"id": "train_data26_seq_059", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [391, 207, 419, 231],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [383, 200, 411, 224],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [391, 209, 419, 233],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [395, 206, 423, 230],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [397, 216, 425, 240],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.89\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_059.mp4"}, {"id": "train_data26_seq_050", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [358, 235, 378, 259],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [360, 228, 380, 252],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [366, 221, 386, 245],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [369, 225, 389, 249],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.7\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [365, 221, 385, 245],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.79\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_050.mp4"}, {"id": "train_data25_seq_036", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [326, 251, 366, 279],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [322, 244, 362, 272],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [315, 241, 355, 269],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [305, 236, 345, 264],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [309, 234, 349, 262],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.71\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_036.mp4"}, {"id": "train_data25_seq_034", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [323, 264, 345, 310],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [317, 259, 339, 305],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [308, 254, 330, 300],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [317, 245, 339, 291],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [326, 243, 348, 289],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.85\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_034.mp4"}, {"id": "train_data02_seq_022", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [99, 212, 151, 236],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.7\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [92, 213, 144, 237],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [85, 208, 137, 232],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [92, 212, 144, 236],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [88, 209, 140, 233],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.84\n    }\n  ]\n}"}], "video": "data/videos/data02_seq_022.mp4"}, {"id": "train_data05_seq_014", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [154, 224, 186, 246],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [162, 233, 194, 255],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [160, 243, 192, 256],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [163, 238, 195, 256],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [160, 237, 192, 256],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.87\n    }\n  ]\n}"}], "video": "data/videos/data05_seq_014.mp4"}, {"id": "train_data26_seq_046", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [352, 229, 394, 261],\n      \"class_name\": \"car\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [350, 233, 392, 265],\n      \"class_name\": \"car\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [344, 225, 386, 257],\n      \"class_name\": \"car\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [346, 232, 388, 264],\n      \"class_name\": \"car\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [347, 225, 389, 257],\n      \"class_name\": \"car\",\n      \"confidence\": 0.8\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_046.mp4"}, {"id": "train_data25_seq_029", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [318, 312, 368, 340],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [328, 322, 378, 350],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [325, 325, 375, 353],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [315, 327, 365, 355],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [324, 319, 374, 347],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.92\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_029.mp4"}, {"id": "train_data06_seq_010", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [180, 0, 210, 53],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [175, 0, 205, 45],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [172, 0, 202, 48],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [168, 0, 198, 47],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [173, 0, 203, 47],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.72\n    }\n  ]\n}"}], "video": "data/videos/data06_seq_010.mp4"}, {"id": "train_data25_seq_050", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [297, 211, 345, 261],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [296, 215, 344, 265],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [298, 215, 346, 265],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [294, 208, 342, 258],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [285, 204, 333, 254],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.84\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_050.mp4"}, {"id": "train_data06_seq_006", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [11, 186, 47, 230],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [4, 178, 40, 222],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [6, 169, 42, 213],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [10, 162, 46, 206],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [20, 167, 56, 211],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.76\n    }\n  ]\n}"}], "video": "data/videos/data06_seq_006.mp4"}, {"id": "train_data25_seq_014", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [350, 254, 384, 294],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [354, 251, 388, 291],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [352, 256, 386, 296],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [347, 262, 381, 302],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [349, 266, 383, 306],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.73\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_014.mp4"}, {"id": "train_data26_seq_019", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [347, 176, 397, 204],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [337, 182, 387, 210],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [343, 180, 393, 208],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [340, 189, 390, 217],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [338, 188, 388, 216],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.74\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_019.mp4"}, {"id": "train_data25_seq_016", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [271, 312, 315, 344],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [261, 307, 305, 339],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [261, 299, 305, 331],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [261, 300, 305, 332],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [267, 290, 311, 322],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.91\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_016.mp4"}, {"id": "train_data02_seq_011", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [74, 136, 96, 188],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [66, 144, 88, 196],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [61, 153, 83, 205],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [56, 147, 78, 199],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [62, 155, 84, 207],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.77\n    }\n  ]\n}"}], "video": "data/videos/data02_seq_011.mp4"}, {"id": "train_data26_seq_042", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [248, 334, 298, 372],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [241, 332, 291, 370],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [244, 331, 294, 369],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [246, 326, 296, 364],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [250, 325, 300, 363],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.89\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_042.mp4"}, {"id": "train_data06_seq_005", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [31, 20, 79, 78],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [23, 26, 71, 84],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [32, 32, 80, 90],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [31, 32, 79, 90],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [31, 25, 79, 83],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.7\n    }\n  ]\n}"}], "video": "data/videos/data06_seq_005.mp4"}, {"id": "train_data06_seq_012", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [13, 27, 51, 51],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [10, 25, 48, 49],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [13, 26, 51, 50],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [20, 18, 58, 42],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [25, 20, 63, 44],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.79\n    }\n  ]\n}"}], "video": "data/videos/data06_seq_012.mp4"}, {"id": "train_data26_seq_031", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [361, 290, 397, 348],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [366, 297, 402, 355],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [369, 303, 405, 361],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [361, 304, 397, 362],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [367, 298, 403, 356],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.81\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_031.mp4"}, {"id": "train_data25_seq_026", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [315, 157, 357, 189],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [309, 158, 351, 190],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [307, 157, 349, 189],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [302, 149, 344, 181],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [301, 145, 343, 177],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.93\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_026.mp4"}, {"id": "train_data25_seq_003", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [259, 331, 309, 363],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [267, 329, 317, 361],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [269, 327, 319, 359],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [271, 331, 321, 363],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [272, 327, 322, 359],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.93\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_003.mp4"}, {"id": "train_data06_seq_002", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [55, 144, 77, 194],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [54, 144, 76, 194],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [51, 141, 73, 191],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [44, 134, 66, 184],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [45, 142, 67, 192],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.74\n    }\n  ]\n}"}], "video": "data/videos/data06_seq_002.mp4"}, {"id": "train_data25_seq_031", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [241, 146, 281, 168],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [249, 143, 289, 165],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [250, 133, 290, 155],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [242, 133, 282, 155],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [232, 137, 272, 159],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.79\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_031.mp4"}, {"id": "train_data26_seq_056", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [346, 273, 366, 319],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [350, 272, 370, 318],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [344, 266, 364, 312],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [349, 262, 369, 308],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [342, 272, 362, 318],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.79\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_056.mp4"}, {"id": "train_data25_seq_046", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [235, 225, 259, 283],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [243, 230, 267, 288],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [244, 238, 268, 296],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [240, 243, 264, 301],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [239, 233, 263, 291],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.79\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_046.mp4"}, {"id": "train_data26_seq_009", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [273, 165, 313, 199],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [278, 173, 318, 207],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [278, 173, 318, 207],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [269, 177, 309, 211],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [279, 172, 319, 206],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.81\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_009.mp4"}, {"id": "train_data26_seq_000", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [376, 293, 408, 345],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [385, 292, 417, 344],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [375, 287, 407, 339],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [375, 297, 407, 349],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [367, 301, 399, 353],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.84\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_000.mp4"}, {"id": "train_data25_seq_010", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [373, 169, 397, 217],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [375, 159, 399, 207],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [368, 164, 392, 212],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [371, 159, 395, 207],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [363, 154, 387, 202],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.71\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_010.mp4"}, {"id": "train_data26_seq_027", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [350, 158, 372, 190],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [359, 165, 381, 197],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [359, 169, 381, 201],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [352, 172, 374, 204],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [358, 175, 380, 207],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.82\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_027.mp4"}, {"id": "train_data06_seq_011", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [141, 188, 181, 236],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [151, 192, 191, 240],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [153, 188, 193, 236],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [160, 188, 200, 236],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [160, 186, 200, 234],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.87\n    }\n  ]\n}"}], "video": "data/videos/data06_seq_011.mp4"}, {"id": "train_data25_seq_007", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [283, 123, 323, 175],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [282, 114, 322, 166],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [280, 108, 320, 160],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [281, 114, 321, 166],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [283, 111, 323, 163],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.9\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_007.mp4"}, {"id": "train_data26_seq_055", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [386, 287, 422, 329],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [390, 290, 426, 332],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [394, 281, 430, 323],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [388, 275, 424, 317],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [384, 282, 420, 324],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.72\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_055.mp4"}, {"id": "train_data02_seq_024", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [40, 54, 74, 94],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [40, 46, 74, 86],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [46, 56, 80, 96],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [54, 56, 88, 96],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [50, 46, 84, 86],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.85\n    }\n  ]\n}"}], "video": "data/videos/data02_seq_024.mp4"}, {"id": "train_data25_seq_019", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [234, 241, 264, 265],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [244, 245, 274, 269],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [246, 253, 276, 277],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [236, 249, 266, 273],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.7\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [228, 253, 258, 277],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.77\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_019.mp4"}, {"id": "train_data07_seq_006", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [93, 199, 121, 239],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [91, 207, 119, 247],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [97, 205, 125, 245],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [105, 198, 133, 238],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [111, 188, 139, 228],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.72\n    }\n  ]\n}"}], "video": "data/videos/data07_seq_006.mp4"}, {"id": "train_data26_seq_028", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [230, 218, 260, 240],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [233, 222, 263, 244],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [223, 213, 253, 235],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [231, 212, 261, 234],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [223, 216, 253, 238],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.93\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_028.mp4"}, {"id": "train_data05_seq_011", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [114, 9, 166, 33],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [116, 2, 168, 26],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [126, 7, 178, 31],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [116, 2, 168, 26],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [111, 0, 163, 22],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.74\n    }\n  ]\n}"}], "video": "data/videos/data05_seq_011.mp4"}, {"id": "train_data04_seq_000", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [66, 95, 102, 141],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [75, 102, 111, 148],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [82, 94, 118, 140],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [82, 85, 118, 131],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [82, 84, 118, 130],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.92\n    }\n  ]\n}"}], "video": "data/videos/data04_seq_000.mp4"}, {"id": "train_data26_seq_033", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [237, 163, 277, 183],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [244, 168, 284, 188],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [236, 167, 276, 187],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [237, 161, 277, 181],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [247, 161, 287, 181],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.76\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_033.mp4"}, {"id": "train_data07_seq_013", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [106, 187, 164, 215],\n      \"class_name\": \"car\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [105, 196, 163, 224],\n      \"class_name\": \"car\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [108, 194, 166, 222],\n      \"class_name\": \"car\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [109, 202, 167, 230],\n      \"class_name\": \"car\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [103, 205, 161, 233],\n      \"class_name\": \"car\",\n      \"confidence\": 0.78\n    }\n  ]\n}"}], "video": "data/videos/data07_seq_013.mp4"}, {"id": "train_data26_seq_041", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [254, 299, 312, 323],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [263, 292, 321, 316],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [256, 287, 314, 311],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [257, 294, 315, 318],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [249, 291, 307, 315],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.9\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_041.mp4"}, {"id": "train_data26_seq_015", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [268, 155, 312, 177],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [261, 150, 305, 172],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [271, 144, 315, 166],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [261, 151, 305, 173],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [264, 145, 308, 167],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.89\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_015.mp4"}, {"id": "train_data04_seq_001", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [194, 112, 234, 168],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [192, 109, 232, 165],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [183, 105, 223, 161],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [182, 103, 222, 159],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [174, 100, 214, 156],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.8\n    }\n  ]\n}"}], "video": "data/videos/data04_seq_001.mp4"}, {"id": "train_data05_seq_006", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [58, 170, 110, 196],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [65, 174, 117, 200],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [73, 167, 125, 193],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [67, 170, 119, 196],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [67, 168, 119, 194],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.71\n    }\n  ]\n}"}], "video": "data/videos/data05_seq_006.mp4"}, {"id": "train_data02_seq_018", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [25, 151, 81, 205],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [35, 151, 91, 205],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [45, 144, 101, 198],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [41, 138, 97, 192],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [49, 129, 105, 183],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.88\n    }\n  ]\n}"}], "video": "data/videos/data02_seq_018.mp4"}, {"id": "train_data26_seq_012", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [320, 308, 356, 346],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [327, 308, 363, 346],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [329, 303, 365, 341],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [326, 308, 362, 346],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [336, 316, 372, 354],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.86\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_012.mp4"}, {"id": "train_data05_seq_002", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [196, 13, 232, 45],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [191, 19, 227, 51],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [194, 23, 230, 55],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [197, 14, 233, 46],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [191, 19, 227, 51],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.89\n    }\n  ]\n}"}], "video": "data/videos/data05_seq_002.mp4"}, {"id": "train_data02_seq_006", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [28, 89, 62, 117],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [26, 95, 60, 123],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [32, 91, 66, 119],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [29, 89, 63, 117],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [32, 99, 66, 127],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.87\n    }\n  ]\n}"}], "video": "data/videos/data02_seq_006.mp4"}, {"id": "train_data02_seq_010", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [128, 43, 162, 89],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [138, 46, 172, 92],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [128, 51, 162, 97],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [137, 52, 171, 98],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [135, 45, 169, 91],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.94\n    }\n  ]\n}"}], "video": "data/videos/data02_seq_010.mp4"}, {"id": "train_data26_seq_043", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [385, 150, 435, 206],\n      \"class_name\": \"car\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [386, 154, 436, 210],\n      \"class_name\": \"car\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [385, 152, 435, 208],\n      \"class_name\": \"car\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [386, 144, 436, 200],\n      \"class_name\": \"car\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [380, 135, 430, 191],\n      \"class_name\": \"car\",\n      \"confidence\": 0.94\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_043.mp4"}, {"id": "train_data25_seq_028", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [294, 345, 328, 365],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [303, 345, 337, 365],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [311, 355, 345, 375],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [314, 364, 348, 384],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [323, 374, 357, 394],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.93\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_028.mp4"}, {"id": "train_data26_seq_026", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [381, 153, 407, 185],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [377, 157, 403, 189],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [369, 166, 395, 198],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [370, 172, 396, 204],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [368, 171, 394, 203],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.77\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_026.mp4"}, {"id": "train_data26_seq_058", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [332, 178, 368, 236],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [334, 185, 370, 243],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [343, 176, 379, 234],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [344, 168, 380, 226],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [352, 178, 388, 236],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.85\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_058.mp4"}, {"id": "train_data26_seq_002", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [301, 159, 355, 179],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [298, 152, 352, 172],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [295, 154, 349, 174],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [286, 148, 340, 168],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [281, 147, 335, 167],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.87\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_002.mp4"}, {"id": "train_data26_seq_040", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [276, 151, 316, 171],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [267, 141, 307, 161],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [260, 146, 300, 166],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [262, 140, 302, 160],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [270, 134, 310, 154],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.78\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_040.mp4"}, {"id": "train_data26_seq_047", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [222, 256, 258, 312],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [219, 254, 255, 310],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [215, 249, 251, 305],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [224, 254, 260, 310],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [223, 262, 259, 318],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.73\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_047.mp4"}, {"id": "train_data07_seq_003", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [81, 112, 101, 144],\n      \"class_name\": \"car\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [71, 122, 91, 154],\n      \"class_name\": \"car\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [72, 117, 92, 149],\n      \"class_name\": \"car\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [62, 116, 82, 148],\n      \"class_name\": \"car\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [54, 117, 74, 149],\n      \"class_name\": \"car\",\n      \"confidence\": 0.7\n    }\n  ]\n}"}], "video": "data/videos/data07_seq_003.mp4"}, {"id": "train_data02_seq_021", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [35, 11, 71, 45],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [42, 3, 78, 37],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [33, 0, 69, 27],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [30, 0, 66, 33],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [37, 0, 73, 29],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.88\n    }\n  ]\n}"}], "video": "data/videos/data02_seq_021.mp4"}, {"id": "train_data26_seq_032", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [236, 338, 290, 360],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [228, 334, 282, 356],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [234, 327, 288, 349],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [224, 317, 278, 339],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [215, 308, 269, 330],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.74\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_032.mp4"}, {"id": "train_data26_seq_051", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [290, 201, 330, 237],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [295, 206, 335, 242],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [300, 197, 340, 233],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [290, 195, 330, 231],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [281, 203, 321, 239],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.81\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_051.mp4"}, {"id": "train_data04_seq_008", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [98, 153, 132, 211],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [96, 160, 130, 218],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [90, 161, 124, 219],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [84, 159, 118, 217],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [74, 168, 108, 226],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.93\n    }\n  ]\n}"}], "video": "data/videos/data04_seq_008.mp4"}, {"id": "train_data26_seq_021", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [291, 182, 333, 214],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [300, 172, 342, 204],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [310, 179, 352, 211],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [302, 176, 344, 208],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [293, 183, 335, 215],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.76\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_021.mp4"}, {"id": "train_data25_seq_041", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [351, 268, 393, 290],\n      \"class_name\": \"car\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [341, 274, 383, 296],\n      \"class_name\": \"car\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [346, 274, 388, 296],\n      \"class_name\": \"car\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [346, 277, 388, 299],\n      \"class_name\": \"car\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [346, 286, 388, 308],\n      \"class_name\": \"car\",\n      \"confidence\": 0.89\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_041.mp4"}, {"id": "train_data26_seq_039", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [347, 225, 387, 249],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [340, 221, 380, 245],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [330, 223, 370, 247],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [329, 231, 369, 255],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [335, 232, 375, 256],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.84\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_039.mp4"}, {"id": "train_data26_seq_008", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [352, 157, 404, 211],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [348, 157, 400, 211],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [357, 163, 409, 217],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [348, 161, 400, 215],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [347, 153, 399, 207],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.87\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_008.mp4"}, {"id": "train_data04_seq_004", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [172, 94, 228, 150],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [177, 86, 233, 142],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [181, 88, 237, 144],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [187, 97, 243, 153],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [186, 106, 242, 162],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.88\n    }\n  ]\n}"}], "video": "data/videos/data04_seq_004.mp4"}, {"id": "train_data02_seq_001", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [184, 25, 224, 69],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [182, 19, 222, 63],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [174, 19, 214, 63],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [179, 14, 219, 58],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.7\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [173, 13, 213, 57],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.86\n    }\n  ]\n}"}], "video": "data/videos/data02_seq_001.mp4"}, {"id": "train_data05_seq_007", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [143, 190, 169, 244],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [140, 196, 166, 250],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [136, 193, 162, 247],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [146, 189, 172, 243],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [146, 179, 172, 233],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.83\n    }\n  ]\n}"}], "video": "data/videos/data05_seq_007.mp4"}, {"id": "train_data26_seq_016", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [302, 225, 322, 275],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [305, 233, 325, 283],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [300, 226, 320, 276],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [301, 234, 321, 284],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [298, 227, 318, 277],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.92\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_016.mp4"}, {"id": "train_data26_seq_025", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [238, 164, 264, 196],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [228, 165, 254, 197],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [225, 173, 251, 205],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [228, 176, 254, 208],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [225, 181, 251, 213],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.94\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_025.mp4"}, {"id": "train_data06_seq_009", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [111, 193, 145, 235],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [105, 200, 139, 242],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [105, 194, 139, 236],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [99, 184, 133, 226],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [90, 191, 124, 233],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.8\n    }\n  ]\n}"}], "video": "data/videos/data06_seq_009.mp4"}, {"id": "train_data25_seq_004", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [215, 262, 251, 308],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [222, 265, 258, 311],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [222, 268, 258, 314],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [232, 270, 268, 316],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [235, 272, 271, 318],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.82\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_004.mp4"}, {"id": "train_data25_seq_044", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [299, 303, 333, 341],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [304, 313, 338, 351],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [296, 314, 330, 352],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [299, 324, 333, 362],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [292, 332, 326, 370],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.92\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_044.mp4"}, {"id": "train_data26_seq_052", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [392, 327, 424, 349],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [392, 324, 424, 346],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [401, 314, 433, 336],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [411, 320, 443, 342],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [402, 325, 434, 347],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.92\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_052.mp4"}, {"id": "train_data02_seq_020", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [38, 135, 90, 181],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [29, 128, 81, 174],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [27, 133, 79, 179],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [26, 130, 78, 176],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [28, 133, 80, 179],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.72\n    }\n  ]\n}"}], "video": "data/videos/data02_seq_020.mp4"}, {"id": "train_data25_seq_024", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [344, 162, 400, 188],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [343, 153, 399, 179],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [345, 155, 401, 181],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [348, 162, 404, 188],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [350, 171, 406, 197],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.85\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_024.mp4"}, {"id": "train_data25_seq_035", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [235, 317, 267, 365],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [240, 320, 272, 368],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [230, 318, 262, 366],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [240, 314, 272, 362],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [235, 319, 267, 367],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.89\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_035.mp4"}, {"id": "train_data25_seq_011", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [284, 179, 328, 213],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [280, 169, 324, 203],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [273, 162, 317, 196],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [264, 158, 308, 192],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [264, 148, 308, 182],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.79\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_011.mp4"}, {"id": "train_data06_seq_008", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [110, 93, 132, 123],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [114, 93, 136, 123],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [119, 97, 141, 127],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [115, 89, 137, 119],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [112, 84, 134, 114],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.9\n    }\n  ]\n}"}], "video": "data/videos/data06_seq_008.mp4"}, {"id": "train_data25_seq_027", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [233, 262, 273, 284],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [226, 260, 266, 282],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [222, 262, 262, 284],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [222, 264, 262, 286],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [229, 258, 269, 280],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.78\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_027.mp4"}, {"id": "train_data04_seq_012", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [56, 189, 84, 235],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [57, 192, 85, 238],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.7\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [56, 190, 84, 236],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [50, 197, 78, 243],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [57, 189, 85, 235],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.73\n    }\n  ]\n}"}], "video": "data/videos/data04_seq_012.mp4"}, {"id": "train_data26_seq_007", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [220, 217, 254, 245],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [218, 223, 252, 251],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [224, 219, 258, 247],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [221, 217, 255, 245],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [224, 227, 258, 255],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.87\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_007.mp4"}, {"id": "train_data02_seq_013", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [96, 103, 152, 151],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [90, 103, 146, 151],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [88, 112, 144, 160],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [84, 109, 140, 157],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [81, 102, 137, 150],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.79\n    }\n  ]\n}"}], "video": "data/videos/data02_seq_013.mp4"}, {"id": "train_data26_seq_003", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [310, 305, 368, 337],\n      \"class_name\": \"car\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [319, 309, 377, 341],\n      \"class_name\": \"car\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [309, 299, 367, 331],\n      \"class_name\": \"car\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [304, 302, 362, 334],\n      \"class_name\": \"car\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [294, 303, 352, 335],\n      \"class_name\": \"car\",\n      \"confidence\": 0.71\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_003.mp4"}, {"id": "train_data04_seq_011", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [73, 58, 95, 96],\n      \"class_name\": \"car\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [79, 49, 101, 87],\n      \"class_name\": \"car\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [73, 54, 95, 92],\n      \"class_name\": \"car\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [71, 47, 93, 85],\n      \"class_name\": \"car\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [78, 47, 100, 85],\n      \"class_name\": \"car\",\n      \"confidence\": 0.86\n    }\n  ]\n}"}], "video": "data/videos/data04_seq_011.mp4"}, {"id": "train_data25_seq_053", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [274, 230, 302, 270],\n      \"class_name\": \"car\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [282, 232, 310, 272],\n      \"class_name\": \"car\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [285, 236, 313, 276],\n      \"class_name\": \"car\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [295, 236, 323, 276],\n      \"class_name\": \"car\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [304, 230, 332, 270],\n      \"class_name\": \"car\",\n      \"confidence\": 0.8\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_053.mp4"}, {"id": "train_data05_seq_009", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [14, 138, 44, 176],\n      \"class_name\": \"car\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [17, 130, 47, 168],\n      \"class_name\": \"car\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [11, 127, 41, 165],\n      \"class_name\": \"car\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [7, 118, 37, 156],\n      \"class_name\": \"car\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [3, 124, 33, 162],\n      \"class_name\": \"car\",\n      \"confidence\": 0.82\n    }\n  ]\n}"}], "video": "data/videos/data05_seq_009.mp4"}, {"id": "train_data25_seq_055", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [303, 307, 349, 367],\n      \"class_name\": \"car\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [308, 313, 354, 373],\n      \"class_name\": \"car\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [314, 310, 360, 370],\n      \"class_name\": \"car\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [304, 311, 350, 371],\n      \"class_name\": \"car\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [307, 303, 353, 363],\n      \"class_name\": \"car\",\n      \"confidence\": 0.74\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_055.mp4"}, {"id": "train_data26_seq_006", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [218, 249, 256, 269],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.7\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [214, 244, 252, 264],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [219, 254, 257, 274],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [211, 252, 249, 272],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [205, 252, 243, 272],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.95\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_006.mp4"}, {"id": "train_data05_seq_013", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [184, 25, 224, 69],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [182, 19, 222, 63],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [174, 19, 214, 63],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [179, 14, 219, 58],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.7\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [173, 13, 213, 57],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.86\n    }\n  ]\n}"}], "video": "data/videos/data05_seq_013.mp4"}, {"id": "train_data02_seq_000", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [97, 195, 153, 233],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [97, 199, 153, 237],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [92, 194, 148, 232],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [84, 190, 140, 228],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [94, 192, 150, 230],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.79\n    }\n  ]\n}"}], "video": "data/videos/data02_seq_000.mp4"}, {"id": "train_data26_seq_035", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [316, 316, 376, 368],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.7\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [307, 313, 367, 365],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [303, 313, 363, 365],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [297, 304, 357, 356],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [303, 298, 363, 350],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.89\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_035.mp4"}, {"id": "train_data26_seq_001", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [230, 311, 270, 335],\n      \"class_name\": \"car\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [222, 312, 262, 336],\n      \"class_name\": \"car\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [218, 315, 258, 339],\n      \"class_name\": \"car\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [225, 318, 265, 342],\n      \"class_name\": \"car\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [224, 318, 264, 342],\n      \"class_name\": \"car\",\n      \"confidence\": 0.72\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_001.mp4"}, {"id": "train_data07_seq_010", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [187, 63, 241, 89],\n      \"class_name\": \"car\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [187, 56, 241, 82],\n      \"class_name\": \"car\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [177, 54, 231, 80],\n      \"class_name\": \"car\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [169, 59, 223, 85],\n      \"class_name\": \"car\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [161, 55, 215, 81],\n      \"class_name\": \"car\",\n      \"confidence\": 0.76\n    }\n  ]\n}"}], "video": "data/videos/data07_seq_010.mp4"}, {"id": "train_data02_seq_003", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [161, 113, 189, 155],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [155, 109, 183, 151],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [150, 116, 178, 158],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [142, 111, 170, 153],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [145, 110, 173, 152],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.83\n    }\n  ]\n}"}], "video": "data/videos/data02_seq_003.mp4"}, {"id": "train_data25_seq_012", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [344, 141, 364, 195],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [340, 141, 360, 195],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [343, 146, 363, 200],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [333, 138, 353, 192],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [333, 132, 353, 186],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.83\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_012.mp4"}, {"id": "train_data26_seq_054", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [263, 232, 323, 266],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [263, 229, 323, 263],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [255, 223, 315, 257],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [248, 224, 308, 258],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [244, 216, 304, 250],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.94\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_054.mp4"}, {"id": "train_data04_seq_007", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [170, 182, 230, 232],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [171, 180, 231, 230],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [170, 175, 230, 225],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [166, 184, 226, 234],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [157, 189, 217, 239],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.76\n    }\n  ]\n}"}], "video": "data/videos/data04_seq_007.mp4"}, {"id": "train_data25_seq_032", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [258, 254, 314, 314],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [249, 252, 305, 312],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [243, 258, 299, 318],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [244, 256, 300, 316],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [243, 260, 299, 320],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.73\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_032.mp4"}, {"id": "train_data02_seq_004", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [110, 128, 142, 172],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [103, 135, 135, 179],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [93, 144, 125, 188],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [83, 137, 115, 181],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [84, 135, 116, 179],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.8\n    }\n  ]\n}"}], "video": "data/videos/data02_seq_004.mp4"}, {"id": "train_data25_seq_051", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [352, 195, 402, 237],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [360, 189, 410, 231],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [360, 186, 410, 228],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [354, 196, 404, 238],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [355, 196, 405, 238],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.83\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_051.mp4"}, {"id": "train_data07_seq_001", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [139, 50, 187, 102],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [142, 40, 190, 92],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [137, 46, 185, 98],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [139, 40, 187, 92],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [138, 38, 186, 90],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.86\n    }\n  ]\n}"}], "video": "data/videos/data07_seq_001.mp4"}, {"id": "train_data25_seq_001", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [343, 272, 395, 324],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [349, 267, 401, 319],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [343, 262, 395, 314],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.7\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [336, 271, 388, 323],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [346, 265, 398, 317],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.78\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_001.mp4"}, {"id": "train_data06_seq_000", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [183, 153, 237, 187],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [181, 159, 235, 193],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [190, 167, 244, 201],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [191, 160, 245, 194],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [181, 158, 235, 192],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.8\n    }\n  ]\n}"}], "video": "data/videos/data06_seq_000.mp4"}, {"id": "train_data07_seq_009", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [76, 147, 134, 169],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [81, 139, 139, 161],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [84, 147, 142, 169],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [75, 150, 133, 172],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [67, 153, 125, 175],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.81\n    }\n  ]\n}"}], "video": "data/videos/data07_seq_009.mp4"}, {"id": "train_data05_seq_005", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [37, 100, 61, 124],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [31, 107, 55, 131],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [25, 107, 49, 131],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [18, 101, 42, 125],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [20, 109, 44, 133],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.83\n    }\n  ]\n}"}], "video": "data/videos/data05_seq_005.mp4"}, {"id": "train_data26_seq_010", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [280, 188, 336, 214],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [283, 183, 339, 209],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [293, 175, 349, 201],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [294, 171, 350, 197],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [288, 169, 344, 195],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.8\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_010.mp4"}, {"id": "train_data02_seq_025", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [115, 140, 151, 164],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [120, 130, 156, 154],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [113, 129, 149, 153],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [123, 120, 159, 144],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [115, 114, 151, 138],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.77\n    }\n  ]\n}"}], "video": "data/videos/data02_seq_025.mp4"}, {"id": "train_data25_seq_017", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [408, 215, 434, 253],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [418, 207, 444, 245],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [408, 209, 434, 247],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [399, 211, 425, 249],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [409, 218, 435, 256],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.72\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_017.mp4"}, {"id": "train_data25_seq_030", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [258, 223, 278, 273],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [254, 215, 274, 265],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [257, 220, 277, 270],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [265, 222, 285, 272],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [257, 229, 277, 279],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.91\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_030.mp4"}, {"id": "train_data07_seq_014", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [123, 134, 159, 174],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [126, 138, 162, 178],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [131, 148, 167, 188],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [125, 152, 161, 192],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [126, 155, 162, 195],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.75\n    }\n  ]\n}"}], "video": "data/videos/data07_seq_014.mp4"}, {"id": "train_data25_seq_039", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [386, 264, 406, 322],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [380, 266, 400, 324],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [382, 259, 402, 317],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [381, 251, 401, 309],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [388, 247, 408, 305],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.88\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_039.mp4"}, {"id": "train_data05_seq_012", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [123, 134, 159, 174],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [126, 138, 162, 178],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [131, 148, 167, 188],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [125, 152, 161, 192],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [126, 155, 162, 195],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.75\n    }\n  ]\n}"}], "video": "data/videos/data05_seq_012.mp4"}, {"id": "train_data02_seq_015", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [181, 48, 241, 100],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [187, 58, 247, 110],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [179, 54, 239, 106],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [183, 53, 243, 105],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [190, 63, 250, 115],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.87\n    }\n  ]\n}"}], "video": "data/videos/data02_seq_015.mp4"}, {"id": "train_data02_seq_012", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [10, 123, 38, 171],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [16, 131, 44, 179],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [20, 126, 48, 174],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [24, 135, 52, 183],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [14, 139, 42, 187],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.74\n    }\n  ]\n}"}], "video": "data/videos/data02_seq_012.mp4"}, {"id": "train_data04_seq_005", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [16, 66, 36, 120],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.7\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [12, 68, 32, 122],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [14, 67, 34, 121],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [9, 64, 29, 118],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [12, 68, 32, 122],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.93\n    }\n  ]\n}"}], "video": "data/videos/data04_seq_005.mp4"}, {"id": "train_data25_seq_015", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [198, 284, 256, 328],\n      \"class_name\": \"car\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [205, 284, 263, 328],\n      \"class_name\": \"car\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [207, 281, 265, 325],\n      \"class_name\": \"car\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [206, 273, 264, 317],\n      \"class_name\": \"car\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [204, 264, 262, 308],\n      \"class_name\": \"car\",\n      \"confidence\": 0.95\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_015.mp4"}, {"id": "train_data25_seq_037", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [369, 195, 411, 247],\n      \"class_name\": \"car\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [369, 186, 411, 238],\n      \"class_name\": \"car\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [370, 180, 412, 232],\n      \"class_name\": \"car\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [378, 177, 420, 229],\n      \"class_name\": \"car\",\n      \"confidence\": 0.7\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [378, 171, 420, 223],\n      \"class_name\": \"car\",\n      \"confidence\": 0.89\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_037.mp4"}, {"id": "train_data05_seq_003", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [37, 114, 73, 170],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [32, 113, 68, 169],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [36, 108, 72, 164],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [46, 100, 82, 156],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [44, 93, 80, 149],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.83\n    }\n  ]\n}"}], "video": "data/videos/data05_seq_003.mp4"}, {"id": "train_data25_seq_042", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [232, 323, 252, 365],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [224, 329, 244, 371],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [214, 339, 234, 381],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [205, 331, 225, 373],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [213, 341, 233, 383],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.94\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_042.mp4"}, {"id": "train_data07_seq_007", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [49, 90, 81, 134],\n      \"class_name\": \"car\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [39, 81, 71, 125],\n      \"class_name\": \"car\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [32, 71, 64, 115],\n      \"class_name\": \"car\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [39, 75, 71, 119],\n      \"class_name\": \"car\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [30, 67, 62, 111],\n      \"class_name\": \"car\",\n      \"confidence\": 0.95\n    }\n  ]\n}"}], "video": "data/videos/data07_seq_007.mp4"}, {"id": "train_data07_seq_012", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [66, 76, 86, 110],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [67, 75, 87, 109],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [60, 68, 80, 102],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [66, 73, 86, 107],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [59, 70, 79, 104],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.77\n    }\n  ]\n}"}], "video": "data/videos/data07_seq_012.mp4"}, {"id": "train_data02_seq_026", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [169, 162, 205, 220],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [174, 169, 210, 227],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [177, 175, 213, 233],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [169, 176, 205, 234],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [175, 170, 211, 228],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.81\n    }\n  ]\n}"}], "video": "data/videos/data02_seq_026.mp4"}, {"id": "train_data26_seq_005", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [331, 285, 371, 333],\n      \"class_name\": \"car\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [322, 277, 362, 325],\n      \"class_name\": \"car\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [328, 287, 368, 335],\n      \"class_name\": \"car\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [322, 290, 362, 338],\n      \"class_name\": \"car\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [322, 282, 362, 330],\n      \"class_name\": \"car\",\n      \"confidence\": 0.76\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_005.mp4"}, {"id": "train_data26_seq_017", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [297, 329, 321, 385],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [300, 339, 324, 395],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [295, 331, 319, 387],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [293, 336, 317, 392],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [292, 341, 316, 397],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.79\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_017.mp4"}, {"id": "train_data04_seq_003", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [140, 9, 182, 39],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [150, 4, 192, 34],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [160, 14, 202, 44],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [162, 11, 204, 41],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [170, 3, 212, 33],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.92\n    }\n  ]\n}"}], "video": "data/videos/data04_seq_003.mp4"}, {"id": "train_data25_seq_000", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [317, 287, 345, 343],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [322, 291, 350, 347],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [317, 299, 345, 355],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [314, 299, 342, 355],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [323, 308, 351, 364],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.86\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_000.mp4"}, {"id": "train_data26_seq_038", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [283, 298, 305, 318],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [283, 295, 305, 315],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [290, 293, 312, 313],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [299, 285, 321, 305],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [291, 284, 313, 304],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.87\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_038.mp4"}, {"id": "train_data26_seq_053", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [292, 316, 312, 374],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [295, 316, 315, 374],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [289, 315, 309, 373],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [287, 307, 307, 365],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [277, 308, 297, 366],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.83\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_053.mp4"}, {"id": "train_data02_seq_016", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [105, 201, 129, 256],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [108, 211, 132, 256],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [103, 203, 127, 256],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [101, 208, 125, 256],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [100, 213, 124, 256],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.79\n    }\n  ]\n}"}], "video": "data/videos/data02_seq_016.mp4"}, {"id": "train_data06_seq_013", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [40, 54, 74, 94],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [40, 46, 74, 86],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [46, 56, 80, 96],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [54, 56, 88, 96],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [50, 46, 84, 86],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.85\n    }\n  ]\n}"}], "video": "data/videos/data06_seq_013.mp4"}, {"id": "train_data26_seq_022", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [350, 254, 384, 294],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [354, 251, 388, 291],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [352, 256, 386, 296],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [347, 262, 381, 302],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [349, 266, 383, 306],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.73\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_022.mp4"}, {"id": "train_data25_seq_057", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [300, 318, 324, 348],\n      \"class_name\": \"car\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [297, 320, 321, 350],\n      \"class_name\": \"car\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [292, 311, 316, 341],\n      \"class_name\": \"car\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [283, 316, 307, 346],\n      \"class_name\": \"car\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [282, 320, 306, 350],\n      \"class_name\": \"car\",\n      \"confidence\": 0.87\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_057.mp4"}, {"id": "train_data07_seq_005", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [70, 82, 118, 140],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [69, 73, 117, 131],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [68, 76, 116, 134],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [67, 79, 115, 137],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [58, 80, 106, 138],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.77\n    }\n  ]\n}"}], "video": "data/videos/data07_seq_005.mp4"}, {"id": "train_data26_seq_011", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [203, 226, 239, 252],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [209, 223, 245, 249],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [209, 222, 245, 248],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [211, 215, 247, 241],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [218, 215, 254, 241],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.92\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_011.mp4"}, {"id": "train_data26_seq_037", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [350, 195, 392, 251],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [355, 200, 397, 256],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [358, 194, 400, 250],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [363, 193, 405, 249],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [354, 194, 396, 250],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.8\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_037.mp4"}, {"id": "train_data25_seq_058", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [299, 248, 357, 304],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [302, 245, 360, 301],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [306, 242, 364, 298],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [311, 234, 369, 290],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [312, 236, 370, 292],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.82\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_058.mp4"}, {"id": "train_data02_seq_029", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [56, 206, 106, 244],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [49, 204, 99, 242],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [52, 203, 102, 241],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [54, 198, 104, 236],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [58, 197, 108, 235],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.89\n    }\n  ]\n}"}], "video": "data/videos/data02_seq_029.mp4"}, {"id": "train_data02_seq_007", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [214, 130, 240, 150],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [215, 135, 241, 155],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [209, 131, 235, 151],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [216, 139, 242, 159],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [209, 135, 235, 155],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.74\n    }\n  ]\n}"}], "video": "data/videos/data02_seq_007.mp4"}, {"id": "train_data05_seq_001", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [27, 121, 51, 151],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [32, 129, 56, 159],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [26, 126, 50, 156],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [30, 119, 54, 149],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [36, 117, 60, 147],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.76\n    }\n  ]\n}"}], "video": "data/videos/data05_seq_001.mp4"}, {"id": "train_data26_seq_036", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [221, 160, 263, 190],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [230, 151, 272, 181],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [225, 154, 267, 184],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [231, 155, 273, 185],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [235, 161, 277, 191],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.77\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_036.mp4"}, {"id": "train_data26_seq_023", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [269, 164, 297, 218],\n      \"class_name\": \"car\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [265, 171, 293, 225],\n      \"class_name\": \"car\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [264, 180, 292, 234],\n      \"class_name\": \"car\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [258, 188, 286, 242],\n      \"class_name\": \"car\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [263, 192, 291, 246],\n      \"class_name\": \"car\",\n      \"confidence\": 0.71\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_023.mp4"}, {"id": "train_data02_seq_023", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [122, 63, 172, 99],\n      \"class_name\": \"car\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [130, 53, 180, 89],\n      \"class_name\": \"car\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [135, 44, 185, 80],\n      \"class_name\": \"car\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [129, 40, 179, 76],\n      \"class_name\": \"car\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [135, 35, 185, 71],\n      \"class_name\": \"car\",\n      \"confidence\": 0.83\n    }\n  ]\n}"}], "video": "data/videos/data02_seq_023.mp4"}, {"id": "train_data26_seq_057", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [338, 281, 366, 333],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [340, 287, 368, 339],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [344, 281, 372, 333],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [354, 285, 382, 337],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [362, 291, 390, 343],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.86\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_057.mp4"}, {"id": "train_data25_seq_047", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [296, 191, 338, 245],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [297, 184, 339, 238],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [292, 177, 334, 231],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [298, 184, 340, 238],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [307, 189, 349, 243],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.86\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_047.mp4"}]