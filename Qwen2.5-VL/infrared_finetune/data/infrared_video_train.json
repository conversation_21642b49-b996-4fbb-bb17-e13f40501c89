[{"id": "train_0000", "video": "data/videos/data25_seq_008.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频中的目标。"}, {"from": "gpt", "value": "我看到了红外视频中的目标物体，正在进行跟踪分析。"}]}, {"id": "train_0001", "video": "data/videos/data26_seq_029.mp4", "conversations": [{"from": "human", "value": "<video>\n描述视频中的运动目标。"}, {"from": "gpt", "value": "视频中显示了运动的目标，具有明显的热特征。"}]}, {"id": "train_0002", "video": "data/videos/data25_seq_025.mp4", "conversations": [{"from": "human", "value": "<video>\n这个红外视频显示了什么？"}, {"from": "gpt", "value": "这是一个红外热成像视频，显示了目标的热辐射特征。"}]}, {"id": "train_0003", "video": "data/videos/data06_seq_007.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频中的目标。"}, {"from": "gpt", "value": "我看到了红外视频中的目标物体，正在进行跟踪分析。"}]}, {"id": "train_0004", "video": "data/videos/data05_seq_004.mp4", "conversations": [{"from": "human", "value": "<video>\n描述视频中的运动目标。"}, {"from": "gpt", "value": "视频中显示了运动的目标，具有明显的热特征。"}]}, {"id": "train_0005", "video": "data/videos/data25_seq_040.mp4", "conversations": [{"from": "human", "value": "<video>\n这个红外视频显示了什么？"}, {"from": "gpt", "value": "这是一个红外热成像视频，显示了目标的热辐射特征。"}]}, {"id": "train_0006", "video": "data/videos/data25_seq_056.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频中的目标。"}, {"from": "gpt", "value": "我看到了红外视频中的目标物体，正在进行跟踪分析。"}]}, {"id": "train_0007", "video": "data/videos/data07_seq_011.mp4", "conversations": [{"from": "human", "value": "<video>\n描述视频中的运动目标。"}, {"from": "gpt", "value": "视频中显示了运动的目标，具有明显的热特征。"}]}, {"id": "train_0008", "video": "data/videos/data25_seq_021.mp4", "conversations": [{"from": "human", "value": "<video>\n这个红外视频显示了什么？"}, {"from": "gpt", "value": "这是一个红外热成像视频，显示了目标的热辐射特征。"}]}, {"id": "train_0009", "video": "data/videos/data26_seq_004.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频中的目标。"}, {"from": "gpt", "value": "我看到了红外视频中的目标物体，正在进行跟踪分析。"}]}, {"id": "train_0010", "video": "data/videos/data25_seq_045.mp4", "conversations": [{"from": "human", "value": "<video>\n描述视频中的运动目标。"}, {"from": "gpt", "value": "视频中显示了运动的目标，具有明显的热特征。"}]}, {"id": "train_0011", "video": "data/videos/data25_seq_006.mp4", "conversations": [{"from": "human", "value": "<video>\n这个红外视频显示了什么？"}, {"from": "gpt", "value": "这是一个红外热成像视频，显示了目标的热辐射特征。"}]}, {"id": "train_0012", "video": "data/videos/data04_seq_014.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频中的目标。"}, {"from": "gpt", "value": "我看到了红外视频中的目标物体，正在进行跟踪分析。"}]}, {"id": "train_0013", "video": "data/videos/data07_seq_004.mp4", "conversations": [{"from": "human", "value": "<video>\n描述视频中的运动目标。"}, {"from": "gpt", "value": "视频中显示了运动的目标，具有明显的热特征。"}]}, {"id": "train_0014", "video": "data/videos/data25_seq_054.mp4", "conversations": [{"from": "human", "value": "<video>\n这个红外视频显示了什么？"}, {"from": "gpt", "value": "这是一个红外热成像视频，显示了目标的热辐射特征。"}]}, {"id": "train_0015", "video": "data/videos/data04_seq_002.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频中的目标。"}, {"from": "gpt", "value": "我看到了红外视频中的目标物体，正在进行跟踪分析。"}]}, {"id": "train_0016", "video": "data/videos/data02_seq_014.mp4", "conversations": [{"from": "human", "value": "<video>\n描述视频中的运动目标。"}, {"from": "gpt", "value": "视频中显示了运动的目标，具有明显的热特征。"}]}, {"id": "train_0017", "video": "data/videos/data26_seq_013.mp4", "conversations": [{"from": "human", "value": "<video>\n这个红外视频显示了什么？"}, {"from": "gpt", "value": "这是一个红外热成像视频，显示了目标的热辐射特征。"}]}, {"id": "train_0018", "video": "data/videos/data02_seq_009.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频中的目标。"}, {"from": "gpt", "value": "我看到了红外视频中的目标物体，正在进行跟踪分析。"}]}, {"id": "train_0019", "video": "data/videos/data25_seq_013.mp4", "conversations": [{"from": "human", "value": "<video>\n描述视频中的运动目标。"}, {"from": "gpt", "value": "视频中显示了运动的目标，具有明显的热特征。"}]}, {"id": "train_0020", "video": "data/videos/data25_seq_005.mp4", "conversations": [{"from": "human", "value": "<video>\n这个红外视频显示了什么？"}, {"from": "gpt", "value": "这是一个红外热成像视频，显示了目标的热辐射特征。"}]}, {"id": "train_0021", "video": "data/videos/data25_seq_043.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频中的目标。"}, {"from": "gpt", "value": "我看到了红外视频中的目标物体，正在进行跟踪分析。"}]}, {"id": "train_0022", "video": "data/videos/data25_seq_049.mp4", "conversations": [{"from": "human", "value": "<video>\n描述视频中的运动目标。"}, {"from": "gpt", "value": "视频中显示了运动的目标，具有明显的热特征。"}]}, {"id": "train_0023", "video": "data/videos/data25_seq_052.mp4", "conversations": [{"from": "human", "value": "<video>\n这个红外视频显示了什么？"}, {"from": "gpt", "value": "这是一个红外热成像视频，显示了目标的热辐射特征。"}]}, {"id": "train_0024", "video": "data/videos/data26_seq_020.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频中的目标。"}, {"from": "gpt", "value": "我看到了红外视频中的目标物体，正在进行跟踪分析。"}]}, {"id": "train_0025", "video": "data/videos/data26_seq_044.mp4", "conversations": [{"from": "human", "value": "<video>\n描述视频中的运动目标。"}, {"from": "gpt", "value": "视频中显示了运动的目标，具有明显的热特征。"}]}, {"id": "train_0026", "video": "data/videos/data07_seq_000.mp4", "conversations": [{"from": "human", "value": "<video>\n这个红外视频显示了什么？"}, {"from": "gpt", "value": "这是一个红外热成像视频，显示了目标的热辐射特征。"}]}, {"id": "train_0027", "video": "data/videos/data26_seq_030.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频中的目标。"}, {"from": "gpt", "value": "我看到了红外视频中的目标物体，正在进行跟踪分析。"}]}, {"id": "train_0028", "video": "data/videos/data25_seq_033.mp4", "conversations": [{"from": "human", "value": "<video>\n描述视频中的运动目标。"}, {"from": "gpt", "value": "视频中显示了运动的目标，具有明显的热特征。"}]}, {"id": "train_0029", "video": "data/videos/data26_seq_024.mp4", "conversations": [{"from": "human", "value": "<video>\n这个红外视频显示了什么？"}, {"from": "gpt", "value": "这是一个红外热成像视频，显示了目标的热辐射特征。"}]}, {"id": "train_0030", "video": "data/videos/data02_seq_017.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频中的目标。"}, {"from": "gpt", "value": "我看到了红外视频中的目标物体，正在进行跟踪分析。"}]}, {"id": "train_0031", "video": "data/videos/data05_seq_000.mp4", "conversations": [{"from": "human", "value": "<video>\n描述视频中的运动目标。"}, {"from": "gpt", "value": "视频中显示了运动的目标，具有明显的热特征。"}]}, {"id": "train_0032", "video": "data/videos/data25_seq_020.mp4", "conversations": [{"from": "human", "value": "<video>\n这个红外视频显示了什么？"}, {"from": "gpt", "value": "这是一个红外热成像视频，显示了目标的热辐射特征。"}]}, {"id": "train_0033", "video": "data/videos/data06_seq_003.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频中的目标。"}, {"from": "gpt", "value": "我看到了红外视频中的目标物体，正在进行跟踪分析。"}]}, {"id": "train_0034", "video": "data/videos/data25_seq_048.mp4", "conversations": [{"from": "human", "value": "<video>\n描述视频中的运动目标。"}, {"from": "gpt", "value": "视频中显示了运动的目标，具有明显的热特征。"}]}, {"id": "train_0035", "video": "data/videos/data25_seq_023.mp4", "conversations": [{"from": "human", "value": "<video>\n这个红外视频显示了什么？"}, {"from": "gpt", "value": "这是一个红外热成像视频，显示了目标的热辐射特征。"}]}, {"id": "train_0036", "video": "data/videos/data25_seq_022.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频中的目标。"}, {"from": "gpt", "value": "我看到了红外视频中的目标物体，正在进行跟踪分析。"}]}, {"id": "train_0037", "video": "data/videos/data05_seq_010.mp4", "conversations": [{"from": "human", "value": "<video>\n描述视频中的运动目标。"}, {"from": "gpt", "value": "视频中显示了运动的目标，具有明显的热特征。"}]}, {"id": "train_0038", "video": "data/videos/data04_seq_006.mp4", "conversations": [{"from": "human", "value": "<video>\n这个红外视频显示了什么？"}, {"from": "gpt", "value": "这是一个红外热成像视频，显示了目标的热辐射特征。"}]}, {"id": "train_0039", "video": "data/videos/data25_seq_009.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频中的目标。"}, {"from": "gpt", "value": "我看到了红外视频中的目标物体，正在进行跟踪分析。"}]}, {"id": "train_0040", "video": "data/videos/data26_seq_048.mp4", "conversations": [{"from": "human", "value": "<video>\n描述视频中的运动目标。"}, {"from": "gpt", "value": "视频中显示了运动的目标，具有明显的热特征。"}]}, {"id": "train_0041", "video": "data/videos/data25_seq_059.mp4", "conversations": [{"from": "human", "value": "<video>\n这个红外视频显示了什么？"}, {"from": "gpt", "value": "这是一个红外热成像视频，显示了目标的热辐射特征。"}]}, {"id": "train_0042", "video": "data/videos/data26_seq_045.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频中的目标。"}, {"from": "gpt", "value": "我看到了红外视频中的目标物体，正在进行跟踪分析。"}]}, {"id": "train_0043", "video": "data/videos/data06_seq_004.mp4", "conversations": [{"from": "human", "value": "<video>\n描述视频中的运动目标。"}, {"from": "gpt", "value": "视频中显示了运动的目标，具有明显的热特征。"}]}, {"id": "train_0044", "video": "data/videos/data07_seq_002.mp4", "conversations": [{"from": "human", "value": "<video>\n这个红外视频显示了什么？"}, {"from": "gpt", "value": "这是一个红外热成像视频，显示了目标的热辐射特征。"}]}, {"id": "train_0045", "video": "data/videos/data25_seq_018.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频中的目标。"}, {"from": "gpt", "value": "我看到了红外视频中的目标物体，正在进行跟踪分析。"}]}, {"id": "train_0046", "video": "data/videos/data06_seq_014.mp4", "conversations": [{"from": "human", "value": "<video>\n描述视频中的运动目标。"}, {"from": "gpt", "value": "视频中显示了运动的目标，具有明显的热特征。"}]}, {"id": "train_0047", "video": "data/videos/data04_seq_010.mp4", "conversations": [{"from": "human", "value": "<video>\n这个红外视频显示了什么？"}, {"from": "gpt", "value": "这是一个红外热成像视频，显示了目标的热辐射特征。"}]}, {"id": "train_0048", "video": "data/videos/data26_seq_034.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频中的目标。"}, {"from": "gpt", "value": "我看到了红外视频中的目标物体，正在进行跟踪分析。"}]}, {"id": "train_0049", "video": "data/videos/data25_seq_038.mp4", "conversations": [{"from": "human", "value": "<video>\n描述视频中的运动目标。"}, {"from": "gpt", "value": "视频中显示了运动的目标，具有明显的热特征。"}]}, {"id": "train_0050", "video": "data/videos/data02_seq_027.mp4", "conversations": [{"from": "human", "value": "<video>\n这个红外视频显示了什么？"}, {"from": "gpt", "value": "这是一个红外热成像视频，显示了目标的热辐射特征。"}]}, {"id": "train_0051", "video": "data/videos/data26_seq_018.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频中的目标。"}, {"from": "gpt", "value": "我看到了红外视频中的目标物体，正在进行跟踪分析。"}]}, {"id": "train_0052", "video": "data/videos/data02_seq_019.mp4", "conversations": [{"from": "human", "value": "<video>\n描述视频中的运动目标。"}, {"from": "gpt", "value": "视频中显示了运动的目标，具有明显的热特征。"}]}, {"id": "train_0053", "video": "data/videos/data07_seq_008.mp4", "conversations": [{"from": "human", "value": "<video>\n这个红外视频显示了什么？"}, {"from": "gpt", "value": "这是一个红外热成像视频，显示了目标的热辐射特征。"}]}, {"id": "train_0054", "video": "data/videos/data02_seq_028.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频中的目标。"}, {"from": "gpt", "value": "我看到了红外视频中的目标物体，正在进行跟踪分析。"}]}, {"id": "train_0055", "video": "data/videos/data06_seq_001.mp4", "conversations": [{"from": "human", "value": "<video>\n描述视频中的运动目标。"}, {"from": "gpt", "value": "视频中显示了运动的目标，具有明显的热特征。"}]}, {"id": "train_0056", "video": "data/videos/data02_seq_005.mp4", "conversations": [{"from": "human", "value": "<video>\n这个红外视频显示了什么？"}, {"from": "gpt", "value": "这是一个红外热成像视频，显示了目标的热辐射特征。"}]}, {"id": "train_0057", "video": "data/videos/data05_seq_008.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频中的目标。"}, {"from": "gpt", "value": "我看到了红外视频中的目标物体，正在进行跟踪分析。"}]}, {"id": "train_0058", "video": "data/videos/data26_seq_014.mp4", "conversations": [{"from": "human", "value": "<video>\n描述视频中的运动目标。"}, {"from": "gpt", "value": "视频中显示了运动的目标，具有明显的热特征。"}]}, {"id": "train_0059", "video": "data/videos/data02_seq_002.mp4", "conversations": [{"from": "human", "value": "<video>\n这个红外视频显示了什么？"}, {"from": "gpt", "value": "这是一个红外热成像视频，显示了目标的热辐射特征。"}]}, {"id": "train_0060", "video": "data/videos/data04_seq_013.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频中的目标。"}, {"from": "gpt", "value": "我看到了红外视频中的目标物体，正在进行跟踪分析。"}]}, {"id": "train_0061", "video": "data/videos/data26_seq_049.mp4", "conversations": [{"from": "human", "value": "<video>\n描述视频中的运动目标。"}, {"from": "gpt", "value": "视频中显示了运动的目标，具有明显的热特征。"}]}, {"id": "train_0062", "video": "data/videos/data04_seq_009.mp4", "conversations": [{"from": "human", "value": "<video>\n这个红外视频显示了什么？"}, {"from": "gpt", "value": "这是一个红外热成像视频，显示了目标的热辐射特征。"}]}, {"id": "train_0063", "video": "data/videos/data25_seq_002.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频中的目标。"}, {"from": "gpt", "value": "我看到了红外视频中的目标物体，正在进行跟踪分析。"}]}, {"id": "train_0064", "video": "data/videos/data02_seq_008.mp4", "conversations": [{"from": "human", "value": "<video>\n描述视频中的运动目标。"}, {"from": "gpt", "value": "视频中显示了运动的目标，具有明显的热特征。"}]}, {"id": "train_0065", "video": "data/videos/data26_seq_059.mp4", "conversations": [{"from": "human", "value": "<video>\n这个红外视频显示了什么？"}, {"from": "gpt", "value": "这是一个红外热成像视频，显示了目标的热辐射特征。"}]}, {"id": "train_0066", "video": "data/videos/data26_seq_050.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频中的目标。"}, {"from": "gpt", "value": "我看到了红外视频中的目标物体，正在进行跟踪分析。"}]}, {"id": "train_0067", "video": "data/videos/data25_seq_036.mp4", "conversations": [{"from": "human", "value": "<video>\n描述视频中的运动目标。"}, {"from": "gpt", "value": "视频中显示了运动的目标，具有明显的热特征。"}]}, {"id": "train_0068", "video": "data/videos/data25_seq_034.mp4", "conversations": [{"from": "human", "value": "<video>\n这个红外视频显示了什么？"}, {"from": "gpt", "value": "这是一个红外热成像视频，显示了目标的热辐射特征。"}]}, {"id": "train_0069", "video": "data/videos/data02_seq_022.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频中的目标。"}, {"from": "gpt", "value": "我看到了红外视频中的目标物体，正在进行跟踪分析。"}]}, {"id": "train_0070", "video": "data/videos/data05_seq_014.mp4", "conversations": [{"from": "human", "value": "<video>\n描述视频中的运动目标。"}, {"from": "gpt", "value": "视频中显示了运动的目标，具有明显的热特征。"}]}, {"id": "train_0071", "video": "data/videos/data26_seq_046.mp4", "conversations": [{"from": "human", "value": "<video>\n这个红外视频显示了什么？"}, {"from": "gpt", "value": "这是一个红外热成像视频，显示了目标的热辐射特征。"}]}, {"id": "train_0072", "video": "data/videos/data25_seq_029.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频中的目标。"}, {"from": "gpt", "value": "我看到了红外视频中的目标物体，正在进行跟踪分析。"}]}, {"id": "train_0073", "video": "data/videos/data06_seq_010.mp4", "conversations": [{"from": "human", "value": "<video>\n描述视频中的运动目标。"}, {"from": "gpt", "value": "视频中显示了运动的目标，具有明显的热特征。"}]}, {"id": "train_0074", "video": "data/videos/data25_seq_050.mp4", "conversations": [{"from": "human", "value": "<video>\n这个红外视频显示了什么？"}, {"from": "gpt", "value": "这是一个红外热成像视频，显示了目标的热辐射特征。"}]}, {"id": "train_0075", "video": "data/videos/data06_seq_006.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频中的目标。"}, {"from": "gpt", "value": "我看到了红外视频中的目标物体，正在进行跟踪分析。"}]}, {"id": "train_0076", "video": "data/videos/data25_seq_014.mp4", "conversations": [{"from": "human", "value": "<video>\n描述视频中的运动目标。"}, {"from": "gpt", "value": "视频中显示了运动的目标，具有明显的热特征。"}]}, {"id": "train_0077", "video": "data/videos/data26_seq_019.mp4", "conversations": [{"from": "human", "value": "<video>\n这个红外视频显示了什么？"}, {"from": "gpt", "value": "这是一个红外热成像视频，显示了目标的热辐射特征。"}]}, {"id": "train_0078", "video": "data/videos/data25_seq_016.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频中的目标。"}, {"from": "gpt", "value": "我看到了红外视频中的目标物体，正在进行跟踪分析。"}]}, {"id": "train_0079", "video": "data/videos/data02_seq_011.mp4", "conversations": [{"from": "human", "value": "<video>\n描述视频中的运动目标。"}, {"from": "gpt", "value": "视频中显示了运动的目标，具有明显的热特征。"}]}, {"id": "train_0080", "video": "data/videos/data26_seq_042.mp4", "conversations": [{"from": "human", "value": "<video>\n这个红外视频显示了什么？"}, {"from": "gpt", "value": "这是一个红外热成像视频，显示了目标的热辐射特征。"}]}, {"id": "train_0081", "video": "data/videos/data06_seq_005.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频中的目标。"}, {"from": "gpt", "value": "我看到了红外视频中的目标物体，正在进行跟踪分析。"}]}, {"id": "train_0082", "video": "data/videos/data06_seq_012.mp4", "conversations": [{"from": "human", "value": "<video>\n描述视频中的运动目标。"}, {"from": "gpt", "value": "视频中显示了运动的目标，具有明显的热特征。"}]}, {"id": "train_0083", "video": "data/videos/data26_seq_031.mp4", "conversations": [{"from": "human", "value": "<video>\n这个红外视频显示了什么？"}, {"from": "gpt", "value": "这是一个红外热成像视频，显示了目标的热辐射特征。"}]}, {"id": "train_0084", "video": "data/videos/data25_seq_026.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频中的目标。"}, {"from": "gpt", "value": "我看到了红外视频中的目标物体，正在进行跟踪分析。"}]}, {"id": "train_0085", "video": "data/videos/data25_seq_003.mp4", "conversations": [{"from": "human", "value": "<video>\n描述视频中的运动目标。"}, {"from": "gpt", "value": "视频中显示了运动的目标，具有明显的热特征。"}]}, {"id": "train_0086", "video": "data/videos/data06_seq_002.mp4", "conversations": [{"from": "human", "value": "<video>\n这个红外视频显示了什么？"}, {"from": "gpt", "value": "这是一个红外热成像视频，显示了目标的热辐射特征。"}]}, {"id": "train_0087", "video": "data/videos/data25_seq_031.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频中的目标。"}, {"from": "gpt", "value": "我看到了红外视频中的目标物体，正在进行跟踪分析。"}]}, {"id": "train_0088", "video": "data/videos/data26_seq_056.mp4", "conversations": [{"from": "human", "value": "<video>\n描述视频中的运动目标。"}, {"from": "gpt", "value": "视频中显示了运动的目标，具有明显的热特征。"}]}, {"id": "train_0089", "video": "data/videos/data25_seq_046.mp4", "conversations": [{"from": "human", "value": "<video>\n这个红外视频显示了什么？"}, {"from": "gpt", "value": "这是一个红外热成像视频，显示了目标的热辐射特征。"}]}, {"id": "train_0090", "video": "data/videos/data26_seq_009.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频中的目标。"}, {"from": "gpt", "value": "我看到了红外视频中的目标物体，正在进行跟踪分析。"}]}, {"id": "train_0091", "video": "data/videos/data26_seq_000.mp4", "conversations": [{"from": "human", "value": "<video>\n描述视频中的运动目标。"}, {"from": "gpt", "value": "视频中显示了运动的目标，具有明显的热特征。"}]}, {"id": "train_0092", "video": "data/videos/data25_seq_010.mp4", "conversations": [{"from": "human", "value": "<video>\n这个红外视频显示了什么？"}, {"from": "gpt", "value": "这是一个红外热成像视频，显示了目标的热辐射特征。"}]}, {"id": "train_0093", "video": "data/videos/data26_seq_027.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频中的目标。"}, {"from": "gpt", "value": "我看到了红外视频中的目标物体，正在进行跟踪分析。"}]}, {"id": "train_0094", "video": "data/videos/data06_seq_011.mp4", "conversations": [{"from": "human", "value": "<video>\n描述视频中的运动目标。"}, {"from": "gpt", "value": "视频中显示了运动的目标，具有明显的热特征。"}]}, {"id": "train_0095", "video": "data/videos/data25_seq_007.mp4", "conversations": [{"from": "human", "value": "<video>\n这个红外视频显示了什么？"}, {"from": "gpt", "value": "这是一个红外热成像视频，显示了目标的热辐射特征。"}]}, {"id": "train_0096", "video": "data/videos/data26_seq_055.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频中的目标。"}, {"from": "gpt", "value": "我看到了红外视频中的目标物体，正在进行跟踪分析。"}]}, {"id": "train_0097", "video": "data/videos/data02_seq_024.mp4", "conversations": [{"from": "human", "value": "<video>\n描述视频中的运动目标。"}, {"from": "gpt", "value": "视频中显示了运动的目标，具有明显的热特征。"}]}, {"id": "train_0098", "video": "data/videos/data25_seq_019.mp4", "conversations": [{"from": "human", "value": "<video>\n这个红外视频显示了什么？"}, {"from": "gpt", "value": "这是一个红外热成像视频，显示了目标的热辐射特征。"}]}, {"id": "train_0099", "video": "data/videos/data07_seq_006.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频中的目标。"}, {"from": "gpt", "value": "我看到了红外视频中的目标物体，正在进行跟踪分析。"}]}]