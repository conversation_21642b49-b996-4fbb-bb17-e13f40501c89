[{"id": "train_data23_seq_015", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [282, 332, 304, 358],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [282, 325, 304, 351],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [290, 328, 312, 354],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [291, 330, 313, 356],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [298, 330, 320, 356],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.81\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_015.mp4"}, {"id": "train_data23_seq_033", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [208, 264, 230, 294],\n      \"class_name\": \"car\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [202, 265, 224, 295],\n      \"class_name\": \"car\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [202, 269, 224, 299],\n      \"class_name\": \"car\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [200, 271, 222, 301],\n      \"class_name\": \"car\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [197, 267, 219, 297],\n      \"class_name\": \"car\",\n      \"confidence\": 0.92\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_033.mp4"}, {"id": "train_data25_seq_008", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [333, 158, 375, 188],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [328, 163, 370, 193],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [325, 157, 367, 187],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [327, 167, 369, 197],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [332, 169, 374, 199],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.88\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_008.mp4"}, {"id": "train_data26_seq_029", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [362, 295, 402, 347],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [368, 301, 408, 353],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [367, 294, 407, 346],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [358, 304, 398, 356],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [361, 297, 401, 349],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.92\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_029.mp4"}, {"id": "train_data25_seq_025", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [362, 317, 404, 337],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [370, 319, 412, 339],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [372, 325, 414, 345],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [378, 315, 420, 335],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [383, 323, 425, 343],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.84\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_025.mp4"}, {"id": "train_data06_seq_007", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [74, 91, 102, 117],\n      \"class_name\": \"car\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [80, 81, 108, 107],\n      \"class_name\": \"car\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [71, 76, 99, 102],\n      \"class_name\": \"car\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [73, 76, 101, 102],\n      \"class_name\": \"car\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [81, 68, 109, 94],\n      \"class_name\": \"car\",\n      \"confidence\": 0.86\n    }\n  ]\n}"}], "video": "data/videos/data06_seq_007.mp4"}, {"id": "train_data05_seq_004", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [151, 121, 197, 149],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [148, 113, 194, 141],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [140, 107, 186, 135],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [140, 114, 186, 142],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [140, 111, 186, 139],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.75\n    }\n  ]\n}"}], "video": "data/videos/data05_seq_004.mp4"}, {"id": "train_data01_seq_040", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [45, 85, 99, 105],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [50, 88, 104, 108],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [48, 97, 102, 117],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.7\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [44, 87, 98, 107],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [43, 82, 97, 102],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.73\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_040.mp4"}, {"id": "train_data25_seq_040", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [205, 249, 265, 273],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [200, 242, 260, 266],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [208, 250, 268, 274],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [203, 249, 263, 273],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [193, 252, 253, 276],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.77\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_040.mp4"}, {"id": "train_data01_seq_022", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [114, 118, 146, 166],\n      \"class_name\": \"car\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [112, 120, 144, 168],\n      \"class_name\": \"car\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [122, 130, 154, 178],\n      \"class_name\": \"car\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [113, 134, 145, 182],\n      \"class_name\": \"car\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [104, 136, 136, 184],\n      \"class_name\": \"car\",\n      \"confidence\": 0.89\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_022.mp4"}, {"id": "train_data25_seq_056", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [273, 249, 311, 283],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [280, 239, 318, 273],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [271, 238, 309, 272],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [269, 248, 307, 282],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [277, 254, 315, 288],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.77\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_056.mp4"}, {"id": "train_data07_seq_011", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [61, 76, 117, 98],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [60, 73, 116, 95],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [69, 68, 125, 90],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [67, 77, 123, 99],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [59, 77, 115, 99],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.7\n    }\n  ]\n}"}], "video": "data/videos/data07_seq_011.mp4"}, {"id": "train_data01_seq_059", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [76, 91, 132, 147],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [78, 95, 134, 151],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [83, 89, 139, 145],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [93, 84, 149, 140],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [83, 77, 139, 133],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.79\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_059.mp4"}, {"id": "train_data25_seq_021", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [352, 140, 410, 200],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [353, 144, 411, 204],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [346, 148, 404, 208],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [345, 150, 403, 210],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [348, 154, 406, 214],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.74\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_021.mp4"}, {"id": "train_data26_seq_004", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [350, 169, 372, 193],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [352, 178, 374, 202],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [345, 180, 367, 204],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [338, 182, 360, 206],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [347, 191, 369, 215],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.92\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_004.mp4"}, {"id": "train_data01_seq_026", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [8, 214, 58, 252],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [0, 216, 48, 254],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [0, 210, 48, 248],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [6, 205, 56, 243],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [0, 210, 46, 248],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.9\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_026.mp4"}, {"id": "train_data25_seq_045", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [273, 297, 301, 319],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [265, 295, 293, 317],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [269, 295, 297, 317],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [266, 287, 294, 309],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [266, 289, 294, 311],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.72\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_045.mp4"}, {"id": "train_data01_seq_003", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [145, 32, 195, 74],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [138, 32, 188, 74],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [133, 32, 183, 74],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [124, 35, 174, 77],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [126, 40, 176, 82],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.87\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_003.mp4"}, {"id": "train_data25_seq_006", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [238, 335, 262, 371],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [243, 327, 267, 363],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [242, 320, 266, 356],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [237, 315, 261, 351],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [240, 322, 264, 358],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.71\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_006.mp4"}, {"id": "train_data01_seq_020", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [150, 148, 176, 178],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [147, 141, 173, 171],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [140, 136, 166, 166],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [139, 146, 165, 176],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [141, 146, 167, 176],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.79\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_020.mp4"}, {"id": "train_data04_seq_014", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [186, 174, 214, 198],\n      \"class_name\": \"car\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [196, 184, 224, 208],\n      \"class_name\": \"car\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [201, 185, 229, 209],\n      \"class_name\": \"car\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [199, 193, 227, 217],\n      \"class_name\": \"car\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [207, 190, 235, 214],\n      \"class_name\": \"car\",\n      \"confidence\": 0.79\n    }\n  ]\n}"}], "video": "data/videos/data04_seq_014.mp4"}, {"id": "train_data07_seq_004", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [199, 120, 256, 168],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [193, 128, 251, 176],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [190, 128, 248, 176],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [187, 128, 245, 176],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [191, 130, 249, 178],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.84\n    }\n  ]\n}"}], "video": "data/videos/data07_seq_004.mp4"}, {"id": "train_data25_seq_054", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [222, 145, 250, 205],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [214, 141, 242, 201],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [216, 150, 244, 210],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [212, 152, 240, 212],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [208, 151, 236, 211],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.91\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_054.mp4"}, {"id": "train_data04_seq_002", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [5, 85, 41, 131],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [0, 95, 34, 141],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [0, 102, 33, 148],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [0, 97, 36, 143],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [0, 89, 30, 135],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.91\n    }\n  ]\n}"}], "video": "data/videos/data04_seq_002.mp4"}, {"id": "train_data23_seq_028", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [251, 158, 287, 196],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [252, 157, 288, 195],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [250, 164, 286, 202],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [240, 167, 276, 205],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [244, 168, 280, 206],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.9\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_028.mp4"}, {"id": "train_data02_seq_014", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [89, 139, 129, 173],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [81, 144, 121, 178],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.7\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [87, 149, 127, 183],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [80, 151, 120, 185],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [86, 155, 126, 189],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.75\n    }\n  ]\n}"}], "video": "data/videos/data02_seq_014.mp4"}, {"id": "train_data26_seq_013", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [237, 269, 297, 325],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [244, 266, 304, 322],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [248, 265, 308, 321],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [241, 274, 301, 330],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [251, 275, 311, 331],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.93\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_013.mp4"}, {"id": "train_data02_seq_009", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [116, 125, 170, 183],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [119, 129, 173, 187],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [109, 120, 163, 178],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [113, 123, 167, 181],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [121, 114, 175, 172],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.89\n    }\n  ]\n}"}], "video": "data/videos/data02_seq_009.mp4"}, {"id": "train_data25_seq_013", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [287, 314, 315, 352],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [295, 316, 323, 354],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [290, 306, 318, 344],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [283, 296, 311, 334],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.7\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [292, 295, 320, 333],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.73\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_013.mp4"}, {"id": "train_data25_seq_005", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [361, 290, 397, 348],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [366, 297, 402, 355],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [369, 303, 405, 361],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [361, 304, 397, 362],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [367, 298, 403, 356],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.81\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_005.mp4"}, {"id": "train_data25_seq_043", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [363, 230, 385, 260],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [358, 229, 380, 259],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [349, 236, 371, 266],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [350, 240, 372, 270],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [344, 232, 366, 262],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.82\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_043.mp4"}, {"id": "train_data01_seq_008", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [12, 59, 42, 79],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [20, 64, 50, 84],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [16, 72, 46, 92],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [13, 81, 43, 101],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [21, 86, 51, 106],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.8\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_008.mp4"}, {"id": "train_data23_seq_031", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [395, 297, 417, 353],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [387, 295, 409, 351],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [394, 303, 416, 359],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [385, 313, 407, 369],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [391, 321, 413, 377],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.77\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_031.mp4"}, {"id": "train_data25_seq_049", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [321, 190, 367, 230],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [321, 195, 367, 235],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [316, 199, 362, 239],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [308, 195, 354, 235],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [318, 204, 364, 244],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.88\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_049.mp4"}, {"id": "train_data25_seq_052", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [320, 222, 368, 258],\n      \"class_name\": \"car\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [321, 226, 369, 262],\n      \"class_name\": \"car\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [318, 231, 366, 267],\n      \"class_name\": \"car\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [314, 227, 362, 263],\n      \"class_name\": \"car\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [314, 230, 362, 266],\n      \"class_name\": \"car\",\n      \"confidence\": 0.87\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_052.mp4"}, {"id": "train_data01_seq_013", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [65, 40, 125, 96],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [61, 37, 121, 93],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [61, 38, 121, 94],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [54, 38, 114, 94],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [53, 37, 113, 93],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.77\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_013.mp4"}, {"id": "train_data26_seq_020", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [397, 217, 425, 257],\n      \"class_name\": \"car\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [397, 216, 425, 256],\n      \"class_name\": \"car\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [407, 219, 435, 259],\n      \"class_name\": \"car\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [397, 212, 425, 252],\n      \"class_name\": \"car\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [404, 217, 432, 257],\n      \"class_name\": \"car\",\n      \"confidence\": 0.93\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_020.mp4"}, {"id": "train_data26_seq_044", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [213, 191, 247, 215],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [215, 194, 249, 218],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [212, 201, 246, 225],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [219, 201, 253, 225],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [212, 192, 246, 216],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.95\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_044.mp4"}, {"id": "train_data07_seq_000", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [124, 188, 184, 240],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.7\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [115, 185, 175, 237],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [111, 185, 171, 237],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [105, 176, 165, 228],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [111, 170, 171, 222],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.89\n    }\n  ]\n}"}], "video": "data/videos/data07_seq_000.mp4"}, {"id": "train_data26_seq_030", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [299, 248, 357, 304],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [302, 245, 360, 301],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [306, 242, 364, 298],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [311, 234, 369, 290],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [312, 236, 370, 292],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.82\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_030.mp4"}, {"id": "train_data25_seq_033", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [342, 269, 386, 315],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [342, 269, 386, 315],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [351, 274, 395, 320],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [343, 270, 387, 316],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.7\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [352, 268, 396, 314],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.84\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_033.mp4"}, {"id": "train_data26_seq_024", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [237, 269, 297, 325],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [244, 266, 304, 322],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [248, 265, 308, 321],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [241, 274, 301, 330],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [251, 275, 311, 331],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.93\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_024.mp4"}, {"id": "train_data02_seq_017", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [45, 35, 85, 55],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [52, 40, 92, 60],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [44, 39, 84, 59],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [45, 33, 85, 53],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [55, 33, 95, 53],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.76\n    }\n  ]\n}"}], "video": "data/videos/data02_seq_017.mp4"}, {"id": "train_data05_seq_000", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [79, 207, 101, 256],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [79, 203, 101, 253],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [86, 204, 108, 254],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [93, 202, 115, 252],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [95, 202, 117, 252],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.85\n    }\n  ]\n}"}], "video": "data/videos/data05_seq_000.mp4"}, {"id": "train_data25_seq_020", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [216, 201, 260, 221],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [210, 199, 254, 219],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [220, 197, 264, 217],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [211, 190, 255, 210],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [213, 188, 257, 208],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.95\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_020.mp4"}, {"id": "train_data06_seq_003", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [115, 14, 147, 46],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [123, 23, 155, 55],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [123, 23, 155, 55],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [130, 29, 162, 61],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [124, 22, 156, 54],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.76\n    }\n  ]\n}"}], "video": "data/videos/data06_seq_003.mp4"}, {"id": "train_data25_seq_048", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [278, 273, 328, 299],\n      \"class_name\": \"car\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [286, 264, 336, 290],\n      \"class_name\": \"car\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [282, 264, 332, 290],\n      \"class_name\": \"car\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [292, 263, 342, 289],\n      \"class_name\": \"car\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [293, 267, 343, 293],\n      \"class_name\": \"car\",\n      \"confidence\": 0.71\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_048.mp4"}, {"id": "train_data25_seq_023", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [232, 323, 252, 365],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [224, 329, 244, 371],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [214, 339, 234, 381],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [205, 331, 225, 373],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [213, 341, 233, 383],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.94\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_023.mp4"}, {"id": "train_data25_seq_022", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [320, 308, 356, 346],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [327, 308, 363, 346],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [329, 303, 365, 341],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [326, 308, 362, 346],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [336, 316, 372, 354],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.86\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_022.mp4"}, {"id": "train_data05_seq_010", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [66, 95, 86, 145],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [62, 87, 82, 137],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [65, 92, 85, 142],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [73, 94, 93, 144],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [65, 101, 85, 151],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.91\n    }\n  ]\n}"}], "video": "data/videos/data05_seq_010.mp4"}, {"id": "train_data04_seq_006", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [87, 8, 115, 40],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [92, 0, 120, 31],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [102, 0, 130, 29],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [109, 0, 137, 26],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [118, 3, 146, 35],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.71\n    }\n  ]\n}"}], "video": "data/videos/data04_seq_006.mp4"}, {"id": "train_data01_seq_052", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [92, 40, 148, 64],\n      \"class_name\": \"car\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [86, 47, 142, 71],\n      \"class_name\": \"car\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [89, 46, 145, 70],\n      \"class_name\": \"car\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [88, 41, 144, 65],\n      \"class_name\": \"car\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [92, 37, 148, 61],\n      \"class_name\": \"car\",\n      \"confidence\": 0.82\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_052.mp4"}, {"id": "train_data25_seq_009", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [271, 216, 323, 250],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [278, 216, 330, 250],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [281, 213, 333, 247],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [288, 217, 340, 251],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [298, 222, 350, 256],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.86\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_009.mp4"}, {"id": "train_data26_seq_048", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [203, 289, 261, 337],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [208, 286, 266, 334],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [202, 296, 260, 344],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [201, 303, 259, 351],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [197, 308, 255, 356],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.8\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_048.mp4"}, {"id": "train_data01_seq_044", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [73, 24, 129, 70],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [70, 22, 126, 68],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [75, 31, 131, 77],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [73, 34, 129, 80],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [63, 36, 119, 82],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.89\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_044.mp4"}, {"id": "train_data25_seq_059", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [220, 197, 252, 235],\n      \"class_name\": \"car\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [226, 205, 258, 243],\n      \"class_name\": \"car\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [217, 205, 249, 243],\n      \"class_name\": \"car\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [222, 205, 254, 243],\n      \"class_name\": \"car\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [219, 207, 251, 245],\n      \"class_name\": \"car\",\n      \"confidence\": 0.71\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_059.mp4"}, {"id": "train_data26_seq_045", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [230, 141, 264, 179],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [240, 147, 274, 185],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [231, 156, 265, 194],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [239, 161, 273, 199],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [245, 154, 279, 192],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.82\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_045.mp4"}, {"id": "train_data06_seq_004", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [49, 172, 97, 194],\n      \"class_name\": \"car\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [47, 166, 95, 188],\n      \"class_name\": \"car\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [41, 165, 89, 187],\n      \"class_name\": \"car\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [34, 164, 82, 186],\n      \"class_name\": \"car\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [40, 157, 88, 179],\n      \"class_name\": \"car\",\n      \"confidence\": 0.78\n    }\n  ]\n}"}], "video": "data/videos/data06_seq_004.mp4"}, {"id": "train_data07_seq_002", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [128, 43, 162, 89],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [138, 46, 172, 92],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [128, 51, 162, 97],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [137, 52, 171, 98],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [135, 45, 169, 91],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.94\n    }\n  ]\n}"}], "video": "data/videos/data07_seq_002.mp4"}, {"id": "train_data25_seq_018", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [364, 169, 394, 209],\n      \"class_name\": \"car\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [362, 168, 392, 208],\n      \"class_name\": \"car\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [363, 163, 393, 203],\n      \"class_name\": \"car\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [353, 159, 383, 199],\n      \"class_name\": \"car\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [347, 152, 377, 192],\n      \"class_name\": \"car\",\n      \"confidence\": 0.82\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_018.mp4"}, {"id": "train_data23_seq_013", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [346, 162, 402, 184],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [337, 161, 393, 183],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [343, 161, 399, 183],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [337, 153, 393, 175],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [339, 145, 395, 167],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.78\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_013.mp4"}, {"id": "train_data06_seq_014", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [52, 44, 90, 100],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [47, 34, 85, 90],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [44, 32, 82, 88],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [51, 22, 89, 78],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [47, 24, 85, 80],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.76\n    }\n  ]\n}"}], "video": "data/videos/data06_seq_014.mp4"}, {"id": "train_data04_seq_010", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [195, 30, 217, 70],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [195, 27, 217, 67],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [192, 19, 214, 59],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [182, 10, 204, 50],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [181, 11, 203, 51],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.77\n    }\n  ]\n}"}], "video": "data/videos/data04_seq_010.mp4"}, {"id": "train_data26_seq_034", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [253, 240, 277, 294],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [259, 234, 283, 288],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [259, 232, 283, 286],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [252, 226, 276, 280],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [255, 227, 279, 281],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.75\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_034.mp4"}, {"id": "train_data25_seq_038", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [240, 189, 260, 227],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [235, 198, 255, 236],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [245, 192, 265, 230],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [243, 184, 263, 222],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [238, 189, 258, 227],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.71\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_038.mp4"}, {"id": "train_data02_seq_027", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [79, 121, 105, 177],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [76, 123, 102, 179],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [82, 116, 108, 172],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.7\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [86, 124, 112, 180],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [93, 134, 119, 190],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.82\n    }\n  ]\n}"}], "video": "data/videos/data02_seq_027.mp4"}, {"id": "train_data01_seq_033", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [158, 136, 178, 184],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [162, 144, 182, 192],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [163, 140, 183, 188],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [171, 137, 191, 185],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [174, 147, 194, 195],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.82\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_033.mp4"}, {"id": "train_data26_seq_018", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [340, 222, 378, 246],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [345, 221, 383, 245],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [352, 227, 390, 251],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [358, 220, 396, 244],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [356, 226, 394, 250],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.89\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_018.mp4"}, {"id": "train_data02_seq_019", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [81, 169, 109, 191],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [73, 167, 101, 189],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [77, 167, 105, 189],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [74, 159, 102, 181],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [74, 161, 102, 183],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.72\n    }\n  ]\n}"}], "video": "data/videos/data02_seq_019.mp4"}, {"id": "train_data07_seq_008", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [61, 158, 119, 200],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [69, 153, 127, 195],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [71, 150, 129, 192],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [78, 141, 136, 183],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [81, 149, 139, 191],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.88\n    }\n  ]\n}"}], "video": "data/videos/data07_seq_008.mp4"}, {"id": "train_data01_seq_046", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [192, 182, 236, 232],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [185, 185, 229, 235],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [193, 175, 237, 225],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [188, 174, 232, 224],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [197, 165, 241, 215],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.76\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_046.mp4"}, {"id": "train_data02_seq_028", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [52, 108, 110, 128],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [58, 104, 116, 124],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [49, 114, 107, 134],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [53, 122, 111, 142],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [59, 121, 117, 141],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.94\n    }\n  ]\n}"}], "video": "data/videos/data02_seq_028.mp4"}, {"id": "train_data06_seq_001", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [109, 72, 145, 130],\n      \"class_name\": \"car\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [101, 66, 137, 124],\n      \"class_name\": \"car\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [95, 61, 131, 119],\n      \"class_name\": \"car\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [104, 62, 140, 120],\n      \"class_name\": \"car\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [101, 71, 137, 129],\n      \"class_name\": \"car\",\n      \"confidence\": 0.84\n    }\n  ]\n}"}], "video": "data/videos/data06_seq_001.mp4"}, {"id": "train_data02_seq_005", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [167, 19, 215, 51],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [158, 24, 206, 56],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.7\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [160, 15, 208, 47],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [158, 5, 206, 37],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [149, 8, 197, 40],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.77\n    }\n  ]\n}"}], "video": "data/videos/data02_seq_005.mp4"}, {"id": "train_data05_seq_008", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [50, 198, 80, 248],\n      \"class_name\": \"car\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [58, 203, 88, 253],\n      \"class_name\": \"car\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [58, 206, 88, 256],\n      \"class_name\": \"car\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [55, 200, 85, 250],\n      \"class_name\": \"car\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [52, 198, 82, 248],\n      \"class_name\": \"car\",\n      \"confidence\": 0.9\n    }\n  ]\n}"}], "video": "data/videos/data05_seq_008.mp4"}, {"id": "train_data26_seq_014", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [352, 157, 404, 211],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [348, 157, 400, 211],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [357, 163, 409, 217],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [348, 161, 400, 215],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [347, 153, 399, 207],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.87\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_014.mp4"}, {"id": "train_data01_seq_024", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [123, 123, 183, 179],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [117, 125, 177, 181],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [118, 127, 178, 183],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [126, 136, 186, 192],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [126, 135, 186, 191],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.72\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_024.mp4"}, {"id": "train_data02_seq_002", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [140, 204, 176, 242],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [135, 198, 171, 236],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [132, 203, 168, 241],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [125, 205, 161, 243],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [135, 215, 171, 253],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.76\n    }\n  ]\n}"}], "video": "data/videos/data02_seq_002.mp4"}, {"id": "train_data04_seq_013", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [151, 144, 203, 196],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [157, 139, 209, 191],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [151, 134, 203, 186],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.7\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [144, 143, 196, 195],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [154, 137, 206, 189],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.78\n    }\n  ]\n}"}], "video": "data/videos/data04_seq_013.mp4"}, {"id": "train_data01_seq_031", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [52, 44, 90, 100],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [47, 34, 85, 90],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [44, 32, 82, 88],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [51, 22, 89, 78],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [47, 24, 85, 80],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.76\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_031.mp4"}, {"id": "train_data26_seq_049", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [293, 177, 351, 219],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [283, 186, 341, 228],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [278, 183, 336, 225],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [277, 177, 335, 219],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [283, 173, 341, 215],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.93\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_049.mp4"}, {"id": "train_data01_seq_037", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [73, 24, 129, 70],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [70, 22, 126, 68],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [75, 31, 131, 77],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [73, 34, 129, 80],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [63, 36, 119, 82],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.89\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_037.mp4"}, {"id": "train_data04_seq_009", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [91, 209, 147, 255],\n      \"class_name\": \"car\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [99, 205, 155, 251],\n      \"class_name\": \"car\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [97, 209, 153, 255],\n      \"class_name\": \"car\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [106, 207, 162, 253],\n      \"class_name\": \"car\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [115, 214, 171, 256],\n      \"class_name\": \"car\",\n      \"confidence\": 0.86\n    }\n  ]\n}"}], "video": "data/videos/data04_seq_009.mp4"}, {"id": "train_data25_seq_002", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [303, 272, 353, 316],\n      \"class_name\": \"car\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [312, 276, 362, 320],\n      \"class_name\": \"car\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [312, 281, 362, 325],\n      \"class_name\": \"car\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [315, 276, 365, 320],\n      \"class_name\": \"car\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [305, 268, 355, 312],\n      \"class_name\": \"car\",\n      \"confidence\": 0.86\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_002.mp4"}, {"id": "train_data02_seq_008", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [98, 81, 152, 131],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [92, 89, 146, 139],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [94, 90, 148, 140],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [87, 98, 141, 148],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [80, 108, 134, 158],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.95\n    }\n  ]\n}"}], "video": "data/videos/data02_seq_008.mp4"}, {"id": "train_data01_seq_036", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [123, 208, 157, 236],\n      \"class_name\": \"car\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [127, 216, 161, 244],\n      \"class_name\": \"car\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [133, 220, 167, 248],\n      \"class_name\": \"car\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [123, 222, 157, 250],\n      \"class_name\": \"car\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [126, 216, 160, 244],\n      \"class_name\": \"car\",\n      \"confidence\": 0.82\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_036.mp4"}, {"id": "train_data26_seq_059", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [271, 205, 307, 257],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [272, 213, 308, 265],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [263, 219, 299, 271],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [272, 220, 308, 272],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [280, 213, 316, 265],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.9\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_059.mp4"}, {"id": "train_data26_seq_050", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [229, 242, 265, 298],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [224, 241, 260, 297],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [228, 236, 264, 292],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [238, 228, 274, 284],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [236, 221, 272, 277],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.83\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_050.mp4"}, {"id": "train_data25_seq_036", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [359, 321, 407, 373],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [360, 321, 408, 373],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.7\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [360, 325, 408, 377],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [361, 332, 409, 384],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [355, 340, 403, 392],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.9\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_036.mp4"}, {"id": "train_data25_seq_034", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [229, 242, 265, 298],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [224, 241, 260, 297],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [228, 236, 264, 292],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [238, 228, 274, 284],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [236, 221, 272, 277],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.83\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_034.mp4"}, {"id": "train_data02_seq_022", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [170, 167, 210, 219],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [176, 173, 216, 225],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [175, 166, 215, 218],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [166, 176, 206, 228],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [169, 169, 209, 221],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.92\n    }\n  ]\n}"}], "video": "data/videos/data02_seq_022.mp4"}, {"id": "train_data05_seq_014", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [77, 120, 119, 164],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [74, 126, 116, 170],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [74, 133, 116, 177],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [74, 142, 116, 186],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [81, 141, 123, 185],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.84\n    }\n  ]\n}"}], "video": "data/videos/data05_seq_014.mp4"}, {"id": "train_data26_seq_046", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [297, 329, 321, 385],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [300, 339, 324, 395],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [295, 331, 319, 387],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [293, 336, 317, 392],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [292, 341, 316, 397],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.79\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_046.mp4"}, {"id": "train_data25_seq_029", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [396, 250, 426, 292],\n      \"class_name\": \"car\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [400, 244, 430, 286],\n      \"class_name\": \"car\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [397, 243, 427, 285],\n      \"class_name\": \"car\",\n      \"confidence\": 0.7\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [393, 247, 423, 289],\n      \"class_name\": \"car\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [400, 255, 430, 297],\n      \"class_name\": \"car\",\n      \"confidence\": 0.82\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_029.mp4"}, {"id": "train_data01_seq_004", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [186, 131, 226, 151],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [178, 125, 218, 145],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [172, 123, 212, 143],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [162, 115, 202, 135],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [158, 121, 198, 141],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.95\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_004.mp4"}, {"id": "train_data23_seq_012", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [271, 335, 293, 385],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [271, 331, 293, 381],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [278, 332, 300, 382],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [285, 330, 307, 380],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [287, 330, 309, 380],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.85\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_012.mp4"}, {"id": "train_data06_seq_010", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [36, 169, 82, 219],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [27, 170, 73, 220],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [21, 174, 67, 224],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [26, 182, 72, 232],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [20, 183, 66, 233],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.76\n    }\n  ]\n}"}], "video": "data/videos/data06_seq_010.mp4"}, {"id": "train_data01_seq_019", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [53, 55, 85, 99],\n      \"class_name\": \"car\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [46, 61, 78, 105],\n      \"class_name\": \"car\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [46, 62, 78, 106],\n      \"class_name\": \"car\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [56, 53, 88, 97],\n      \"class_name\": \"car\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [57, 56, 89, 100],\n      \"class_name\": \"car\",\n      \"confidence\": 0.72\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_019.mp4"}, {"id": "train_data23_seq_018", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [307, 262, 349, 290],\n      \"class_name\": \"car\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [307, 272, 349, 300],\n      \"class_name\": \"car\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [317, 280, 359, 308],\n      \"class_name\": \"car\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [324, 279, 366, 307],\n      \"class_name\": \"car\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [317, 269, 359, 297],\n      \"class_name\": \"car\",\n      \"confidence\": 0.77\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_018.mp4"}, {"id": "train_data01_seq_057", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [139, 192, 189, 212],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.7\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [138, 194, 188, 214],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [146, 184, 196, 204],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [136, 188, 186, 208],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [145, 186, 195, 206],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.77\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_057.mp4"}, {"id": "train_data25_seq_050", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [333, 271, 387, 325],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [326, 279, 380, 333],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [321, 271, 375, 325],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [331, 271, 385, 325],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [321, 281, 375, 335],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.92\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_050.mp4"}, {"id": "train_data06_seq_006", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [44, 210, 98, 232],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [36, 206, 90, 228],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [42, 199, 96, 221],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [32, 189, 86, 211],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [23, 180, 77, 202],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.74\n    }\n  ]\n}"}], "video": "data/videos/data06_seq_006.mp4"}, {"id": "train_data01_seq_055", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [196, 73, 222, 127],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [200, 81, 226, 135],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [196, 83, 222, 137],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [200, 93, 226, 147],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [207, 94, 233, 148],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.84\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_055.mp4"}, {"id": "train_data23_seq_014", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [338, 169, 386, 195],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [339, 163, 387, 189],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [343, 173, 391, 199],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [349, 177, 397, 203],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [341, 177, 389, 203],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.71\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_014.mp4"}, {"id": "train_data01_seq_053", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [68, 87, 96, 109],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [62, 82, 90, 104],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [71, 80, 99, 102],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [73, 70, 101, 92],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [70, 65, 98, 87],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.95\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_053.mp4"}, {"id": "train_data23_seq_004", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [239, 276, 281, 324],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [236, 275, 278, 323],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [232, 275, 274, 323],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [224, 273, 266, 321],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [216, 277, 258, 325],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.89\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_004.mp4"}, {"id": "train_data25_seq_014", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [389, 270, 443, 294],\n      \"class_name\": \"car\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [397, 274, 451, 298],\n      \"class_name\": \"car\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [394, 270, 448, 294],\n      \"class_name\": \"car\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [396, 269, 450, 293],\n      \"class_name\": \"car\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [399, 264, 453, 288],\n      \"class_name\": \"car\",\n      \"confidence\": 0.75\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_014.mp4"}, {"id": "train_data23_seq_025", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [201, 206, 241, 246],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [197, 210, 237, 250],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [187, 204, 227, 244],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [187, 199, 227, 239],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [179, 209, 219, 249],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.78\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_025.mp4"}, {"id": "train_data26_seq_019", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [302, 141, 342, 179],\n      \"class_name\": \"car\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [302, 144, 342, 182],\n      \"class_name\": \"car\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [300, 135, 340, 173],\n      \"class_name\": \"car\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [309, 138, 349, 176],\n      \"class_name\": \"car\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [313, 142, 353, 180],\n      \"class_name\": \"car\",\n      \"confidence\": 0.85\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_019.mp4"}, {"id": "train_data23_seq_021", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [315, 182, 375, 238],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [314, 173, 374, 229],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [304, 175, 364, 231],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [301, 181, 361, 237],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [311, 191, 371, 247],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.72\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_021.mp4"}, {"id": "train_data23_seq_032", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [266, 317, 310, 365],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [256, 311, 300, 359],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [256, 304, 300, 352],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [261, 294, 305, 342],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [265, 292, 309, 340],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.9\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_032.mp4"}, {"id": "train_data25_seq_016", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [251, 158, 287, 196],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [252, 157, 288, 195],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [250, 164, 286, 202],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [240, 167, 276, 205],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [244, 168, 280, 206],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.9\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_016.mp4"}, {"id": "train_data02_seq_011", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [218, 34, 240, 54],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [224, 30, 246, 50],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [221, 31, 243, 51],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [220, 33, 242, 53],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [212, 43, 234, 63],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.9\n    }\n  ]\n}"}], "video": "data/videos/data02_seq_011.mp4"}, {"id": "train_data23_seq_007", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [301, 154, 353, 202],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [292, 155, 344, 203],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [289, 158, 341, 206],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [288, 160, 340, 208],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [280, 170, 332, 218],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.75\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_007.mp4"}, {"id": "train_data26_seq_042", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [373, 176, 433, 228],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [379, 186, 439, 238],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [371, 182, 431, 234],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [375, 181, 435, 233],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [382, 191, 442, 243],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.87\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_042.mp4"}, {"id": "train_data01_seq_009", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [113, 100, 147, 120],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [114, 107, 148, 127],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [124, 109, 158, 129],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [117, 116, 151, 136],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [107, 126, 141, 146],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.71\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_009.mp4"}, {"id": "train_data06_seq_005", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [206, 68, 256, 90],\n      \"class_name\": \"car\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [211, 71, 256, 93],\n      \"class_name\": \"car\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [220, 67, 256, 89],\n      \"class_name\": \"car\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [218, 74, 256, 96],\n      \"class_name\": \"car\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [210, 66, 256, 88],\n      \"class_name\": \"car\",\n      \"confidence\": 0.8\n    }\n  ]\n}"}], "video": "data/videos/data06_seq_005.mp4"}, {"id": "train_data01_seq_058", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [152, 173, 204, 203],\n      \"class_name\": \"car\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [156, 177, 208, 207],\n      \"class_name\": \"car\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [160, 186, 212, 216],\n      \"class_name\": \"car\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [150, 184, 202, 214],\n      \"class_name\": \"car\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [154, 178, 206, 208],\n      \"class_name\": \"car\",\n      \"confidence\": 0.7\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_058.mp4"}, {"id": "train_data06_seq_012", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [153, 149, 197, 191],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [154, 145, 198, 187],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [157, 145, 201, 187],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [163, 146, 207, 188],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.7\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [154, 147, 198, 189],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.78\n    }\n  ]\n}"}], "video": "data/videos/data06_seq_012.mp4"}, {"id": "train_data01_seq_011", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [52, 18, 98, 78],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [46, 16, 92, 76],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [40, 21, 86, 81],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [43, 14, 89, 74],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [37, 13, 83, 73],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.86\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_011.mp4"}, {"id": "train_data01_seq_000", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [160, 101, 202, 133],\n      \"class_name\": \"car\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [158, 105, 200, 137],\n      \"class_name\": \"car\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [152, 97, 194, 129],\n      \"class_name\": \"car\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [154, 104, 196, 136],\n      \"class_name\": \"car\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [155, 97, 197, 129],\n      \"class_name\": \"car\",\n      \"confidence\": 0.8\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_000.mp4"}, {"id": "train_data01_seq_049", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [142, 164, 162, 200],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [138, 170, 158, 206],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [134, 176, 154, 212],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [140, 177, 160, 213],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [144, 171, 164, 207],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.79\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_049.mp4"}, {"id": "train_data26_seq_031", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [268, 155, 312, 177],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [261, 150, 305, 172],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [271, 144, 315, 166],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [261, 151, 305, 173],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [264, 145, 308, 167],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.89\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_031.mp4"}, {"id": "train_data25_seq_026", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [374, 275, 402, 325],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [384, 268, 412, 318],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [380, 261, 408, 311],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [375, 251, 403, 301],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [371, 258, 399, 308],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.8\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_026.mp4"}, {"id": "train_data25_seq_003", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [269, 206, 311, 246],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [274, 199, 316, 239],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [275, 208, 317, 248],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [267, 209, 309, 249],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [260, 217, 302, 257],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.74\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_003.mp4"}, {"id": "train_data06_seq_002", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [163, 46, 211, 82],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [159, 36, 207, 72],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [159, 39, 207, 75],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [165, 40, 213, 76],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [157, 48, 205, 84],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.78\n    }\n  ]\n}"}], "video": "data/videos/data06_seq_002.mp4"}, {"id": "train_data01_seq_002", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [107, 145, 143, 193],\n      \"class_name\": \"car\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [102, 141, 138, 189],\n      \"class_name\": \"car\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [96, 138, 132, 186],\n      \"class_name\": \"car\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [99, 141, 135, 189],\n      \"class_name\": \"car\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [108, 145, 144, 193],\n      \"class_name\": \"car\",\n      \"confidence\": 0.86\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_002.mp4"}, {"id": "train_data25_seq_031", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [298, 275, 352, 335],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [298, 269, 352, 329],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [290, 270, 344, 330],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [297, 279, 351, 339],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [302, 281, 356, 341],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.87\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_031.mp4"}, {"id": "train_data26_seq_056", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [266, 264, 288, 316],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [258, 272, 280, 324],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [253, 281, 275, 333],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [248, 275, 270, 327],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [254, 283, 276, 335],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.77\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_056.mp4"}, {"id": "train_data25_seq_046", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [314, 162, 352, 192],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [324, 157, 362, 187],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [319, 161, 357, 191],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [312, 160, 350, 190],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [309, 152, 347, 182],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.93\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_046.mp4"}, {"id": "train_data01_seq_027", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [19, 98, 65, 138],\n      \"class_name\": \"car\",\n      \"confidence\": 0.7\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [22, 107, 68, 147],\n      \"class_name\": \"car\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [19, 98, 65, 138],\n      \"class_name\": \"car\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [12, 90, 58, 130],\n      \"class_name\": \"car\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [15, 81, 61, 121],\n      \"class_name\": \"car\",\n      \"confidence\": 0.94\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_027.mp4"}, {"id": "train_data26_seq_009", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [320, 204, 350, 224],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [320, 198, 350, 218],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [317, 191, 347, 211],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.7\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [319, 194, 349, 214],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [318, 201, 348, 221],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.73\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_009.mp4"}, {"id": "train_data23_seq_001", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [205, 298, 255, 342],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [201, 298, 251, 342],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [201, 303, 251, 347],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [205, 312, 255, 356],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [210, 306, 260, 350],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.84\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_001.mp4"}, {"id": "train_data01_seq_042", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [40, 54, 74, 94],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [40, 46, 74, 86],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [46, 56, 80, 96],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [54, 56, 88, 96],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [50, 46, 84, 86],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.85\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_042.mp4"}, {"id": "train_data23_seq_026", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [381, 153, 407, 185],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [377, 157, 403, 189],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [369, 166, 395, 198],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [370, 172, 396, 204],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [368, 171, 394, 203],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.77\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_026.mp4"}, {"id": "train_data26_seq_000", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [229, 335, 283, 369],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [231, 337, 285, 371],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [222, 347, 276, 381],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [228, 346, 282, 380],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [227, 347, 281, 381],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.71\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_000.mp4"}, {"id": "train_data25_seq_010", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [363, 249, 395, 283],\n      \"class_name\": \"car\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [365, 252, 397, 286],\n      \"class_name\": \"car\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [362, 256, 394, 290],\n      \"class_name\": \"car\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [369, 248, 401, 282],\n      \"class_name\": \"car\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [371, 245, 403, 279],\n      \"class_name\": \"car\",\n      \"confidence\": 0.73\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_010.mp4"}, {"id": "train_data26_seq_027", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [265, 292, 307, 316],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [270, 292, 312, 316],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [273, 286, 315, 310],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [266, 292, 308, 316],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [276, 288, 318, 312],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.81\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_027.mp4"}, {"id": "train_data23_seq_016", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [357, 267, 407, 291],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [356, 271, 406, 295],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [354, 278, 404, 302],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [344, 272, 394, 296],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [334, 274, 384, 298],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.91\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_016.mp4"}, {"id": "train_data06_seq_011", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [111, 144, 161, 188],\n      \"class_name\": \"car\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [120, 148, 170, 192],\n      \"class_name\": \"car\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [120, 153, 170, 197],\n      \"class_name\": \"car\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [123, 148, 173, 192],\n      \"class_name\": \"car\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [113, 140, 163, 184],\n      \"class_name\": \"car\",\n      \"confidence\": 0.86\n    }\n  ]\n}"}], "video": "data/videos/data06_seq_011.mp4"}, {"id": "train_data25_seq_007", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [393, 308, 415, 352],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [403, 317, 425, 361],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [403, 310, 425, 354],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [397, 309, 419, 353],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [405, 299, 427, 343],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.8\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_007.mp4"}, {"id": "train_data26_seq_055", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [321, 219, 375, 269],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [325, 210, 379, 260],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [328, 205, 382, 255],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [326, 203, 380, 253],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [328, 196, 382, 246],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.92\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_055.mp4"}, {"id": "train_data01_seq_023", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [196, 75, 222, 123],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [196, 65, 222, 113],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [202, 60, 228, 108],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [211, 55, 237, 103],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [210, 63, 236, 111],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.86\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_023.mp4"}, {"id": "train_data02_seq_024", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [53, 55, 85, 99],\n      \"class_name\": \"car\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [46, 61, 78, 105],\n      \"class_name\": \"car\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [46, 62, 78, 106],\n      \"class_name\": \"car\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [56, 53, 88, 97],\n      \"class_name\": \"car\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [57, 56, 89, 100],\n      \"class_name\": \"car\",\n      \"confidence\": 0.72\n    }\n  ]\n}"}], "video": "data/videos/data02_seq_024.mp4"}, {"id": "train_data25_seq_019", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [275, 290, 303, 326],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [275, 281, 303, 317],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [267, 276, 295, 312],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [258, 273, 286, 309],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [248, 276, 276, 312],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.9\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_019.mp4"}, {"id": "train_data07_seq_006", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [27, 121, 51, 151],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [32, 129, 56, 159],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [26, 126, 50, 156],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [30, 119, 54, 149],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [36, 117, 60, 147],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.76\n    }\n  ]\n}"}], "video": "data/videos/data07_seq_006.mp4"}, {"id": "train_data01_seq_006", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [12, 45, 50, 83],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [11, 49, 49, 87],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [2, 48, 40, 86],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [2, 56, 40, 94],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [0, 60, 38, 98],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.74\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_006.mp4"}, {"id": "train_data01_seq_005", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [6, 36, 66, 82],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.7\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [6, 45, 66, 91],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [12, 51, 72, 97],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [20, 56, 80, 102],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [12, 55, 72, 101],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.82\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_005.mp4"}, {"id": "train_data26_seq_028", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [295, 300, 321, 320],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [295, 299, 321, 319],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [296, 294, 322, 314],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [290, 287, 316, 307],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [291, 282, 317, 302],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.91\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_028.mp4"}, {"id": "train_data01_seq_039", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [99, 54, 141, 86],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [108, 44, 150, 76],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [118, 51, 160, 83],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [110, 48, 152, 80],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [101, 55, 143, 87],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.76\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_039.mp4"}, {"id": "train_data05_seq_011", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [90, 102, 118, 142],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [96, 99, 124, 139],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [101, 91, 129, 131],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [107, 81, 135, 121],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [104, 77, 132, 117],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.71\n    }\n  ]\n}"}], "video": "data/videos/data05_seq_011.mp4"}, {"id": "train_data04_seq_000", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [55, 144, 97, 198],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [47, 137, 89, 191],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [51, 141, 93, 195],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [55, 151, 97, 205],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [63, 144, 105, 198],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.8\n    }\n  ]\n}"}], "video": "data/videos/data04_seq_000.mp4"}, {"id": "train_data26_seq_033", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [238, 335, 262, 371],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [243, 327, 267, 363],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [242, 320, 266, 356],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [237, 315, 261, 351],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [240, 322, 264, 358],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.71\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_033.mp4"}, {"id": "train_data07_seq_013", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [130, 199, 182, 225],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [135, 197, 187, 223],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [135, 201, 187, 227],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [136, 196, 188, 222],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [129, 187, 181, 213],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.84\n    }\n  ]\n}"}], "video": "data/videos/data07_seq_013.mp4"}, {"id": "train_data26_seq_041", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [342, 160, 388, 218],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [334, 151, 380, 209],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [326, 147, 372, 205],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [320, 141, 366, 199],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [325, 149, 371, 207],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.86\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_041.mp4"}, {"id": "train_data01_seq_007", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [24, 73, 68, 93],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [18, 71, 62, 91],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [28, 69, 72, 89],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [19, 62, 63, 82],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [21, 60, 65, 80],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.95\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_007.mp4"}, {"id": "train_data26_seq_015", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [312, 284, 370, 340],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [309, 283, 367, 339],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [308, 275, 366, 331],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [317, 285, 375, 341],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [313, 295, 371, 351],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.92\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_015.mp4"}, {"id": "train_data01_seq_051", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [17, 202, 53, 234],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [9, 195, 45, 227],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [16, 190, 52, 222],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [24, 184, 60, 216],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [14, 183, 50, 215],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.83\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_051.mp4"}, {"id": "train_data04_seq_001", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [72, 180, 104, 226],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [77, 171, 109, 217],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [86, 177, 118, 223],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [89, 174, 121, 220],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [92, 170, 124, 216],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.9\n    }\n  ]\n}"}], "video": "data/videos/data04_seq_001.mp4"}, {"id": "train_data05_seq_006", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [189, 115, 219, 165],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [188, 125, 218, 175],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [197, 134, 227, 184],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [193, 142, 223, 192],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [200, 144, 230, 194],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.92\n    }\n  ]\n}"}], "video": "data/videos/data05_seq_006.mp4"}, {"id": "train_data01_seq_021", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [170, 92, 226, 138],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [172, 86, 228, 132],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [169, 78, 225, 124],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [160, 87, 216, 133],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [161, 91, 217, 137],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.74\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_021.mp4"}, {"id": "train_data01_seq_018", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [42, 161, 68, 185],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [39, 158, 65, 182],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [48, 151, 74, 175],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [38, 143, 64, 167],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [32, 150, 58, 174],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.83\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_018.mp4"}, {"id": "train_data02_seq_018", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [126, 147, 164, 197],\n      \"class_name\": \"car\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [126, 155, 164, 205],\n      \"class_name\": \"car\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [122, 151, 160, 201],\n      \"class_name\": \"car\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [129, 155, 167, 205],\n      \"class_name\": \"car\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [136, 155, 174, 205],\n      \"class_name\": \"car\",\n      \"confidence\": 0.83\n    }\n  ]\n}"}], "video": "data/videos/data02_seq_018.mp4"}, {"id": "train_data01_seq_017", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [199, 120, 256, 168],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [193, 128, 251, 176],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [190, 128, 248, 176],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [187, 128, 245, 176],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [191, 130, 249, 178],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.84\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_017.mp4"}, {"id": "train_data26_seq_012", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [357, 199, 413, 233],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [366, 203, 422, 237],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [360, 195, 416, 229],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [354, 194, 410, 228],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [357, 193, 413, 227],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.91\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_012.mp4"}, {"id": "train_data05_seq_002", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [107, 120, 165, 176],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [110, 117, 168, 173],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [114, 114, 172, 170],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [119, 106, 177, 162],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [120, 108, 178, 164],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.82\n    }\n  ]\n}"}], "video": "data/videos/data05_seq_002.mp4"}, {"id": "train_data02_seq_006", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [190, 114, 226, 152],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [191, 112, 227, 150],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [199, 112, 235, 150],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [201, 102, 237, 140],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [203, 107, 239, 145],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.83\n    }\n  ]\n}"}], "video": "data/videos/data02_seq_006.mp4"}, {"id": "train_data02_seq_010", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [171, 126, 199, 180],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [164, 123, 192, 177],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [160, 119, 188, 173],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [167, 123, 195, 177],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [165, 120, 193, 174],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.7\n    }\n  ]\n}"}], "video": "data/videos/data02_seq_010.mp4"}, {"id": "train_data26_seq_043", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [283, 337, 339, 383],\n      \"class_name\": \"car\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [291, 333, 347, 379],\n      \"class_name\": \"car\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [289, 337, 345, 383],\n      \"class_name\": \"car\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [298, 335, 354, 381],\n      \"class_name\": \"car\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [307, 342, 363, 388],\n      \"class_name\": \"car\",\n      \"confidence\": 0.86\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_043.mp4"}, {"id": "train_data25_seq_028", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [364, 278, 402, 298],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [369, 283, 407, 303],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [367, 286, 405, 306],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [370, 294, 408, 314],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [362, 294, 400, 314],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.94\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_028.mp4"}, {"id": "train_data26_seq_026", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [269, 164, 297, 218],\n      \"class_name\": \"car\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [265, 171, 293, 225],\n      \"class_name\": \"car\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [264, 180, 292, 234],\n      \"class_name\": \"car\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [258, 188, 286, 242],\n      \"class_name\": \"car\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [263, 192, 291, 246],\n      \"class_name\": \"car\",\n      \"confidence\": 0.71\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_026.mp4"}, {"id": "train_data23_seq_011", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [256, 293, 294, 317],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [264, 288, 302, 312],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [268, 285, 306, 309],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [270, 295, 308, 319],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [270, 293, 308, 317],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.89\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_011.mp4"}, {"id": "train_data26_seq_058", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [221, 322, 257, 380],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [223, 320, 259, 378],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [214, 312, 250, 370],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [206, 319, 242, 377],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [208, 329, 244, 387],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.9\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_058.mp4"}, {"id": "train_data26_seq_002", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [311, 242, 353, 298],\n      \"class_name\": \"car\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [302, 246, 344, 302],\n      \"class_name\": \"car\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [311, 253, 353, 309],\n      \"class_name\": \"car\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [310, 246, 352, 302],\n      \"class_name\": \"car\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [302, 246, 344, 302],\n      \"class_name\": \"car\",\n      \"confidence\": 0.73\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_002.mp4"}, {"id": "train_data01_seq_030", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [52, 37, 78, 85],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [62, 36, 88, 84],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [71, 29, 97, 77],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [72, 33, 98, 81],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [63, 27, 89, 75],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.73\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_030.mp4"}, {"id": "train_data01_seq_050", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [173, 115, 225, 169],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [167, 111, 219, 165],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [166, 117, 218, 171],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [176, 111, 228, 165],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [186, 120, 238, 174],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.85\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_050.mp4"}, {"id": "train_data26_seq_040", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [374, 315, 396, 349],\n      \"class_name\": \"car\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [374, 324, 396, 358],\n      \"class_name\": \"car\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [371, 320, 393, 354],\n      \"class_name\": \"car\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [364, 318, 386, 352],\n      \"class_name\": \"car\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [357, 313, 379, 347],\n      \"class_name\": \"car\",\n      \"confidence\": 0.9\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_040.mp4"}, {"id": "train_data26_seq_047", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [288, 303, 326, 343],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [284, 310, 322, 350],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [290, 319, 328, 359],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [281, 327, 319, 367],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [279, 336, 317, 376],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.77\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_047.mp4"}, {"id": "train_data07_seq_003", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [115, 14, 147, 46],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [123, 23, 155, 55],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [123, 23, 155, 55],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [130, 29, 162, 61],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [124, 22, 156, 54],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.76\n    }\n  ]\n}"}], "video": "data/videos/data07_seq_003.mp4"}, {"id": "train_data02_seq_021", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [172, 67, 214, 117],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [181, 71, 223, 121],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [173, 77, 215, 127],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [170, 72, 212, 122],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [166, 64, 208, 114],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.83\n    }\n  ]\n}"}], "video": "data/videos/data02_seq_021.mp4"}, {"id": "train_data26_seq_032", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [248, 131, 306, 179],\n      \"class_name\": \"car\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [249, 123, 307, 171],\n      \"class_name\": \"car\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [240, 123, 298, 171],\n      \"class_name\": \"car\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [235, 130, 293, 178],\n      \"class_name\": \"car\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [238, 121, 296, 169],\n      \"class_name\": \"car\",\n      \"confidence\": 0.84\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_032.mp4"}, {"id": "train_data26_seq_051", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [337, 283, 389, 311],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [328, 292, 380, 320],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [319, 294, 371, 322],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [322, 295, 374, 323],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [314, 305, 366, 333],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.76\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_051.mp4"}, {"id": "train_data04_seq_008", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [50, 107, 92, 149],\n      \"class_name\": \"car\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [49, 110, 91, 152],\n      \"class_name\": \"car\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [52, 110, 94, 152],\n      \"class_name\": \"car\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [59, 102, 101, 144],\n      \"class_name\": \"car\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [50, 106, 92, 148],\n      \"class_name\": \"car\",\n      \"confidence\": 0.76\n    }\n  ]\n}"}], "video": "data/videos/data04_seq_008.mp4"}, {"id": "train_data26_seq_021", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [203, 182, 255, 218],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [200, 190, 252, 226],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [208, 186, 260, 222],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [198, 193, 250, 229],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [203, 196, 255, 232],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.81\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_021.mp4"}, {"id": "train_data23_seq_009", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [383, 155, 427, 191],\n      \"class_name\": \"car\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [382, 151, 426, 187],\n      \"class_name\": \"car\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [382, 147, 426, 183],\n      \"class_name\": \"car\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [374, 154, 418, 190],\n      \"class_name\": \"car\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [367, 161, 411, 197],\n      \"class_name\": \"car\",\n      \"confidence\": 0.78\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_009.mp4"}, {"id": "train_data23_seq_008", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [320, 259, 350, 297],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [316, 268, 346, 306],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [310, 263, 340, 301],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [301, 259, 331, 297],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [305, 250, 335, 288],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.87\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_008.mp4"}, {"id": "train_data25_seq_041", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [279, 189, 313, 221],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [286, 198, 320, 230],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [293, 208, 327, 240],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [283, 206, 317, 238],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [288, 201, 322, 233],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.9\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_041.mp4"}, {"id": "train_data26_seq_039", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [318, 260, 356, 306],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.7\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [316, 257, 354, 303],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [306, 256, 344, 302],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [306, 250, 344, 296],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [315, 249, 353, 295],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.71\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_039.mp4"}, {"id": "train_data26_seq_008", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [307, 314, 345, 362],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [304, 311, 342, 359],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [312, 302, 350, 350],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [316, 303, 354, 351],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [321, 309, 359, 357],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.9\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_008.mp4"}, {"id": "train_data01_seq_012", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [93, 61, 151, 91],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [85, 59, 143, 89],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [91, 66, 149, 96],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [100, 71, 158, 101],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [109, 79, 167, 109],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.75\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_012.mp4"}, {"id": "train_data04_seq_004", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [13, 121, 73, 145],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [8, 114, 68, 138],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [16, 122, 76, 146],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [11, 121, 71, 145],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [1, 124, 61, 148],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.77\n    }\n  ]\n}"}], "video": "data/videos/data04_seq_004.mp4"}, {"id": "train_data01_seq_025", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [154, 79, 176, 129],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [158, 86, 180, 136],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [159, 87, 181, 137],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [166, 79, 188, 129],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [156, 76, 178, 126],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.86\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_025.mp4"}, {"id": "train_data02_seq_001", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [87, 49, 129, 97],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [91, 43, 133, 91],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [92, 49, 134, 97],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [86, 56, 128, 104],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [91, 56, 133, 104],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.79\n    }\n  ]\n}"}], "video": "data/videos/data02_seq_001.mp4"}, {"id": "train_data01_seq_014", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [191, 168, 213, 218],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [197, 167, 219, 217],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [193, 176, 215, 226],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [192, 177, 214, 227],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [193, 187, 215, 237],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.75\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_014.mp4"}, {"id": "train_data05_seq_007", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [70, 111, 96, 135],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [74, 108, 100, 132],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [80, 107, 106, 131],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [77, 115, 103, 139],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [83, 115, 109, 139],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.85\n    }\n  ]\n}"}], "video": "data/videos/data05_seq_007.mp4"}, {"id": "train_data26_seq_016", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [210, 320, 246, 358],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [201, 327, 237, 365],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [195, 328, 231, 366],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [200, 338, 236, 376],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [209, 329, 245, 367],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.93\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_016.mp4"}, {"id": "train_data26_seq_025", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [360, 182, 390, 226],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [358, 179, 388, 223],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [359, 184, 389, 228],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [355, 186, 385, 230],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [347, 190, 377, 234],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.87\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_025.mp4"}, {"id": "train_data06_seq_009", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [154, 201, 204, 227],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [153, 197, 203, 223],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [155, 187, 205, 213],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [162, 195, 212, 221],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [162, 193, 212, 219],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.78\n    }\n  ]\n}"}], "video": "data/videos/data06_seq_009.mp4"}, {"id": "train_data23_seq_010", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [350, 280, 376, 318],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [352, 283, 378, 321],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [349, 281, 375, 319],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [340, 291, 366, 329],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [343, 293, 369, 331],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.74\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_010.mp4"}, {"id": "train_data01_seq_035", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [21, 34, 81, 84],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [15, 35, 75, 85],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [24, 38, 84, 88],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [24, 38, 84, 88],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [22, 35, 82, 85],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.83\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_035.mp4"}, {"id": "train_data01_seq_032", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [28, 57, 74, 89],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [22, 55, 68, 87],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [12, 50, 58, 82],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.7\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [7, 43, 53, 75],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [0, 44, 43, 76],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.81\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_032.mp4"}, {"id": "train_data23_seq_003", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [237, 163, 277, 183],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [244, 168, 284, 188],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [236, 167, 276, 187],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [237, 161, 277, 181],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [247, 161, 287, 181],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.76\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_003.mp4"}, {"id": "train_data25_seq_004", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [320, 214, 354, 236],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [314, 221, 348, 243],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [322, 231, 356, 253],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [313, 239, 347, 261],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [315, 244, 349, 266],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.79\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_004.mp4"}, {"id": "train_data25_seq_044", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [333, 233, 387, 283],\n      \"class_name\": \"car\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [338, 242, 392, 292],\n      \"class_name\": \"car\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [341, 234, 395, 284],\n      \"class_name\": \"car\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [346, 226, 400, 276],\n      \"class_name\": \"car\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [350, 231, 404, 281],\n      \"class_name\": \"car\",\n      \"confidence\": 0.75\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_044.mp4"}, {"id": "train_data26_seq_052", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [271, 312, 315, 344],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [261, 307, 305, 339],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [261, 299, 305, 331],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [261, 300, 305, 332],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [267, 290, 311, 322],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.91\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_052.mp4"}, {"id": "train_data02_seq_020", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [168, 129, 220, 153],\n      \"class_name\": \"car\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [158, 120, 210, 144],\n      \"class_name\": \"car\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [148, 126, 200, 150],\n      \"class_name\": \"car\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [146, 122, 198, 146],\n      \"class_name\": \"car\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [137, 114, 189, 138],\n      \"class_name\": \"car\",\n      \"confidence\": 0.7\n    }\n  ]\n}"}], "video": "data/videos/data02_seq_020.mp4"}, {"id": "train_data25_seq_024", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [268, 302, 320, 358],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [275, 311, 327, 367],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [273, 312, 325, 368],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [270, 306, 322, 362],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [261, 299, 313, 355],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.76\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_024.mp4"}, {"id": "train_data25_seq_035", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [382, 254, 414, 302],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [390, 258, 422, 306],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [382, 261, 414, 309],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [375, 259, 407, 307],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [380, 267, 412, 315],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.85\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_035.mp4"}, {"id": "train_data25_seq_011", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [245, 183, 277, 227],\n      \"class_name\": \"car\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [238, 189, 270, 233],\n      \"class_name\": \"car\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [238, 190, 270, 234],\n      \"class_name\": \"car\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [248, 181, 280, 225],\n      \"class_name\": \"car\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [249, 184, 281, 228],\n      \"class_name\": \"car\",\n      \"confidence\": 0.72\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_011.mp4"}, {"id": "train_data06_seq_008", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [24, 151, 52, 175],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [34, 146, 62, 170],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [32, 138, 60, 162],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [27, 141, 55, 165],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [27, 131, 55, 155],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.83\n    }\n  ]\n}"}], "video": "data/videos/data06_seq_008.mp4"}, {"id": "train_data25_seq_027", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [316, 218, 358, 250],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [307, 227, 349, 259],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [306, 229, 348, 261],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [303, 237, 345, 269],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [303, 238, 345, 270],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.74\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_027.mp4"}, {"id": "train_data01_seq_029", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [200, 68, 234, 126],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [206, 61, 240, 119],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [200, 68, 234, 126],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [192, 69, 226, 127],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [192, 59, 226, 117],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.91\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_029.mp4"}, {"id": "train_data01_seq_045", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [145, 30, 185, 52],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [143, 38, 183, 60],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [148, 46, 188, 68],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [155, 41, 195, 63],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [155, 40, 195, 62],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.71\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_045.mp4"}, {"id": "train_data04_seq_012", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [190, 175, 232, 213],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [199, 178, 241, 216],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [208, 188, 250, 226],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [205, 190, 247, 228],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [208, 186, 250, 224],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.92\n    }\n  ]\n}"}], "video": "data/videos/data04_seq_012.mp4"}, {"id": "train_data26_seq_007", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [299, 248, 357, 304],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [302, 245, 360, 301],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [306, 242, 364, 298],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [311, 234, 369, 290],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [312, 236, 370, 292],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.82\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_007.mp4"}, {"id": "train_data02_seq_013", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [23, 100, 79, 156],\n      \"class_name\": \"car\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [32, 98, 88, 154],\n      \"class_name\": \"car\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [24, 96, 80, 152],\n      \"class_name\": \"car\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [16, 106, 72, 162],\n      \"class_name\": \"car\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [9, 104, 65, 160],\n      \"class_name\": \"car\",\n      \"confidence\": 0.94\n    }\n  ]\n}"}], "video": "data/videos/data02_seq_013.mp4"}, {"id": "train_data26_seq_003", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [359, 321, 407, 373],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [360, 321, 408, 373],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.7\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [360, 325, 408, 377],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [361, 332, 409, 384],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [355, 340, 403, 392],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.9\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_003.mp4"}, {"id": "train_data01_seq_048", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [37, 70, 77, 122],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [35, 68, 75, 120],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [39, 63, 79, 115],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [42, 65, 82, 117],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [37, 75, 77, 127],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.94\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_048.mp4"}, {"id": "train_data04_seq_011", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [76, 27, 120, 49],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [69, 22, 113, 44],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [79, 16, 123, 38],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [69, 23, 113, 45],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [72, 17, 116, 39],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.89\n    }\n  ]\n}"}], "video": "data/videos/data04_seq_011.mp4"}, {"id": "train_data25_seq_053", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [198, 164, 258, 210],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.7\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [198, 173, 258, 219],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [204, 179, 264, 225],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [212, 184, 272, 230],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [204, 183, 264, 229],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.82\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_053.mp4"}, {"id": "train_data01_seq_010", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [199, 79, 227, 103],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [191, 72, 219, 96],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [199, 81, 227, 105],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [203, 78, 231, 102],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [205, 88, 233, 112],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.89\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_010.mp4"}, {"id": "train_data05_seq_009", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [23, 141, 71, 161],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [20, 138, 68, 158],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [13, 141, 61, 161],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [18, 135, 66, 155],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [23, 134, 71, 154],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.72\n    }\n  ]\n}"}], "video": "data/videos/data05_seq_009.mp4"}, {"id": "train_data25_seq_055", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [278, 273, 328, 299],\n      \"class_name\": \"car\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [286, 264, 336, 290],\n      \"class_name\": \"car\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [282, 264, 332, 290],\n      \"class_name\": \"car\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [292, 263, 342, 289],\n      \"class_name\": \"car\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [293, 267, 343, 293],\n      \"class_name\": \"car\",\n      \"confidence\": 0.71\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_055.mp4"}, {"id": "train_data26_seq_006", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [248, 317, 276, 363],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [249, 320, 277, 366],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.7\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [248, 318, 276, 364],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [242, 325, 270, 371],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [249, 317, 277, 363],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.73\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_006.mp4"}, {"id": "train_data23_seq_029", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [210, 272, 260, 316],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [216, 277, 266, 321],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [219, 287, 269, 331],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [227, 278, 277, 322],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [224, 274, 274, 318],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.89\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_029.mp4"}, {"id": "train_data05_seq_013", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [109, 0, 137, 55],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [110, 0, 138, 48],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [109, 0, 137, 55],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [108, 5, 136, 63],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [103, 7, 131, 65],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.87\n    }\n  ]\n}"}], "video": "data/videos/data05_seq_013.mp4"}, {"id": "train_data02_seq_000", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [149, 150, 187, 172],\n      \"class_name\": \"car\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [154, 150, 192, 172],\n      \"class_name\": \"car\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [161, 144, 199, 166],\n      \"class_name\": \"car\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [168, 143, 206, 165],\n      \"class_name\": \"car\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [172, 136, 210, 158],\n      \"class_name\": \"car\",\n      \"confidence\": 0.86\n    }\n  ]\n}"}], "video": "data/videos/data02_seq_000.mp4"}, {"id": "train_data23_seq_005", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [228, 297, 274, 347],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [219, 298, 265, 348],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [213, 302, 259, 352],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [218, 310, 264, 360],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [212, 311, 258, 361],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.76\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_005.mp4"}, {"id": "train_data26_seq_035", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [254, 223, 274, 243],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [261, 231, 281, 251],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [255, 225, 275, 245],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [262, 226, 282, 246],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [271, 233, 291, 253],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.92\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_035.mp4"}, {"id": "train_data26_seq_001", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [361, 294, 415, 316],\n      \"class_name\": \"car\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [366, 284, 420, 306],\n      \"class_name\": \"car\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [367, 288, 421, 310],\n      \"class_name\": \"car\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [376, 294, 430, 316],\n      \"class_name\": \"car\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [372, 291, 426, 313],\n      \"class_name\": \"car\",\n      \"confidence\": 0.82\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_001.mp4"}, {"id": "train_data07_seq_010", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [39, 49, 91, 69],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [47, 56, 99, 76],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [46, 56, 98, 76],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [36, 56, 88, 76],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [37, 63, 89, 83],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.83\n    }\n  ]\n}"}], "video": "data/videos/data07_seq_010.mp4"}, {"id": "train_data02_seq_003", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [150, 148, 176, 178],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [147, 141, 173, 171],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [140, 136, 166, 166],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [139, 146, 165, 176],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [141, 146, 167, 176],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.79\n    }\n  ]\n}"}], "video": "data/videos/data02_seq_003.mp4"}, {"id": "train_data25_seq_012", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [340, 222, 378, 246],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [345, 221, 383, 245],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [352, 227, 390, 251],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [358, 220, 396, 244],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [356, 226, 394, 250],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.89\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_012.mp4"}, {"id": "train_data26_seq_054", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [254, 299, 312, 323],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [263, 292, 321, 316],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [256, 287, 314, 311],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [257, 294, 315, 318],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [249, 291, 307, 315],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.9\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_054.mp4"}, {"id": "train_data04_seq_007", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [181, 117, 205, 157],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [176, 118, 200, 158],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [167, 127, 191, 167],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [157, 133, 181, 173],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [163, 136, 187, 176],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.84\n    }\n  ]\n}"}], "video": "data/videos/data04_seq_007.mp4"}, {"id": "train_data23_seq_022", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [346, 329, 396, 355],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [345, 325, 395, 351],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [347, 315, 397, 341],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [354, 323, 404, 349],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [354, 321, 404, 347],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.78\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_022.mp4"}, {"id": "train_data01_seq_001", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [66, 100, 122, 146],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [61, 103, 117, 149],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [66, 97, 122, 143],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [56, 107, 112, 153],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [50, 98, 106, 144],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.86\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_001.mp4"}, {"id": "train_data25_seq_032", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [251, 228, 293, 284],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [250, 234, 292, 290],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [243, 240, 285, 296],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [244, 239, 286, 295],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [254, 241, 296, 297],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.94\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_032.mp4"}, {"id": "train_data01_seq_034", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [42, 185, 98, 229],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [34, 195, 90, 239],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [43, 196, 99, 240],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [45, 187, 101, 231],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [47, 197, 103, 241],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.71\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_034.mp4"}, {"id": "train_data02_seq_004", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [61, 76, 117, 98],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [60, 73, 116, 95],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [69, 68, 125, 90],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [67, 77, 123, 99],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [59, 77, 115, 99],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.7\n    }\n  ]\n}"}], "video": "data/videos/data02_seq_004.mp4"}, {"id": "train_data25_seq_051", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [263, 163, 283, 193],\n      \"class_name\": \"car\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [273, 158, 293, 188],\n      \"class_name\": \"car\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [273, 154, 293, 184],\n      \"class_name\": \"car\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [270, 151, 290, 181],\n      \"class_name\": \"car\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [279, 154, 299, 184],\n      \"class_name\": \"car\",\n      \"confidence\": 0.79\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_051.mp4"}, {"id": "train_data07_seq_001", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [23, 141, 71, 161],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [20, 138, 68, 158],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [13, 141, 61, 161],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [18, 135, 66, 155],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [23, 134, 71, 154],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.72\n    }\n  ]\n}"}], "video": "data/videos/data07_seq_001.mp4"}, {"id": "train_data25_seq_001", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [244, 165, 270, 213],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [254, 164, 280, 212],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [263, 157, 289, 205],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [264, 161, 290, 209],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [255, 155, 281, 203],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.73\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_001.mp4"}, {"id": "train_data06_seq_000", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [96, 66, 138, 98],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [94, 65, 136, 97],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [95, 57, 137, 89],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [90, 48, 132, 80],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [81, 44, 123, 76],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.93\n    }\n  ]\n}"}], "video": "data/videos/data06_seq_000.mp4"}]