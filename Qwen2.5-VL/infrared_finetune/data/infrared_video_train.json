[{"id": "train_data23_seq_015", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data23_seq_015.mp4"}, {"id": "train_data23_seq_033", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 207, 85, 74]}"}], "video": "data/videos/data23_seq_033.mp4"}, {"id": "train_data25_seq_008", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data25_seq_008.mp4"}, {"id": "train_data26_seq_029", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data26_seq_029.mp4"}, {"id": "train_data25_seq_025", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data25_seq_025.mp4"}, {"id": "train_data06_seq_007", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data06_seq_007.mp4"}, {"id": "train_data05_seq_004", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data05_seq_004.mp4"}, {"id": "train_data01_seq_040", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data01_seq_040.mp4"}, {"id": "train_data25_seq_040", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data25_seq_040.mp4"}, {"id": "train_data01_seq_022", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data01_seq_022.mp4"}, {"id": "train_data25_seq_056", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data25_seq_056.mp4"}, {"id": "train_data07_seq_011", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data07_seq_011.mp4"}, {"id": "train_data01_seq_059", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data01_seq_059.mp4"}, {"id": "train_data25_seq_021", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data25_seq_021.mp4"}, {"id": "train_data26_seq_004", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data26_seq_004.mp4"}, {"id": "train_data01_seq_026", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data01_seq_026.mp4"}, {"id": "train_data25_seq_045", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data25_seq_045.mp4"}, {"id": "train_data01_seq_003", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data01_seq_003.mp4"}, {"id": "train_data25_seq_006", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data25_seq_006.mp4"}, {"id": "train_data01_seq_020", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data01_seq_020.mp4"}, {"id": "train_data04_seq_014", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data04_seq_014.mp4"}, {"id": "train_data07_seq_004", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data07_seq_004.mp4"}, {"id": "train_data25_seq_054", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data25_seq_054.mp4"}, {"id": "train_data04_seq_002", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data04_seq_002.mp4"}, {"id": "train_data23_seq_028", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data23_seq_028.mp4"}, {"id": "train_data02_seq_014", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data02_seq_014.mp4"}, {"id": "train_data26_seq_013", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data26_seq_013.mp4"}, {"id": "train_data02_seq_009", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data02_seq_009.mp4"}, {"id": "train_data25_seq_013", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data25_seq_013.mp4"}, {"id": "train_data25_seq_005", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data25_seq_005.mp4"}, {"id": "train_data25_seq_043", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data25_seq_043.mp4"}, {"id": "train_data01_seq_008", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data01_seq_008.mp4"}, {"id": "train_data23_seq_031", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 207, 85, 74]}"}], "video": "data/videos/data23_seq_031.mp4"}, {"id": "train_data25_seq_049", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data25_seq_049.mp4"}, {"id": "train_data25_seq_052", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data25_seq_052.mp4"}, {"id": "train_data01_seq_013", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data01_seq_013.mp4"}, {"id": "train_data26_seq_020", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data26_seq_020.mp4"}, {"id": "train_data26_seq_044", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data26_seq_044.mp4"}, {"id": "train_data07_seq_000", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data07_seq_000.mp4"}, {"id": "train_data26_seq_030", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data26_seq_030.mp4"}, {"id": "train_data25_seq_033", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data25_seq_033.mp4"}, {"id": "train_data26_seq_024", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data26_seq_024.mp4"}, {"id": "train_data02_seq_017", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data02_seq_017.mp4"}, {"id": "train_data05_seq_000", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data05_seq_000.mp4"}, {"id": "train_data25_seq_020", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data25_seq_020.mp4"}, {"id": "train_data06_seq_003", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data06_seq_003.mp4"}, {"id": "train_data25_seq_048", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data25_seq_048.mp4"}, {"id": "train_data25_seq_023", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data25_seq_023.mp4"}, {"id": "train_data25_seq_022", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data25_seq_022.mp4"}, {"id": "train_data05_seq_010", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data05_seq_010.mp4"}, {"id": "train_data04_seq_006", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data04_seq_006.mp4"}, {"id": "train_data01_seq_052", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data01_seq_052.mp4"}, {"id": "train_data25_seq_009", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data25_seq_009.mp4"}, {"id": "train_data26_seq_048", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data26_seq_048.mp4"}, {"id": "train_data01_seq_044", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data01_seq_044.mp4"}, {"id": "train_data25_seq_059", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data25_seq_059.mp4"}, {"id": "train_data26_seq_045", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data26_seq_045.mp4"}, {"id": "train_data06_seq_004", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data06_seq_004.mp4"}, {"id": "train_data07_seq_002", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data07_seq_002.mp4"}, {"id": "train_data25_seq_018", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data25_seq_018.mp4"}, {"id": "train_data23_seq_013", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data23_seq_013.mp4"}, {"id": "train_data06_seq_014", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data06_seq_014.mp4"}, {"id": "train_data04_seq_010", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data04_seq_010.mp4"}, {"id": "train_data26_seq_034", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data26_seq_034.mp4"}, {"id": "train_data25_seq_038", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data25_seq_038.mp4"}, {"id": "train_data02_seq_027", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data02_seq_027.mp4"}, {"id": "train_data01_seq_033", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data01_seq_033.mp4"}, {"id": "train_data26_seq_018", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data26_seq_018.mp4"}, {"id": "train_data02_seq_019", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data02_seq_019.mp4"}, {"id": "train_data07_seq_008", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data07_seq_008.mp4"}, {"id": "train_data01_seq_046", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data01_seq_046.mp4"}, {"id": "train_data02_seq_028", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data02_seq_028.mp4"}, {"id": "train_data06_seq_001", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data06_seq_001.mp4"}, {"id": "train_data02_seq_005", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data02_seq_005.mp4"}, {"id": "train_data05_seq_008", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data05_seq_008.mp4"}, {"id": "train_data26_seq_014", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data26_seq_014.mp4"}, {"id": "train_data01_seq_024", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data01_seq_024.mp4"}, {"id": "train_data02_seq_002", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data02_seq_002.mp4"}, {"id": "train_data04_seq_013", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data04_seq_013.mp4"}, {"id": "train_data01_seq_031", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data01_seq_031.mp4"}, {"id": "train_data26_seq_049", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data26_seq_049.mp4"}, {"id": "train_data01_seq_037", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data01_seq_037.mp4"}, {"id": "train_data04_seq_009", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data04_seq_009.mp4"}, {"id": "train_data25_seq_002", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data25_seq_002.mp4"}, {"id": "train_data02_seq_008", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data02_seq_008.mp4"}, {"id": "train_data01_seq_036", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data01_seq_036.mp4"}, {"id": "train_data26_seq_059", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data26_seq_059.mp4"}, {"id": "train_data26_seq_050", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data26_seq_050.mp4"}, {"id": "train_data25_seq_036", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data25_seq_036.mp4"}, {"id": "train_data25_seq_034", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data25_seq_034.mp4"}, {"id": "train_data02_seq_022", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data02_seq_022.mp4"}, {"id": "train_data05_seq_014", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data05_seq_014.mp4"}, {"id": "train_data26_seq_046", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data26_seq_046.mp4"}, {"id": "train_data25_seq_029", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data25_seq_029.mp4"}, {"id": "train_data01_seq_004", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data01_seq_004.mp4"}, {"id": "train_data23_seq_012", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data23_seq_012.mp4"}, {"id": "train_data06_seq_010", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data06_seq_010.mp4"}, {"id": "train_data01_seq_019", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data01_seq_019.mp4"}, {"id": "train_data23_seq_018", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data23_seq_018.mp4"}, {"id": "train_data01_seq_057", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data01_seq_057.mp4"}, {"id": "train_data25_seq_050", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data25_seq_050.mp4"}, {"id": "train_data06_seq_006", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data06_seq_006.mp4"}, {"id": "train_data01_seq_055", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data01_seq_055.mp4"}, {"id": "train_data23_seq_014", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data23_seq_014.mp4"}, {"id": "train_data01_seq_053", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data01_seq_053.mp4"}, {"id": "train_data23_seq_004", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data23_seq_004.mp4"}, {"id": "train_data25_seq_014", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data25_seq_014.mp4"}, {"id": "train_data23_seq_025", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data23_seq_025.mp4"}, {"id": "train_data26_seq_019", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data26_seq_019.mp4"}, {"id": "train_data23_seq_021", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data23_seq_021.mp4"}, {"id": "train_data23_seq_032", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 207, 85, 74]}"}], "video": "data/videos/data23_seq_032.mp4"}, {"id": "train_data25_seq_016", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data25_seq_016.mp4"}, {"id": "train_data02_seq_011", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data02_seq_011.mp4"}, {"id": "train_data23_seq_007", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data23_seq_007.mp4"}, {"id": "train_data26_seq_042", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data26_seq_042.mp4"}, {"id": "train_data01_seq_009", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data01_seq_009.mp4"}, {"id": "train_data06_seq_005", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data06_seq_005.mp4"}, {"id": "train_data01_seq_058", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data01_seq_058.mp4"}, {"id": "train_data06_seq_012", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data06_seq_012.mp4"}, {"id": "train_data01_seq_011", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data01_seq_011.mp4"}, {"id": "train_data01_seq_000", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data01_seq_000.mp4"}, {"id": "train_data01_seq_049", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data01_seq_049.mp4"}, {"id": "train_data26_seq_031", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data26_seq_031.mp4"}, {"id": "train_data25_seq_026", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data25_seq_026.mp4"}, {"id": "train_data25_seq_003", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data25_seq_003.mp4"}, {"id": "train_data06_seq_002", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data06_seq_002.mp4"}, {"id": "train_data01_seq_002", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data01_seq_002.mp4"}, {"id": "train_data25_seq_031", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data25_seq_031.mp4"}, {"id": "train_data26_seq_056", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data26_seq_056.mp4"}, {"id": "train_data25_seq_046", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data25_seq_046.mp4"}, {"id": "train_data01_seq_027", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data01_seq_027.mp4"}, {"id": "train_data26_seq_009", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data26_seq_009.mp4"}, {"id": "train_data23_seq_001", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data23_seq_001.mp4"}, {"id": "train_data01_seq_042", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data01_seq_042.mp4"}, {"id": "train_data23_seq_026", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data23_seq_026.mp4"}, {"id": "train_data26_seq_000", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data26_seq_000.mp4"}, {"id": "train_data25_seq_010", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data25_seq_010.mp4"}, {"id": "train_data26_seq_027", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data26_seq_027.mp4"}, {"id": "train_data23_seq_016", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data23_seq_016.mp4"}, {"id": "train_data06_seq_011", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data06_seq_011.mp4"}, {"id": "train_data25_seq_007", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data25_seq_007.mp4"}, {"id": "train_data26_seq_055", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data26_seq_055.mp4"}, {"id": "train_data01_seq_023", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data01_seq_023.mp4"}, {"id": "train_data02_seq_024", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data02_seq_024.mp4"}, {"id": "train_data25_seq_019", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data25_seq_019.mp4"}, {"id": "train_data07_seq_006", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data07_seq_006.mp4"}, {"id": "train_data01_seq_006", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data01_seq_006.mp4"}, {"id": "train_data01_seq_005", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data01_seq_005.mp4"}, {"id": "train_data26_seq_028", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data26_seq_028.mp4"}, {"id": "train_data01_seq_039", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data01_seq_039.mp4"}, {"id": "train_data05_seq_011", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data05_seq_011.mp4"}, {"id": "train_data04_seq_000", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data04_seq_000.mp4"}, {"id": "train_data26_seq_033", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data26_seq_033.mp4"}, {"id": "train_data07_seq_013", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data07_seq_013.mp4"}, {"id": "train_data26_seq_041", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data26_seq_041.mp4"}, {"id": "train_data01_seq_007", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data01_seq_007.mp4"}, {"id": "train_data26_seq_015", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data26_seq_015.mp4"}, {"id": "train_data01_seq_051", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data01_seq_051.mp4"}, {"id": "train_data04_seq_001", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data04_seq_001.mp4"}, {"id": "train_data05_seq_006", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data05_seq_006.mp4"}, {"id": "train_data01_seq_021", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data01_seq_021.mp4"}, {"id": "train_data01_seq_018", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data01_seq_018.mp4"}, {"id": "train_data02_seq_018", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data02_seq_018.mp4"}, {"id": "train_data01_seq_017", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data01_seq_017.mp4"}, {"id": "train_data26_seq_012", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data26_seq_012.mp4"}, {"id": "train_data05_seq_002", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data05_seq_002.mp4"}, {"id": "train_data02_seq_006", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data02_seq_006.mp4"}, {"id": "train_data02_seq_010", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data02_seq_010.mp4"}, {"id": "train_data26_seq_043", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data26_seq_043.mp4"}, {"id": "train_data25_seq_028", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data25_seq_028.mp4"}, {"id": "train_data26_seq_026", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data26_seq_026.mp4"}, {"id": "train_data23_seq_011", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data23_seq_011.mp4"}, {"id": "train_data26_seq_058", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data26_seq_058.mp4"}, {"id": "train_data26_seq_002", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data26_seq_002.mp4"}, {"id": "train_data01_seq_030", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data01_seq_030.mp4"}, {"id": "train_data01_seq_050", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data01_seq_050.mp4"}, {"id": "train_data26_seq_040", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data26_seq_040.mp4"}, {"id": "train_data26_seq_047", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data26_seq_047.mp4"}, {"id": "train_data07_seq_003", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data07_seq_003.mp4"}, {"id": "train_data02_seq_021", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data02_seq_021.mp4"}, {"id": "train_data26_seq_032", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data26_seq_032.mp4"}, {"id": "train_data26_seq_051", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data26_seq_051.mp4"}, {"id": "train_data04_seq_008", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data04_seq_008.mp4"}, {"id": "train_data26_seq_021", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data26_seq_021.mp4"}, {"id": "train_data23_seq_009", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data23_seq_009.mp4"}, {"id": "train_data23_seq_008", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data23_seq_008.mp4"}, {"id": "train_data25_seq_041", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data25_seq_041.mp4"}, {"id": "train_data26_seq_039", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data26_seq_039.mp4"}, {"id": "train_data26_seq_008", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data26_seq_008.mp4"}, {"id": "train_data01_seq_012", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data01_seq_012.mp4"}, {"id": "train_data04_seq_004", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data04_seq_004.mp4"}, {"id": "train_data01_seq_025", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data01_seq_025.mp4"}, {"id": "train_data02_seq_001", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data02_seq_001.mp4"}, {"id": "train_data01_seq_014", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data01_seq_014.mp4"}, {"id": "train_data05_seq_007", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data05_seq_007.mp4"}, {"id": "train_data26_seq_016", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data26_seq_016.mp4"}, {"id": "train_data26_seq_025", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data26_seq_025.mp4"}, {"id": "train_data06_seq_009", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data06_seq_009.mp4"}, {"id": "train_data23_seq_010", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data23_seq_010.mp4"}, {"id": "train_data01_seq_035", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data01_seq_035.mp4"}, {"id": "train_data01_seq_032", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data01_seq_032.mp4"}, {"id": "train_data23_seq_003", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data23_seq_003.mp4"}, {"id": "train_data25_seq_004", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data25_seq_004.mp4"}, {"id": "train_data25_seq_044", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data25_seq_044.mp4"}, {"id": "train_data26_seq_052", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data26_seq_052.mp4"}, {"id": "train_data02_seq_020", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data02_seq_020.mp4"}, {"id": "train_data25_seq_024", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data25_seq_024.mp4"}, {"id": "train_data25_seq_035", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data25_seq_035.mp4"}, {"id": "train_data25_seq_011", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data25_seq_011.mp4"}, {"id": "train_data06_seq_008", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data06_seq_008.mp4"}, {"id": "train_data25_seq_027", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data25_seq_027.mp4"}, {"id": "train_data01_seq_029", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data01_seq_029.mp4"}, {"id": "train_data01_seq_045", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data01_seq_045.mp4"}, {"id": "train_data04_seq_012", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data04_seq_012.mp4"}, {"id": "train_data26_seq_007", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data26_seq_007.mp4"}, {"id": "train_data02_seq_013", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data02_seq_013.mp4"}, {"id": "train_data26_seq_003", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data26_seq_003.mp4"}, {"id": "train_data01_seq_048", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data01_seq_048.mp4"}, {"id": "train_data04_seq_011", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data04_seq_011.mp4"}, {"id": "train_data25_seq_053", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data25_seq_053.mp4"}, {"id": "train_data01_seq_010", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data01_seq_010.mp4"}, {"id": "train_data05_seq_009", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data05_seq_009.mp4"}, {"id": "train_data25_seq_055", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data25_seq_055.mp4"}, {"id": "train_data26_seq_006", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data26_seq_006.mp4"}, {"id": "train_data23_seq_029", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data23_seq_029.mp4"}, {"id": "train_data05_seq_013", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data05_seq_013.mp4"}, {"id": "train_data02_seq_000", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data02_seq_000.mp4"}, {"id": "train_data23_seq_005", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data23_seq_005.mp4"}, {"id": "train_data26_seq_035", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data26_seq_035.mp4"}, {"id": "train_data26_seq_001", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data26_seq_001.mp4"}, {"id": "train_data07_seq_010", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data07_seq_010.mp4"}, {"id": "train_data02_seq_003", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data02_seq_003.mp4"}, {"id": "train_data25_seq_012", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data25_seq_012.mp4"}, {"id": "train_data26_seq_054", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data26_seq_054.mp4"}, {"id": "train_data04_seq_007", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data04_seq_007.mp4"}, {"id": "train_data23_seq_022", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data23_seq_022.mp4"}, {"id": "train_data01_seq_001", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data01_seq_001.mp4"}, {"id": "train_data25_seq_032", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data25_seq_032.mp4"}, {"id": "train_data01_seq_034", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data01_seq_034.mp4"}, {"id": "train_data02_seq_004", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data02_seq_004.mp4"}, {"id": "train_data25_seq_051", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data25_seq_051.mp4"}, {"id": "train_data07_seq_001", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data07_seq_001.mp4"}, {"id": "train_data25_seq_001", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data25_seq_001.mp4"}, {"id": "train_data06_seq_000", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data06_seq_000.mp4"}]