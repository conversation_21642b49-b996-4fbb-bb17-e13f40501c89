[{"id": "test_data07_seq_009", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [164, 69, 210, 125],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [171, 67, 217, 123],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [179, 64, 225, 120],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [175, 55, 221, 111],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [165, 62, 211, 118],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.83\n    }\n  ]\n}"}], "video": "data/videos/data07_seq_009.mp4"}, {"id": "test_data05_seq_005", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [187, 199, 215, 227],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [179, 189, 207, 217],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [185, 184, 213, 212],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [183, 175, 211, 203],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [173, 175, 201, 203],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.72\n    }\n  ]\n}"}], "video": "data/videos/data05_seq_005.mp4"}, {"id": "test_data26_seq_010", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [228, 297, 274, 347],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [219, 298, 265, 348],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [213, 302, 259, 352],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [218, 310, 264, 360],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [212, 311, 258, 361],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.76\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_010.mp4"}, {"id": "test_data23_seq_023", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [229, 326, 275, 374],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [232, 331, 278, 379],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [228, 341, 274, 389],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [223, 340, 269, 388],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [227, 335, 273, 383],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.73\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_023.mp4"}, {"id": "test_data02_seq_025", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [126, 110, 172, 152],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [123, 108, 169, 150],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [123, 102, 169, 144],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [114, 104, 160, 146],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.7\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [117, 114, 163, 156],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.74\n    }\n  ]\n}"}], "video": "data/videos/data02_seq_025.mp4"}, {"id": "test_data25_seq_017", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [217, 216, 277, 276],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [210, 222, 270, 282],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [219, 214, 279, 274],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [220, 223, 280, 283],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [224, 218, 284, 278],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.7\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_017.mp4"}, {"id": "test_data25_seq_030", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [338, 169, 386, 195],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [339, 163, 387, 189],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [343, 173, 391, 199],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [349, 177, 397, 203],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [341, 177, 389, 203],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.71\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_030.mp4"}, {"id": "test_data01_seq_054", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [193, 22, 243, 78],\n      \"class_name\": \"car\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [194, 26, 244, 82],\n      \"class_name\": \"car\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [193, 24, 243, 80],\n      \"class_name\": \"car\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [194, 16, 244, 72],\n      \"class_name\": \"car\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [188, 7, 238, 63],\n      \"class_name\": \"car\",\n      \"confidence\": 0.94\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_054.mp4"}, {"id": "test_data01_seq_043", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [101, 39, 155, 95],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [94, 48, 148, 104],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [92, 53, 146, 109],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [99, 60, 153, 116],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [100, 70, 154, 126],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.78\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_043.mp4"}, {"id": "test_data07_seq_014", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [139, 192, 189, 212],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.7\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [138, 194, 188, 214],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [146, 184, 196, 204],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [136, 188, 186, 208],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [145, 186, 195, 206],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.77\n    }\n  ]\n}"}], "video": "data/videos/data07_seq_014.mp4"}, {"id": "test_data25_seq_039", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [234, 313, 290, 357],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [226, 323, 282, 367],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [235, 324, 291, 368],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [237, 315, 293, 359],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [239, 325, 295, 369],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.71\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_039.mp4"}, {"id": "test_data05_seq_012", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [124, 62, 166, 84],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [133, 70, 175, 92],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [126, 60, 168, 82],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [122, 52, 164, 74],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [131, 42, 173, 64],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.82\n    }\n  ]\n}"}], "video": "data/videos/data05_seq_012.mp4"}, {"id": "test_data02_seq_015", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [95, 186, 123, 224],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [103, 188, 131, 226],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [98, 178, 126, 216],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [91, 168, 119, 206],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.7\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [100, 167, 128, 205],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.73\n    }\n  ]\n}"}], "video": "data/videos/data02_seq_015.mp4"}, {"id": "test_data23_seq_002", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [318, 238, 364, 280],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [315, 236, 361, 278],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [315, 230, 361, 272],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [306, 232, 352, 274],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.7\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [309, 242, 355, 284],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.74\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_002.mp4"}, {"id": "test_data02_seq_012", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [11, 54, 63, 90],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [8, 62, 60, 98],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [16, 58, 68, 94],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [6, 65, 58, 101],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [11, 68, 63, 104],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.81\n    }\n  ]\n}"}], "video": "data/videos/data02_seq_012.mp4"}, {"id": "test_data04_seq_005", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [32, 173, 80, 213],\n      \"class_name\": \"car\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [42, 178, 90, 218],\n      \"class_name\": \"car\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [41, 182, 89, 222],\n      \"class_name\": \"car\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [37, 180, 85, 220],\n      \"class_name\": \"car\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [30, 180, 78, 220],\n      \"class_name\": \"car\",\n      \"confidence\": 0.83\n    }\n  ]\n}"}], "video": "data/videos/data04_seq_005.mp4"}, {"id": "test_data25_seq_015", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [241, 218, 273, 262],\n      \"class_name\": \"car\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [231, 209, 263, 253],\n      \"class_name\": \"car\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [224, 199, 256, 243],\n      \"class_name\": \"car\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [231, 203, 263, 247],\n      \"class_name\": \"car\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [222, 195, 254, 239],\n      \"class_name\": \"car\",\n      \"confidence\": 0.95\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_015.mp4"}, {"id": "test_data25_seq_037", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [341, 281, 397, 307],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [337, 284, 393, 310],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [336, 286, 392, 312],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [336, 285, 392, 311],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [329, 287, 385, 313],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.73\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_037.mp4"}, {"id": "test_data05_seq_003", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [56, 124, 80, 182],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [51, 126, 75, 184],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [59, 118, 83, 176],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [61, 121, 85, 179],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [59, 116, 83, 174],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.8\n    }\n  ]\n}"}], "video": "data/videos/data05_seq_003.mp4"}, {"id": "test_data25_seq_042", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [242, 187, 296, 213],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [243, 196, 297, 222],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [237, 192, 291, 218],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [245, 182, 299, 208],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [240, 179, 294, 205],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.75\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_042.mp4"}, {"id": "test_data07_seq_007", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [35, 138, 67, 182],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [39, 141, 71, 185],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [40, 148, 72, 192],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [31, 148, 63, 192],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [26, 141, 58, 185],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.75\n    }\n  ]\n}"}], "video": "data/videos/data07_seq_007.mp4"}, {"id": "test_data07_seq_012", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [173, 147, 213, 183],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [182, 144, 222, 180],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [179, 141, 219, 177],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [172, 140, 212, 176],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [165, 146, 205, 182],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.73\n    }\n  ]\n}"}], "video": "data/videos/data07_seq_012.mp4"}, {"id": "test_data02_seq_026", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [145, 32, 195, 74],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [138, 32, 188, 74],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [133, 32, 183, 74],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [124, 35, 174, 77],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [126, 40, 176, 82],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.87\n    }\n  ]\n}"}], "video": "data/videos/data02_seq_026.mp4"}, {"id": "test_data23_seq_020", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [345, 293, 369, 341],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [343, 285, 367, 333],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [344, 294, 368, 342],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [351, 299, 375, 347],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [356, 304, 380, 352],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.76\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_020.mp4"}, {"id": "test_data01_seq_047", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [160, 101, 202, 133],\n      \"class_name\": \"car\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [158, 105, 200, 137],\n      \"class_name\": \"car\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [152, 97, 194, 129],\n      \"class_name\": \"car\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [154, 104, 196, 136],\n      \"class_name\": \"car\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [155, 97, 197, 129],\n      \"class_name\": \"car\",\n      \"confidence\": 0.8\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_047.mp4"}, {"id": "test_data26_seq_005", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [384, 299, 408, 337],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [380, 301, 404, 339],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [388, 298, 412, 336],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [392, 308, 416, 346],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [384, 309, 408, 347],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.88\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_005.mp4"}, {"id": "test_data26_seq_017", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [342, 332, 362, 364],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [334, 331, 354, 363],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [328, 337, 348, 369],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [321, 331, 341, 363],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [330, 329, 350, 361],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.88\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_017.mp4"}, {"id": "test_data23_seq_027", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [199, 323, 243, 343],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [204, 327, 248, 347],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [204, 325, 248, 345],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [209, 326, 253, 346],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [202, 318, 246, 338],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.95\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_027.mp4"}, {"id": "test_data04_seq_003", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [64, 23, 116, 69],\n      \"class_name\": \"car\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [59, 20, 111, 66],\n      \"class_name\": \"car\",\n      \"confidence\": 0.7\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [61, 28, 113, 74],\n      \"class_name\": \"car\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [60, 18, 112, 64],\n      \"class_name\": \"car\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [50, 26, 102, 72],\n      \"class_name\": \"car\",\n      \"confidence\": 0.81\n    }\n  ]\n}"}], "video": "data/videos/data04_seq_003.mp4"}, {"id": "test_data25_seq_000", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [303, 321, 337, 363],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [297, 328, 331, 370],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [297, 322, 331, 364],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [291, 312, 325, 354],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [282, 319, 316, 361],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.8\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_000.mp4"}, {"id": "test_data01_seq_015", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [84, 23, 124, 43],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [75, 13, 115, 33],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [68, 18, 108, 38],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [70, 12, 110, 32],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [78, 6, 118, 26],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.78\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_015.mp4"}, {"id": "test_data23_seq_030", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [355, 174, 403, 210],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [351, 164, 399, 200],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [351, 167, 399, 203],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [357, 168, 405, 204],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [349, 176, 397, 212],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.78\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_030.mp4"}, {"id": "test_data26_seq_038", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [366, 141, 414, 185],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [365, 144, 413, 188],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [375, 146, 423, 190],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [371, 149, 419, 193],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [361, 141, 409, 185],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.87\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_038.mp4"}, {"id": "test_data26_seq_053", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [360, 257, 412, 281],\n      \"class_name\": \"car\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [350, 248, 402, 272],\n      \"class_name\": \"car\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [340, 254, 392, 278],\n      \"class_name\": \"car\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [338, 250, 390, 274],\n      \"class_name\": \"car\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [329, 242, 381, 266],\n      \"class_name\": \"car\",\n      \"confidence\": 0.7\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_053.mp4"}, {"id": "test_data02_seq_016", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [23, 141, 71, 161],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [20, 138, 68, 158],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [13, 141, 61, 161],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [18, 135, 66, 155],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [23, 134, 71, 154],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.72\n    }\n  ]\n}"}], "video": "data/videos/data02_seq_016.mp4"}, {"id": "test_data06_seq_013", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [39, 89, 63, 139],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [48, 95, 72, 145],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [47, 86, 71, 136],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [44, 76, 68, 126],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [40, 78, 64, 128],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.81\n    }\n  ]\n}"}], "video": "data/videos/data06_seq_013.mp4"}, {"id": "test_data26_seq_022", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [257, 287, 277, 319],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [263, 277, 283, 309],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [260, 286, 280, 318],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [259, 281, 279, 313],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [265, 288, 285, 320],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.81\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_022.mp4"}, {"id": "test_data25_seq_057", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [267, 297, 303, 351],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [268, 303, 304, 357],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [270, 295, 306, 349],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [271, 300, 307, 354],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [274, 300, 310, 354],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.91\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_057.mp4"}, {"id": "test_data07_seq_005", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [29, 194, 65, 252],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [31, 192, 67, 250],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [22, 184, 58, 242],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [14, 191, 50, 249],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [16, 201, 52, 256],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.9\n    }\n  ]\n}"}], "video": "data/videos/data07_seq_005.mp4"}, {"id": "test_data26_seq_011", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [249, 183, 275, 225],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [256, 173, 282, 215],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [253, 178, 279, 220],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [258, 170, 284, 212],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [251, 164, 277, 206],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.82\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_011.mp4"}, {"id": "test_data01_seq_041", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [37, 93, 77, 149],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [45, 92, 85, 148],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [45, 100, 85, 156],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [44, 103, 84, 159],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [37, 93, 77, 149],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.92\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_041.mp4"}, {"id": "test_data01_seq_016", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [52, 62, 88, 94],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [60, 56, 96, 88],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [65, 59, 101, 91],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [69, 55, 105, 87],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [64, 63, 100, 95],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.87\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_016.mp4"}, {"id": "test_data26_seq_037", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [279, 328, 303, 384],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [289, 333, 313, 389],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [290, 328, 314, 384],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [293, 318, 317, 374],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [284, 318, 308, 374],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.84\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_037.mp4"}, {"id": "test_data01_seq_028", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [53, 103, 105, 141],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [46, 110, 98, 148],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [37, 111, 89, 149],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [46, 115, 98, 153],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [56, 115, 108, 153],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.94\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_028.mp4"}, {"id": "test_data25_seq_058", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [308, 276, 358, 330],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [307, 281, 357, 335],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [310, 281, 360, 335],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [316, 285, 366, 339],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [313, 276, 363, 330],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.9\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_058.mp4"}, {"id": "test_data02_seq_029", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [166, 107, 186, 131],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [168, 100, 188, 124],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [174, 93, 194, 117],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [177, 97, 197, 121],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.7\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [173, 93, 193, 117],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.79\n    }\n  ]\n}"}], "video": "data/videos/data02_seq_029.mp4"}, {"id": "test_data02_seq_007", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [128, 0, 182, 49],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [126, 0, 180, 41],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [131, 0, 185, 42],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [141, 0, 195, 41],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [145, 0, 199, 39],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.88\n    }\n  ]\n}"}], "video": "data/videos/data02_seq_007.mp4"}, {"id": "test_data23_seq_017", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [199, 323, 243, 343],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [204, 327, 248, 347],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [204, 325, 248, 345],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [209, 326, 253, 346],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [202, 318, 246, 338],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.95\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_017.mp4"}, {"id": "test_data05_seq_001", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [12, 45, 50, 83],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [11, 49, 49, 87],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [2, 48, 40, 86],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [2, 56, 40, 94],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [0, 60, 38, 98],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.74\n    }\n  ]\n}"}], "video": "data/videos/data05_seq_001.mp4"}, {"id": "test_data26_seq_036", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [316, 316, 376, 368],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.7\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [307, 313, 367, 365],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [303, 313, 363, 365],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [297, 304, 357, 356],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [303, 298, 363, 350],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.89\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_036.mp4"}, {"id": "test_data01_seq_038", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [104, 63, 146, 117],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [105, 56, 147, 110],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [100, 49, 142, 103],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [106, 56, 148, 110],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [115, 61, 157, 115],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.86\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_038.mp4"}, {"id": "test_data01_seq_056", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [54, 54, 112, 84],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [54, 57, 112, 87],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.7\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [53, 60, 111, 90],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [60, 54, 118, 84],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [61, 48, 119, 78],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.75\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_056.mp4"}, {"id": "test_data26_seq_023", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [313, 178, 353, 224],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [308, 175, 348, 221],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [298, 168, 338, 214],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [301, 161, 341, 207],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [291, 159, 331, 205],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.9\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_023.mp4"}, {"id": "test_data23_seq_019", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [280, 188, 336, 214],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [283, 183, 339, 209],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [293, 175, 349, 201],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [294, 171, 350, 197],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [288, 169, 344, 195],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.8\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_019.mp4"}, {"id": "test_data23_seq_006", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [279, 170, 329, 204],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [269, 176, 319, 210],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [266, 183, 316, 217],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [261, 181, 311, 215],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [255, 189, 305, 223],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.77\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_006.mp4"}, {"id": "test_data23_seq_000", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [348, 144, 378, 166],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [339, 144, 369, 166],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [338, 147, 368, 169],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [343, 156, 373, 178],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [340, 152, 370, 174],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.73\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_000.mp4"}, {"id": "test_data02_seq_023", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [56, 189, 84, 235],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [57, 192, 85, 238],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.7\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [56, 190, 84, 236],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [50, 197, 78, 243],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [57, 189, 85, 235],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.73\n    }\n  ]\n}"}], "video": "data/videos/data02_seq_023.mp4"}, {"id": "test_data26_seq_057", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [392, 159, 422, 193],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [383, 154, 413, 188],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [376, 163, 406, 197],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [380, 164, 410, 198],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [387, 164, 417, 198],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.76\n    }\n  ]\n}"}], "video": "data/videos/data26_seq_057.mp4"}, {"id": "test_data23_seq_024", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [265, 152, 321, 198],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [262, 150, 318, 196],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [267, 159, 323, 205],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [265, 162, 321, 208],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [255, 164, 311, 210],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.89\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_024.mp4"}, {"id": "test_data25_seq_047", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [217, 279, 273, 333],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [227, 279, 283, 333],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [237, 272, 293, 326],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [233, 266, 289, 320],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [241, 257, 297, 311],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.88\n    }\n  ]\n}"}], "video": "data/videos/data25_seq_047.mp4"}, {"id": "test_data23_seq_034", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [242, 326, 272, 376],\n      \"class_name\": \"car\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [250, 331, 280, 381],\n      \"class_name\": \"car\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [250, 334, 280, 384],\n      \"class_name\": \"car\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [247, 328, 277, 378],\n      \"class_name\": \"car\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [244, 326, 274, 376],\n      \"class_name\": \"car\",\n      \"confidence\": 0.9\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_034.mp4"}]