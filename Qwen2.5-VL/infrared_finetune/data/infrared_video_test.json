[{"id": "test_data23_seq_015", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [348, 240, 355, 247],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [348, 240, 356, 247],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [348, 239, 356, 245],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [348, 239, 355, 247],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [348, 240, 355, 246],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_015.mp4"}, {"id": "test_data23_seq_033", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [348, 243, 356, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [348, 243, 355, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [348, 241, 356, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [350, 243, 356, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [350, 243, 356, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_033.mp4"}, {"id": "test_data01_seq_040", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [67, 111, 72, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [66, 111, 71, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [67, 111, 72, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [66, 111, 71, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [65, 111, 70, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_040.mp4"}, {"id": "test_data01_seq_022", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [63, 109, 68, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [66, 109, 71, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [68, 109, 73, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [68, 109, 73, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [68, 109, 73, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_022.mp4"}, {"id": "test_data01_seq_059", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [73, 112, 78, 117],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [73, 112, 78, 117],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [71, 112, 76, 117],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [71, 112, 76, 117],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [71, 112, 76, 117],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_059.mp4"}, {"id": "test_data01_seq_026", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [71, 110, 76, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [71, 110, 76, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [70, 110, 75, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [69, 110, 74, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [68, 110, 73, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_026.mp4"}, {"id": "test_data01_seq_003", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [69, 106, 74, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [68, 106, 73, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [68, 106, 73, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [68, 106, 73, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [67, 106, 72, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_003.mp4"}, {"id": "test_data01_seq_020", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [67, 110, 72, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [66, 110, 71, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [66, 110, 71, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [65, 110, 70, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [65, 110, 70, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_020.mp4"}, {"id": "test_data23_seq_028", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [350, 243, 356, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [348, 244, 356, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [350, 243, 358, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [351, 244, 357, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [350, 243, 356, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_028.mp4"}, {"id": "test_data01_seq_008", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [62, 106, 67, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [62, 106, 67, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [60, 105, 65, 110],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [60, 106, 65, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [64, 106, 69, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_008.mp4"}, {"id": "test_data23_seq_031", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [348, 242, 356, 249],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [350, 244, 356, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [350, 242, 356, 249],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [350, 242, 356, 249],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [350, 242, 356, 249],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_031.mp4"}, {"id": "test_data01_seq_013", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [71, 107, 76, 112],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [70, 107, 75, 112],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [68, 108, 73, 113],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [68, 108, 73, 113],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [67, 107, 72, 112],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_013.mp4"}, {"id": "test_data01_seq_052", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [73, 112, 78, 117],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [72, 112, 77, 117],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [71, 112, 76, 117],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [71, 112, 76, 117],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [71, 112, 76, 117],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_052.mp4"}, {"id": "test_data01_seq_044", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [68, 111, 73, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [68, 111, 73, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [68, 111, 73, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [70, 111, 75, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [71, 111, 76, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_044.mp4"}, {"id": "test_data23_seq_013", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [348, 245, 355, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [348, 245, 355, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [347, 244, 356, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [347, 246, 355, 253],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [348, 244, 355, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_013.mp4"}, {"id": "test_data01_seq_033", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [69, 111, 74, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [69, 111, 74, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [68, 111, 73, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [68, 111, 73, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [66, 111, 71, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_033.mp4"}, {"id": "test_data01_seq_046", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [70, 111, 75, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [70, 111, 75, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [68, 111, 73, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [68, 111, 73, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [68, 111, 73, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_046.mp4"}, {"id": "test_data01_seq_024", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [65, 109, 70, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [65, 109, 70, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [63, 109, 68, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [63, 109, 68, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [62, 110, 67, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_024.mp4"}, {"id": "test_data01_seq_031", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [63, 110, 68, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [62, 110, 67, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [63, 110, 68, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [66, 110, 71, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [70, 110, 75, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_031.mp4"}, {"id": "test_data01_seq_037", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [66, 111, 71, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [65, 110, 70, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [65, 111, 70, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [65, 111, 70, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [65, 111, 70, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_037.mp4"}, {"id": "test_data01_seq_036", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [69, 111, 74, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [69, 111, 74, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [68, 111, 73, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [68, 111, 73, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [68, 111, 73, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_036.mp4"}, {"id": "test_data01_seq_004", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [65, 106, 70, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [65, 106, 70, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [65, 106, 70, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [64, 106, 69, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [63, 106, 68, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_004.mp4"}, {"id": "test_data23_seq_012", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [350, 246, 356, 253],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [348, 246, 355, 253],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [348, 245, 356, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [348, 246, 355, 253],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [348, 245, 355, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_012.mp4"}, {"id": "test_data01_seq_019", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [68, 109, 73, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [69, 110, 74, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [69, 109, 74, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [69, 110, 74, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [68, 110, 73, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_019.mp4"}, {"id": "test_data23_seq_018", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [351, 242, 357, 249],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [351, 242, 357, 248],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [350, 241, 358, 248],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [351, 243, 359, 249],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [350, 243, 356, 249],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_018.mp4"}, {"id": "test_data01_seq_057", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [68, 111, 73, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [71, 112, 76, 117],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [73, 111, 78, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [74, 112, 79, 117],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [75, 112, 80, 117],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_057.mp4"}, {"id": "test_data01_seq_055", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [74, 111, 79, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [74, 112, 79, 117],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [72, 111, 77, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [73, 111, 78, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [71, 111, 76, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_055.mp4"}, {"id": "test_data23_seq_014", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [348, 243, 356, 249],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [348, 241, 356, 248],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [348, 241, 356, 248],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [348, 242, 355, 248],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [350, 240, 356, 246],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_014.mp4"}, {"id": "test_data01_seq_053", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [69, 112, 74, 117],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [69, 112, 74, 117],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [68, 112, 73, 117],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [68, 112, 73, 117],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [68, 112, 73, 117],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_053.mp4"}, {"id": "test_data23_seq_004", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [351, 244, 357, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [350, 244, 356, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [349, 246, 355, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [349, 243, 355, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [348, 244, 355, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_004.mp4"}, {"id": "test_data23_seq_025", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [350, 244, 356, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [350, 244, 356, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [348, 243, 356, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [348, 243, 355, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [348, 243, 356, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_025.mp4"}, {"id": "test_data23_seq_021", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [347, 242, 355, 249],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [347, 242, 355, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [347, 241, 356, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [347, 241, 356, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [348, 242, 356, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_021.mp4"}, {"id": "test_data23_seq_032", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [350, 242, 356, 249],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [350, 242, 356, 249],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [348, 242, 356, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [350, 242, 356, 249],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [348, 243, 356, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_032.mp4"}, {"id": "test_data23_seq_007", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [348, 245, 355, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [348, 245, 355, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [348, 246, 355, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [348, 245, 355, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [348, 245, 355, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_007.mp4"}, {"id": "test_data01_seq_009", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [66, 106, 71, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [68, 106, 73, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [57, 106, 62, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [56, 106, 61, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [55, 107, 60, 112],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_009.mp4"}, {"id": "test_data01_seq_058", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [74, 112, 79, 117],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [74, 112, 79, 117],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [74, 112, 79, 117],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [74, 112, 79, 117],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [74, 112, 79, 117],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_058.mp4"}, {"id": "test_data01_seq_011", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [59, 106, 64, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [59, 106, 64, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [59, 107, 64, 112],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [59, 106, 64, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [59, 106, 64, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_011.mp4"}, {"id": "test_data01_seq_000", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [68, 106, 73, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [67, 106, 72, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [66, 106, 71, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [67, 106, 72, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [66, 106, 71, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_000.mp4"}, {"id": "test_data01_seq_049", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [71, 111, 76, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [71, 111, 76, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [70, 111, 75, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [69, 111, 74, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [68, 111, 73, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_049.mp4"}, {"id": "test_data01_seq_002", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [62, 106, 67, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [62, 106, 67, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [64, 106, 69, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [68, 106, 73, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [70, 106, 75, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_002.mp4"}, {"id": "test_data01_seq_027", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [68, 110, 73, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [66, 110, 71, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [65, 110, 70, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [65, 109, 70, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [65, 109, 70, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_027.mp4"}, {"id": "test_data23_seq_001", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [348, 246, 355, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [349, 247, 355, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [348, 244, 355, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [349, 245, 355, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [349, 245, 355, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_001.mp4"}, {"id": "test_data01_seq_042", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [75, 111, 80, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [74, 110, 79, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [74, 111, 79, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [74, 110, 79, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [73, 111, 78, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_042.mp4"}, {"id": "test_data23_seq_026", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [348, 244, 355, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [348, 243, 355, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [348, 243, 356, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [348, 244, 356, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [348, 244, 356, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_026.mp4"}, {"id": "test_data23_seq_016", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [348, 240, 356, 248],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [348, 239, 355, 245],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [348, 240, 355, 247],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [348, 240, 356, 248],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [348, 240, 355, 246],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_016.mp4"}, {"id": "test_data01_seq_023", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [67, 109, 72, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [66, 109, 71, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [65, 109, 70, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [65, 108, 70, 113],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [65, 109, 70, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_023.mp4"}, {"id": "test_data01_seq_006", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [70, 106, 75, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [70, 106, 75, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [69, 106, 74, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [68, 106, 73, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [68, 106, 73, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_006.mp4"}, {"id": "test_data01_seq_005", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [62, 106, 67, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [61, 106, 66, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [61, 107, 66, 112],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [64, 106, 69, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [68, 106, 73, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_005.mp4"}, {"id": "test_data01_seq_039", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [62, 111, 67, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [62, 111, 67, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [65, 111, 70, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [67, 111, 72, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [68, 111, 73, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_039.mp4"}, {"id": "test_data01_seq_007", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [67, 106, 72, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [66, 106, 71, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [65, 106, 70, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [65, 106, 70, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [64, 106, 69, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_007.mp4"}, {"id": "test_data01_seq_051", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [71, 111, 76, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [73, 111, 78, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [74, 111, 79, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [74, 111, 79, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [73, 112, 78, 117],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_051.mp4"}, {"id": "test_data01_seq_021", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [64, 110, 69, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [62, 110, 67, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [62, 109, 67, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [61, 109, 66, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [61, 109, 66, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_021.mp4"}, {"id": "test_data01_seq_018", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [62, 109, 67, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [62, 109, 67, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [60, 109, 65, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [61, 109, 66, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [64, 109, 69, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_018.mp4"}, {"id": "test_data01_seq_017", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [65, 108, 70, 113],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [64, 109, 69, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [64, 109, 69, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [64, 109, 69, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [62, 109, 67, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_017.mp4"}, {"id": "test_data23_seq_011", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [350, 245, 356, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [348, 244, 356, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [350, 245, 356, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [350, 245, 356, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [348, 246, 355, 253],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_011.mp4"}, {"id": "test_data01_seq_030", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [66, 110, 71, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [66, 110, 71, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [66, 110, 71, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [65, 110, 70, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [65, 110, 70, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_030.mp4"}, {"id": "test_data01_seq_050", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [68, 111, 73, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [67, 111, 72, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [66, 111, 71, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [66, 111, 71, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [68, 111, 73, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_050.mp4"}, {"id": "test_data23_seq_009", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [348, 244, 356, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [348, 242, 356, 249],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [350, 244, 356, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [348, 244, 356, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [348, 244, 356, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_009.mp4"}, {"id": "test_data23_seq_008", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [348, 244, 355, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [348, 245, 355, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [348, 245, 356, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [348, 245, 355, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [348, 245, 355, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_008.mp4"}, {"id": "test_data01_seq_012", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [57, 106, 62, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [58, 107, 63, 112],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [62, 107, 67, 112],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [68, 107, 73, 112],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [70, 107, 75, 112],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_012.mp4"}, {"id": "test_data01_seq_025", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [62, 110, 67, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [65, 110, 70, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [69, 110, 74, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [71, 110, 76, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [71, 110, 76, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_025.mp4"}, {"id": "test_data01_seq_014", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [66, 107, 71, 112],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [65, 108, 70, 113],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [65, 107, 70, 112],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [63, 107, 68, 112],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [62, 108, 67, 113],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_014.mp4"}, {"id": "test_data23_seq_010", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [348, 243, 356, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [350, 245, 358, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [350, 245, 358, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [348, 245, 358, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [350, 244, 356, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_010.mp4"}, {"id": "test_data01_seq_035", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [68, 111, 73, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [70, 111, 75, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [71, 111, 76, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [71, 111, 76, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [70, 111, 75, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_035.mp4"}, {"id": "test_data01_seq_032", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [73, 110, 78, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [72, 110, 77, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [72, 110, 77, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [71, 110, 76, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [71, 111, 76, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_032.mp4"}, {"id": "test_data23_seq_003", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [351, 243, 357, 249],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [351, 243, 355, 248],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [350, 244, 356, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [350, 244, 356, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [349, 244, 355, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_003.mp4"}, {"id": "test_data01_seq_029", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [70, 109, 75, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [68, 109, 73, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [68, 110, 73, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [68, 110, 73, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [67, 110, 72, 115],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_029.mp4"}, {"id": "test_data01_seq_045", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [71, 111, 76, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [71, 111, 76, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [71, 111, 76, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [71, 111, 76, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [70, 111, 75, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_045.mp4"}, {"id": "test_data01_seq_048", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [71, 111, 76, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [72, 111, 77, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [71, 111, 76, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [71, 111, 76, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [71, 111, 76, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_048.mp4"}, {"id": "test_data01_seq_010", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [55, 106, 60, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [54, 106, 59, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [56, 106, 61, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [58, 106, 63, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [59, 106, 64, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_010.mp4"}, {"id": "test_data23_seq_029", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [351, 245, 357, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [350, 245, 358, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [350, 245, 358, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [350, 245, 358, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [350, 243, 356, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_029.mp4"}, {"id": "test_data23_seq_005", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [349, 246, 355, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [348, 245, 355, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [348, 244, 355, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [348, 245, 355, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [349, 245, 355, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_005.mp4"}, {"id": "test_data23_seq_022", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [350, 242, 358, 249],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [348, 242, 358, 249],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [349, 241, 359, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [349, 243, 359, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [349, 242, 359, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_022.mp4"}, {"id": "test_data01_seq_001", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [65, 106, 70, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [65, 107, 70, 112],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [64, 106, 69, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [63, 106, 68, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [62, 106, 67, 111],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_001.mp4"}, {"id": "test_data01_seq_034", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [66, 111, 71, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [66, 111, 71, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [65, 111, 70, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [65, 111, 70, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [66, 111, 71, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_034.mp4"}, {"id": "test_data23_seq_023", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [351, 243, 360, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [351, 243, 360, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [352, 243, 360, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [351, 243, 360, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [351, 243, 360, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_023.mp4"}, {"id": "test_data01_seq_054", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [69, 112, 74, 117],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [71, 112, 76, 117],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [74, 111, 79, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [74, 111, 79, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [74, 111, 79, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_054.mp4"}, {"id": "test_data01_seq_043", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [71, 111, 76, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [71, 111, 76, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [71, 111, 76, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [70, 111, 75, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [68, 111, 73, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_043.mp4"}, {"id": "test_data23_seq_002", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [348, 244, 355, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [349, 245, 355, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [348, 244, 355, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [349, 244, 355, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [351, 243, 355, 248],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_002.mp4"}, {"id": "test_data23_seq_020", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [347, 241, 355, 249],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [348, 241, 355, 248],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [347, 242, 355, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [346, 240, 356, 249],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [345, 242, 355, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_020.mp4"}, {"id": "test_data01_seq_047", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [66, 111, 71, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [66, 111, 71, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [66, 111, 71, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [68, 111, 73, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [71, 111, 76, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_047.mp4"}, {"id": "test_data23_seq_027", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [348, 242, 356, 249],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [348, 243, 355, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [348, 243, 355, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [348, 243, 356, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [348, 244, 356, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_027.mp4"}, {"id": "test_data01_seq_015", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [62, 108, 67, 113],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [61, 108, 66, 113],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [61, 108, 66, 113],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [62, 108, 67, 113],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [64, 109, 69, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_015.mp4"}, {"id": "test_data23_seq_030", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [350, 242, 356, 249],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [350, 242, 356, 249],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [349, 244, 355, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [350, 244, 356, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [350, 243, 356, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_030.mp4"}, {"id": "test_data01_seq_041", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [67, 111, 72, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [70, 111, 75, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [74, 111, 79, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [77, 111, 82, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [76, 111, 81, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_041.mp4"}, {"id": "test_data01_seq_016", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [66, 109, 71, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [66, 109, 71, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [65, 109, 70, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [65, 109, 70, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [65, 109, 70, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_016.mp4"}, {"id": "test_data01_seq_028", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [63, 109, 68, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [64, 109, 69, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [65, 109, 70, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [67, 109, 72, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [69, 109, 74, 114],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_028.mp4"}, {"id": "test_data23_seq_017", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [348, 242, 356, 248],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [350, 242, 358, 249],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [348, 243, 355, 248],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [351, 243, 357, 249],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [351, 243, 359, 249],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_017.mp4"}, {"id": "test_data01_seq_038", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [66, 111, 71, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [63, 111, 68, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [62, 111, 67, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [62, 111, 67, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [62, 111, 67, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_038.mp4"}, {"id": "test_data01_seq_056", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [71, 111, 76, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [71, 111, 76, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [70, 111, 75, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [70, 111, 75, 116],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [68, 112, 73, 117],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_056.mp4"}, {"id": "test_data23_seq_019", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [348, 241, 355, 248],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [348, 240, 355, 248],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [348, 242, 355, 248],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [348, 241, 355, 248],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [347, 241, 356, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_019.mp4"}, {"id": "test_data23_seq_006", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [348, 246, 356, 253],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [348, 245, 355, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [348, 245, 356, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [348, 246, 356, 253],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [348, 245, 355, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_006.mp4"}, {"id": "test_data23_seq_000", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [348, 243, 355, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [348, 244, 355, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [348, 244, 356, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [348, 245, 355, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [348, 245, 355, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_000.mp4"}, {"id": "test_data23_seq_024", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [349, 244, 359, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [349, 242, 359, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [351, 245, 357, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [351, 244, 357, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [351, 244, 357, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_024.mp4"}, {"id": "test_data23_seq_034", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [350, 244, 356, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [349, 244, 355, 251],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [350, 243, 356, 250],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [350, 244, 358, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [350, 244, 356, 252],\n      \"class_name\": \"target\",\n      \"confidence\": 1.0\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_034.mp4"}]