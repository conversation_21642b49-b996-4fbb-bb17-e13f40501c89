[{"video": "videos/data01_seq_058.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"278\":[{\"bbox\":[72,111,77,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"283\":[{\"bbox\":[70,111,75,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"288\":[{\"bbox\":[73,111,78,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"293\":[{\"bbox\":[74,112,79,117],\"class_name\":\"drone\",\"confidence\":1.0}],\"298\":[{\"bbox\":[71,112,76,117],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data01_seq_049.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"229\":[{\"bbox\":[71,111,76,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"234\":[{\"bbox\":[68,111,73,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"239\":[{\"bbox\":[68,111,73,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"244\":[{\"bbox\":[71,111,76,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"249\":[{\"bbox\":[69,111,74,116],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data01_seq_050.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"250\":[{\"bbox\":[68,111,73,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"255\":[{\"bbox\":[68,111,73,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"260\":[{\"bbox\":[73,112,78,117],\"class_name\":\"drone\",\"confidence\":1.0}],\"265\":[{\"bbox\":[71,112,76,117],\"class_name\":\"drone\",\"confidence\":1.0}],\"270\":[{\"bbox\":[68,112,73,117],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data01_seq_030.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"150\":[{\"bbox\":[67,110,72,115],\"class_name\":\"drone\",\"confidence\":1.0}],\"155\":[{\"bbox\":[65,110,70,115],\"class_name\":\"drone\",\"confidence\":1.0}],\"160\":[{\"bbox\":[70,110,75,115],\"class_name\":\"drone\",\"confidence\":1.0}],\"165\":[{\"bbox\":[71,111,76,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"170\":[{\"bbox\":[66,111,71,116],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data01_seq_032.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"152\":[{\"bbox\":[66,110,71,115],\"class_name\":\"drone\",\"confidence\":1.0}],\"157\":[{\"bbox\":[62,110,67,115],\"class_name\":\"drone\",\"confidence\":1.0}],\"162\":[{\"bbox\":[72,110,77,115],\"class_name\":\"drone\",\"confidence\":1.0}],\"167\":[{\"bbox\":[69,111,74,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"172\":[{\"bbox\":[66,111,71,116],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data01_seq_059.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"279\":[{\"bbox\":[73,111,78,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"284\":[{\"bbox\":[70,111,75,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"289\":[{\"bbox\":[74,112,79,117],\"class_name\":\"drone\",\"confidence\":1.0}],\"294\":[{\"bbox\":[74,112,79,117],\"class_name\":\"drone\",\"confidence\":1.0}],\"299\":[{\"bbox\":[71,112,76,117],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data01_seq_018.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"78\":[{\"bbox\":[61,108,66,113],\"class_name\":\"drone\",\"confidence\":1.0}],\"83\":[{\"bbox\":[65,109,70,114],\"class_name\":\"drone\",\"confidence\":1.0}],\"88\":[{\"bbox\":[64,109,69,114],\"class_name\":\"drone\",\"confidence\":1.0}],\"93\":[{\"bbox\":[60,109,65,114],\"class_name\":\"drone\",\"confidence\":1.0}],\"98\":[{\"bbox\":[69,109,74,114],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data01_seq_012.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"52\":[{\"bbox\":[54,106,59,111],\"class_name\":\"drone\",\"confidence\":1.0}],\"57\":[{\"bbox\":[59,106,64,111],\"class_name\":\"drone\",\"confidence\":1.0}],\"62\":[{\"bbox\":[58,107,63,112],\"class_name\":\"drone\",\"confidence\":1.0}],\"67\":[{\"bbox\":[70,107,75,112],\"class_name\":\"drone\",\"confidence\":1.0}],\"72\":[{\"bbox\":[65,108,70,113],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data01_seq_025.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"125\":[{\"bbox\":[62,110,67,115],\"class_name\":\"drone\",\"confidence\":1.0}],\"130\":[{\"bbox\":[71,110,76,115],\"class_name\":\"drone\",\"confidence\":1.0}],\"135\":[{\"bbox\":[68,110,73,115],\"class_name\":\"drone\",\"confidence\":1.0}],\"140\":[{\"bbox\":[65,109,70,114],\"class_name\":\"drone\",\"confidence\":1.0}],\"145\":[{\"bbox\":[69,109,74,114],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data01_seq_042.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"202\":[{\"bbox\":[66,111,71,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"207\":[{\"bbox\":[70,111,75,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"212\":[{\"bbox\":[74,110,79,115],\"class_name\":\"drone\",\"confidence\":1.0}],\"217\":[{\"bbox\":[71,111,76,116],\"class_name\":\"drone\",\"confidence\":1.0}],\"222\":[{\"bbox\":[68,111,73,116],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data01_seq_005.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"25\":[{\"bbox\":[63,106,68,111],\"class_name\":\"drone\",\"confidence\":1.0}],\"30\":[{\"bbox\":[68,106,73,111],\"class_name\":\"drone\",\"confidence\":1.0}],\"35\":[{\"bbox\":[68,106,73,111],\"class_name\":\"drone\",\"confidence\":1.0}],\"40\":[{\"bbox\":[64,106,69,111],\"class_name\":\"drone\",\"confidence\":1.0}],\"45\":[{\"bbox\":[64,106,69,111],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data01_seq_026.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"126\":[{\"bbox\":[62,110,67,115],\"class_name\":\"drone\",\"confidence\":1.0}],\"131\":[{\"bbox\":[71,110,76,115],\"class_name\":\"drone\",\"confidence\":1.0}],\"136\":[{\"bbox\":[68,110,73,115],\"class_name\":\"drone\",\"confidence\":1.0}],\"141\":[{\"bbox\":[63,109,68,114],\"class_name\":\"drone\",\"confidence\":1.0}],\"146\":[{\"bbox\":[70,109,75,114],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data02_seq_014.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"54\":[{\"bbox\":[97,89,102,94],\"class_name\":\"drone\",\"confidence\":1.0}],\"59\":[{\"bbox\":[99,91,104,96],\"class_name\":\"drone\",\"confidence\":1.0}],\"64\":[{\"bbox\":[99,90,104,95],\"class_name\":\"drone\",\"confidence\":1.0}],\"69\":[{\"bbox\":[98,90,103,95],\"class_name\":\"drone\",\"confidence\":1.0}],\"74\":[{\"bbox\":[96,87,101,92],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data02_seq_004.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"4\":[{\"bbox\":[97,87,102,92],\"class_name\":\"drone\",\"confidence\":1.0}],\"9\":[{\"bbox\":[96,89,101,94],\"class_name\":\"drone\",\"confidence\":1.0}],\"14\":[{\"bbox\":[96,90,101,95],\"class_name\":\"drone\",\"confidence\":1.0}],\"19\":[{\"bbox\":[96,89,101,94],\"class_name\":\"drone\",\"confidence\":1.0}],\"24\":[{\"bbox\":[96,88,101,93],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data02_seq_013.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"53\":[{\"bbox\":[97,89,102,94],\"class_name\":\"drone\",\"confidence\":1.0}],\"58\":[{\"bbox\":[99,91,104,96],\"class_name\":\"drone\",\"confidence\":1.0}],\"63\":[{\"bbox\":[99,91,104,96],\"class_name\":\"drone\",\"confidence\":1.0}],\"68\":[{\"bbox\":[99,90,104,95],\"class_name\":\"drone\",\"confidence\":1.0}],\"73\":[{\"bbox\":[96,87,101,92],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data02_seq_018.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"78\":[{\"bbox\":[99,88,104,93],\"class_name\":\"drone\",\"confidence\":1.0}],\"83\":[{\"bbox\":[99,94,104,99],\"class_name\":\"drone\",\"confidence\":1.0}],\"88\":[{\"bbox\":[99,94,104,99],\"class_name\":\"drone\",\"confidence\":1.0}],\"93\":[{\"bbox\":[98,92,103,97],\"class_name\":\"drone\",\"confidence\":1.0}],\"98\":[{\"bbox\":[98,91,103,96],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data02_seq_008.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"28\":[{\"bbox\":[97,89,102,94],\"class_name\":\"drone\",\"confidence\":1.0}],\"33\":[{\"bbox\":[96,89,101,94],\"class_name\":\"drone\",\"confidence\":1.0}],\"38\":[{\"bbox\":[96,87,101,92],\"class_name\":\"drone\",\"confidence\":1.0}],\"43\":[{\"bbox\":[98,91,103,96],\"class_name\":\"drone\",\"confidence\":1.0}],\"48\":[{\"bbox\":[98,90,103,95],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data02_seq_021.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"101\":[{\"bbox\":[97,93,102,98],\"class_name\":\"drone\",\"confidence\":1.0}],\"106\":[{\"bbox\":[97,92,102,97],\"class_name\":\"drone\",\"confidence\":1.0}],\"111\":[{\"bbox\":[96,91,101,96],\"class_name\":\"drone\",\"confidence\":1.0}],\"116\":[{\"bbox\":[96,93,101,98],\"class_name\":\"drone\",\"confidence\":1.0}],\"121\":[{\"bbox\":[95,92,100,97],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data04_seq_013.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"53\":[{\"bbox\":[102,83,107,88],\"class_name\":\"drone\",\"confidence\":1.0}],\"58\":[{\"bbox\":[103,81,108,86],\"class_name\":\"drone\",\"confidence\":1.0}],\"63\":[{\"bbox\":[103,79,108,84],\"class_name\":\"drone\",\"confidence\":1.0}],\"68\":[{\"bbox\":[103,85,108,90],\"class_name\":\"drone\",\"confidence\":1.0}],\"73\":[{\"bbox\":[102,83,107,88],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data04_seq_006.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"26\":[{\"bbox\":[100,82,105,87],\"class_name\":\"drone\",\"confidence\":1.0}],\"31\":[{\"bbox\":[100,79,105,84],\"class_name\":\"drone\",\"confidence\":1.0}],\"36\":[{\"bbox\":[104,85,109,90],\"class_name\":\"drone\",\"confidence\":1.0}],\"41\":[{\"bbox\":[104,82,109,87],\"class_name\":\"drone\",\"confidence\":1.0}],\"46\":[{\"bbox\":[102,80,107,85],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data04_seq_014.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"54\":[{\"bbox\":[103,83,108,88],\"class_name\":\"drone\",\"confidence\":1.0}],\"59\":[{\"bbox\":[103,81,108,86],\"class_name\":\"drone\",\"confidence\":1.0}],\"64\":[{\"bbox\":[103,78,108,83],\"class_name\":\"drone\",\"confidence\":1.0}],\"69\":[{\"bbox\":[103,84,108,89],\"class_name\":\"drone\",\"confidence\":1.0}],\"74\":[{\"bbox\":[102,82,107,87],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data05_seq_006.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"26\":[{\"bbox\":[182,58,217,93],\"class_name\":\"drone\",\"confidence\":1.0}],\"31\":[{\"bbox\":[171,47,206,82],\"class_name\":\"drone\",\"confidence\":1.0}],\"36\":[{\"bbox\":[171,38,206,73],\"class_name\":\"drone\",\"confidence\":1.0}],\"41\":[{\"bbox\":[170,29,205,64],\"class_name\":\"drone\",\"confidence\":1.0}],\"46\":[{\"bbox\":[161,30,196,65],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data05_seq_005.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"25\":[{\"bbox\":[181,59,216,94],\"class_name\":\"drone\",\"confidence\":1.0}],\"30\":[{\"bbox\":[171,49,206,84],\"class_name\":\"drone\",\"confidence\":1.0}],\"35\":[{\"bbox\":[172,40,207,75],\"class_name\":\"drone\",\"confidence\":1.0}],\"40\":[{\"bbox\":[170,31,205,66],\"class_name\":\"drone\",\"confidence\":1.0}],\"45\":[{\"bbox\":[163,31,198,66],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data05_seq_011.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"51\":[{\"bbox\":[160,22,195,57],\"class_name\":\"drone\",\"confidence\":1.0}],\"56\":[{\"bbox\":[157,15,192,50],\"class_name\":\"drone\",\"confidence\":1.0}],\"61\":[{\"bbox\":[143,34,178,69],\"class_name\":\"drone\",\"confidence\":1.0}],\"66\":[{\"bbox\":[135,26,170,61],\"class_name\":\"drone\",\"confidence\":1.0}],\"71\":[{\"bbox\":[132,18,167,53],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data06_seq_003.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"3\":[{\"bbox\":[91,123,96,128],\"class_name\":\"drone\",\"confidence\":1.0}],\"8\":[{\"bbox\":[90,123,95,128],\"class_name\":\"drone\",\"confidence\":1.0}],\"13\":[{\"bbox\":[90,122,95,127],\"class_name\":\"drone\",\"confidence\":1.0}],\"18\":[{\"bbox\":[89,123,94,128],\"class_name\":\"drone\",\"confidence\":1.0}],\"23\":[{\"bbox\":[90,124,95,129],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data06_seq_010.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"50\":[{\"bbox\":[93,121,98,126],\"class_name\":\"drone\",\"confidence\":1.0}],\"55\":[{\"bbox\":[93,122,98,127],\"class_name\":\"drone\",\"confidence\":1.0}],\"60\":[{\"bbox\":[19,162,24,167],\"class_name\":\"drone\",\"confidence\":1.0}],\"65\":[{\"bbox\":[80,117,85,122],\"class_name\":\"drone\",\"confidence\":1.0}],\"70\":[{\"bbox\":[95,120,100,125],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data06_seq_009.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"29\":[{\"bbox\":[90,123,95,128],\"class_name\":\"drone\",\"confidence\":1.0}],\"34\":[{\"bbox\":[90,123,95,128],\"class_name\":\"drone\",\"confidence\":1.0}],\"39\":[{\"bbox\":[89,123,94,128],\"class_name\":\"drone\",\"confidence\":1.0}],\"44\":[{\"bbox\":[93,120,98,125],\"class_name\":\"drone\",\"confidence\":1.0}],\"49\":[{\"bbox\":[93,120,98,125],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data07_seq_003.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"3\":[{\"bbox\":[161,132,192,162],\"class_name\":\"drone\",\"confidence\":1.0}],\"8\":[{\"bbox\":[154,121,184,152],\"class_name\":\"drone\",\"confidence\":1.0}],\"13\":[{\"bbox\":[146,112,176,142],\"class_name\":\"drone\",\"confidence\":1.0}],\"18\":[{\"bbox\":[144,103,174,133],\"class_name\":\"drone\",\"confidence\":1.0}],\"23\":[{\"bbox\":[140,92,171,123],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data07_seq_000.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"0\":[{\"bbox\":[164,137,194,168],\"class_name\":\"drone\",\"confidence\":1.0}],\"5\":[{\"bbox\":[160,128,190,158],\"class_name\":\"drone\",\"confidence\":1.0}],\"10\":[{\"bbox\":[148,117,179,148],\"class_name\":\"drone\",\"confidence\":1.0}],\"15\":[{\"bbox\":[145,108,176,139],\"class_name\":\"drone\",\"confidence\":1.0}],\"20\":[{\"bbox\":[143,98,173,128],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data07_seq_011.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"51\":[{\"bbox\":[117,40,148,71],\"class_name\":\"drone\",\"confidence\":1.0}],\"56\":[{\"bbox\":[112,32,143,63],\"class_name\":\"drone\",\"confidence\":1.0}],\"61\":[{\"bbox\":[106,23,136,53],\"class_name\":\"drone\",\"confidence\":1.0}],\"66\":[{\"bbox\":[101,13,132,44],\"class_name\":\"drone\",\"confidence\":1.0}],\"71\":[{\"bbox\":[100,32,131,62],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data23_seq_022.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_new2_train_000103\":[{\"bbox\":[347,242,355,250],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000108\":[{\"bbox\":[347,241,356,250],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000113\":[{\"bbox\":[349,241,359,250],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000118\":[{\"bbox\":[352,243,360,251],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000123\":[{\"bbox\":[351,245,357,252],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data23_seq_004.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_new2_train_000005\":[{\"bbox\":[348,245,355,252],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000010\":[{\"bbox\":[349,245,355,252],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000015\":[{\"bbox\":[351,243,355,248],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000020\":[{\"bbox\":[349,244,355,251],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000025\":[{\"bbox\":[348,244,355,251],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data23_seq_018.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_new2_train_000079\":[{\"bbox\":[348,239,355,247],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000084\":[{\"bbox\":[348,240,356,248],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000089\":[{\"bbox\":[351,243,357,249],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000094\":[{\"bbox\":[351,243,359,249],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000099\":[{\"bbox\":[348,241,355,248],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data23_seq_034.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含4帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_new2_train_000155\":[{\"bbox\":[350,243,356,250],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000160\":[{\"bbox\":[350,242,356,249],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000165\":[{\"bbox\":[348,243,356,251],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000170\":[{\"bbox\":[350,243,356,250],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data23_seq_007.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_new2_train_000028\":[{\"bbox\":[348,244,355,251],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000033\":[{\"bbox\":[348,245,356,252],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000038\":[{\"bbox\":[348,246,355,252],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000043\":[{\"bbox\":[348,245,356,252],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000048\":[{\"bbox\":[350,244,356,250],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data23_seq_000.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_new2_train_000001\":[{\"bbox\":[348,243,355,250],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000006\":[{\"bbox\":[348,246,355,251],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000011\":[{\"bbox\":[348,244,355,251],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000016\":[{\"bbox\":[351,243,357,249],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000021\":[{\"bbox\":[351,244,357,250],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data23_seq_009.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_new2_train_000030\":[{\"bbox\":[349,245,355,252],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000035\":[{\"bbox\":[348,245,355,252],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000040\":[{\"bbox\":[348,245,355,252],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000045\":[{\"bbox\":[348,245,355,252],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_new2_train_000050\":[{\"bbox\":[348,244,356,251],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data25_seq_048.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000229\":[{\"bbox\":[451,172,460,178],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000234\":[{\"bbox\":[455,174,465,181],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000239\":[{\"bbox\":[457,175,466,181],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000244\":[{\"bbox\":[459,174,469,180],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000249\":[{\"bbox\":[463,175,471,181],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data25_seq_038.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000179\":[{\"bbox\":[410,144,422,151],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000184\":[{\"bbox\":[410,148,422,156],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000189\":[{\"bbox\":[412,150,423,158],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000194\":[{\"bbox\":[414,153,426,161],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000199\":[{\"bbox\":[416,156,428,164],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data25_seq_051.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000252\":[{\"bbox\":[463,175,471,181],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000257\":[{\"bbox\":[463,175,471,181],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000262\":[{\"bbox\":[463,175,471,181],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000267\":[{\"bbox\":[461,174,467,180],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000272\":[{\"bbox\":[458,175,465,180],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data25_seq_046.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000227\":[{\"bbox\":[447,171,457,178],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000232\":[{\"bbox\":[453,174,462,181],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000237\":[{\"bbox\":[457,174,466,180],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000242\":[{\"bbox\":[459,174,469,180],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000247\":[{\"bbox\":[461,174,471,180],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data25_seq_013.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000054\":[{\"bbox\":[499,151,513,160],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000059\":[{\"bbox\":[478,149,490,158],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000064\":[{\"bbox\":[448,147,464,154],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000069\":[{\"bbox\":[447,145,461,152],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000074\":[{\"bbox\":[449,144,461,153],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data25_seq_043.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000204\":[{\"bbox\":[422,159,434,167],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000209\":[{\"bbox\":[426,163,437,170],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000214\":[{\"bbox\":[432,166,444,173],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000219\":[{\"bbox\":[438,168,450,175],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000224\":[{\"bbox\":[444,170,455,176],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data25_seq_047.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000228\":[{\"bbox\":[449,172,459,178],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000233\":[{\"bbox\":[453,174,463,181],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000238\":[{\"bbox\":[457,175,466,181],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000243\":[{\"bbox\":[459,174,469,180],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000248\":[{\"bbox\":[463,175,471,181],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data25_seq_012.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000053\":[{\"bbox\":[497,151,511,160],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000058\":[{\"bbox\":[482,149,494,158],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000063\":[{\"bbox\":[451,147,464,154],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000068\":[{\"bbox\":[448,145,462,153],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000073\":[{\"bbox\":[449,144,461,153],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data25_seq_004.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000005\":[{\"bbox\":[484,167,499,177],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000010\":[{\"bbox\":[492,165,508,175],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000015\":[{\"bbox\":[504,164,516,173],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000020\":[{\"bbox\":[513,162,527,171],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000025\":[{\"bbox\":[524,160,540,166],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data25_seq_023.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000104\":[{\"bbox\":[458,137,470,146],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000109\":[{\"bbox\":[431,140,445,146],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000114\":[{\"bbox\":[404,140,418,146],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000119\":[{\"bbox\":[402,141,413,150],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000124\":[{\"bbox\":[401,142,415,151],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data25_seq_007.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000028\":[{\"bbox\":[528,159,540,165],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000033\":[{\"bbox\":[509,158,522,164],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000038\":[{\"bbox\":[488,156,504,163],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000043\":[{\"bbox\":[489,155,503,164],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000048\":[{\"bbox\":[495,152,509,162],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data25_seq_052.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000253\":[{\"bbox\":[463,175,471,181],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000258\":[{\"bbox\":[463,175,471,181],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000263\":[{\"bbox\":[462,174,470,180],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000268\":[{\"bbox\":[461,174,467,180],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_041_split_02_000273\":[{\"bbox\":[458,175,465,180],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data26_seq_009.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000030\":[{\"bbox\":[531,81,533,84],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000035\":[{\"bbox\":[532,81,534,84],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000040\":[{\"bbox\":[532,81,534,84],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000045\":[{\"bbox\":[531,81,533,84],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000050\":[{\"bbox\":[531,81,533,84],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data26_seq_005.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000026\":[{\"bbox\":[531,81,533,84],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000031\":[{\"bbox\":[531,81,533,84],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000036\":[{\"bbox\":[532,81,534,84],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000041\":[{\"bbox\":[532,81,534,84],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000046\":[{\"bbox\":[531,81,533,84],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data26_seq_010.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000051\":[{\"bbox\":[531,81,533,84],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000056\":[{\"bbox\":[531,81,533,84],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000061\":[{\"bbox\":[530,80,533,83],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000066\":[{\"bbox\":[528,80,530,83],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000071\":[{\"bbox\":[526,79,529,82],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data26_seq_022.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000103\":[{\"bbox\":[509,63,511,66],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000108\":[{\"bbox\":[505,60,507,62],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000113\":[{\"bbox\":[502,57,504,60],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000118\":[{\"bbox\":[498,52,501,55],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000123\":[{\"bbox\":[494,50,497,52],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data26_seq_028.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000129\":[{\"bbox\":[489,44,491,47],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000134\":[{\"bbox\":[485,42,487,44],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000139\":[{\"bbox\":[480,37,483,40],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000144\":[{\"bbox\":[477,34,479,36],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000149\":[{\"bbox\":[474,32,477,35],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data26_seq_001.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000002\":[{\"bbox\":[535,83,539,87],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000007\":[{\"bbox\":[533,83,535,85],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000012\":[{\"bbox\":[531,82,533,84],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000017\":[{\"bbox\":[531,82,533,84],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000022\":[{\"bbox\":[530,82,533,84],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data26_seq_046.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000227\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000232\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000237\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000242\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000247\":[]}"}]}, {"video": "videos/data26_seq_033.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000154\":[{\"bbox\":[470,28,473,31],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000159\":[{\"bbox\":[467,27,469,29],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000164\":[{\"bbox\":[463,24,465,27],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000169\":[{\"bbox\":[460,20,463,23],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000174\":[{\"bbox\":[456,18,459,20],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data26_seq_004.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000005\":[{\"bbox\":[534,83,537,87],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000010\":[{\"bbox\":[532,83,534,85],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000015\":[{\"bbox\":[531,82,533,84],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000020\":[{\"bbox\":[531,82,533,84],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000025\":[{\"bbox\":[531,81,533,84],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data26_seq_013.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000054\":[{\"bbox\":[531,81,533,84],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000059\":[{\"bbox\":[530,81,533,84],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000064\":[{\"bbox\":[529,80,531,83],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000069\":[{\"bbox\":[527,79,529,82],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000074\":[{\"bbox\":[525,76,527,79],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}, {"video": "videos/data26_seq_044.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000205\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000210\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000215\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000220\":[],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000225\":[]}"}]}, {"video": "videos/data26_seq_038.mp4", "conversations": [{"from": "human", "value": "<video>\n请分析这个红外视频序列中的目标检测结果。视频包含5帧图像，请识别其中的无人机目标并给出检测框坐标。"}, {"from": "gpt", "value": "{\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000179\":[{\"bbox\":[452,14,454,16],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000184\":[{\"bbox\":[449,11,451,13],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000189\":[{\"bbox\":[444,7,447,8],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000194\":[{\"bbox\":[440,3,443,5],\"class_name\":\"drone\",\"confidence\":1.0}],\"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000199\":[{\"bbox\":[437,0,439,3],\"class_name\":\"drone\",\"confidence\":1.0}]}"}]}]