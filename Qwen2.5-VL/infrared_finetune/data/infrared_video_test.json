[{"id": "test_data07_seq_009", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data07_seq_009.mp4"}, {"id": "test_data05_seq_005", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data05_seq_005.mp4"}, {"id": "test_data26_seq_010", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data26_seq_010.mp4"}, {"id": "test_data23_seq_023", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data23_seq_023.mp4"}, {"id": "test_data02_seq_025", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data02_seq_025.mp4"}, {"id": "test_data25_seq_017", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data25_seq_017.mp4"}, {"id": "test_data25_seq_030", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data25_seq_030.mp4"}, {"id": "test_data01_seq_054", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data01_seq_054.mp4"}, {"id": "test_data01_seq_043", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data01_seq_043.mp4"}, {"id": "test_data07_seq_014", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data07_seq_014.mp4"}, {"id": "test_data25_seq_039", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data25_seq_039.mp4"}, {"id": "test_data05_seq_012", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data05_seq_012.mp4"}, {"id": "test_data02_seq_015", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data02_seq_015.mp4"}, {"id": "test_data23_seq_002", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data23_seq_002.mp4"}, {"id": "test_data02_seq_012", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data02_seq_012.mp4"}, {"id": "test_data04_seq_005", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data04_seq_005.mp4"}, {"id": "test_data25_seq_015", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data25_seq_015.mp4"}, {"id": "test_data25_seq_037", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data25_seq_037.mp4"}, {"id": "test_data05_seq_003", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data05_seq_003.mp4"}, {"id": "test_data25_seq_042", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data25_seq_042.mp4"}, {"id": "test_data07_seq_007", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data07_seq_007.mp4"}, {"id": "test_data07_seq_012", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data07_seq_012.mp4"}, {"id": "test_data02_seq_026", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data02_seq_026.mp4"}, {"id": "test_data23_seq_020", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data23_seq_020.mp4"}, {"id": "test_data01_seq_047", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data01_seq_047.mp4"}, {"id": "test_data26_seq_005", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data26_seq_005.mp4"}, {"id": "test_data26_seq_017", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data26_seq_017.mp4"}, {"id": "test_data23_seq_027", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data23_seq_027.mp4"}, {"id": "test_data04_seq_003", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data04_seq_003.mp4"}, {"id": "test_data25_seq_000", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data25_seq_000.mp4"}, {"id": "test_data01_seq_015", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data01_seq_015.mp4"}, {"id": "test_data23_seq_030", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 207, 85, 74]}"}], "video": "data/videos/data23_seq_030.mp4"}, {"id": "test_data26_seq_038", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data26_seq_038.mp4"}, {"id": "test_data26_seq_053", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data26_seq_053.mp4"}, {"id": "test_data02_seq_016", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data02_seq_016.mp4"}, {"id": "test_data06_seq_013", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data06_seq_013.mp4"}, {"id": "test_data26_seq_022", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data26_seq_022.mp4"}, {"id": "test_data25_seq_057", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data25_seq_057.mp4"}, {"id": "test_data07_seq_005", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data07_seq_005.mp4"}, {"id": "test_data26_seq_011", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data26_seq_011.mp4"}, {"id": "test_data01_seq_041", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data01_seq_041.mp4"}, {"id": "test_data01_seq_016", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data01_seq_016.mp4"}, {"id": "test_data26_seq_037", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data26_seq_037.mp4"}, {"id": "test_data01_seq_028", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data01_seq_028.mp4"}, {"id": "test_data25_seq_058", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data25_seq_058.mp4"}, {"id": "test_data02_seq_029", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data02_seq_029.mp4"}, {"id": "test_data02_seq_007", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data02_seq_007.mp4"}, {"id": "test_data23_seq_017", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data23_seq_017.mp4"}, {"id": "test_data05_seq_001", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data05_seq_001.mp4"}, {"id": "test_data26_seq_036", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data26_seq_036.mp4"}, {"id": "test_data01_seq_038", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data01_seq_038.mp4"}, {"id": "test_data01_seq_056", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data01_seq_056.mp4"}, {"id": "test_data26_seq_023", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data26_seq_023.mp4"}, {"id": "test_data23_seq_019", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data23_seq_019.mp4"}, {"id": "test_data23_seq_006", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data23_seq_006.mp4"}, {"id": "test_data23_seq_000", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data23_seq_000.mp4"}, {"id": "test_data02_seq_023", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [119, 130, 23, 31]}"}], "video": "data/videos/data02_seq_023.mp4"}, {"id": "test_data26_seq_057", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data26_seq_057.mp4"}, {"id": "test_data23_seq_024", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data23_seq_024.mp4"}, {"id": "test_data25_seq_047", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 206, 85, 54]}"}], "video": "data/videos/data25_seq_047.mp4"}, {"id": "test_data23_seq_034", "conversations": [{"from": "user", "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"}, {"from": "assistant", "value": "{\"bbox\": [278, 207, 85, 74]}"}], "video": "data/videos/data23_seq_034.mp4"}]