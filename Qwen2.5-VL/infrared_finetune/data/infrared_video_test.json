[{"id": "test_data23_seq_015", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [237, 269, 297, 325],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [244, 266, 304, 322],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [248, 265, 308, 321],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [241, 274, 301, 330],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [251, 275, 311, 331],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.93\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_015.mp4"}, {"id": "test_data23_seq_033", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [365, 213, 391, 251],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [372, 215, 398, 253],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [362, 208, 388, 246],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [357, 206, 383, 244],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [363, 210, 389, 248],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.91\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_033.mp4"}, {"id": "test_data01_seq_040", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [128, 177, 166, 205],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [130, 182, 168, 210],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [137, 192, 175, 220],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [127, 190, 165, 218],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [128, 181, 166, 209],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.84\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_040.mp4"}, {"id": "test_data01_seq_022", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [170, 189, 212, 209],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [178, 191, 220, 211],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [180, 197, 222, 217],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [186, 187, 228, 207],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [191, 195, 233, 215],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.84\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_022.mp4"}, {"id": "test_data01_seq_059", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [141, 114, 165, 134],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [140, 112, 164, 132],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [133, 122, 157, 142],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [128, 121, 152, 141],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [131, 115, 155, 135],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.79\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_059.mp4"}, {"id": "test_data01_seq_026", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [169, 182, 225, 216],\n      \"class_name\": \"car\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [163, 179, 219, 213],\n      \"class_name\": \"car\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [169, 175, 225, 209],\n      \"class_name\": \"car\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [174, 185, 230, 219],\n      \"class_name\": \"car\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [173, 179, 229, 213],\n      \"class_name\": \"car\",\n      \"confidence\": 0.88\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_026.mp4"}, {"id": "test_data01_seq_003", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [43, 97, 67, 155],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [51, 102, 75, 160],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [52, 110, 76, 168],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [48, 115, 72, 173],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [47, 105, 71, 163],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.79\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_003.mp4"}, {"id": "test_data01_seq_020", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [190, 162, 216, 202],\n      \"class_name\": \"car\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [191, 170, 217, 210],\n      \"class_name\": \"car\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [193, 177, 219, 217],\n      \"class_name\": \"car\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [200, 174, 226, 214],\n      \"class_name\": \"car\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [208, 183, 234, 223],\n      \"class_name\": \"car\",\n      \"confidence\": 0.84\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_020.mp4"}, {"id": "test_data23_seq_028", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [357, 217, 383, 267],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [365, 214, 391, 264],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [374, 216, 400, 266],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [368, 210, 394, 260],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [378, 218, 404, 268],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.87\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_028.mp4"}, {"id": "test_data01_seq_008", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [154, 201, 204, 227],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [153, 197, 203, 223],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [155, 187, 205, 213],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [162, 195, 212, 221],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [162, 193, 212, 219],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.78\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_008.mp4"}, {"id": "test_data23_seq_031", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [342, 332, 362, 364],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [334, 331, 354, 363],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [328, 337, 348, 369],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [321, 331, 341, 363],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [330, 329, 350, 361],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.88\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_031.mp4"}, {"id": "test_data01_seq_013", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [120, 197, 170, 247],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [110, 201, 160, 251],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [114, 196, 164, 246],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [109, 201, 159, 251],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [109, 209, 159, 256],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.89\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_013.mp4"}, {"id": "test_data01_seq_052", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [14, 132, 66, 190],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [9, 122, 61, 180],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [16, 118, 68, 176],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [18, 109, 70, 167],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [18, 112, 70, 170],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.78\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_052.mp4"}, {"id": "test_data01_seq_044", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [172, 94, 228, 150],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [177, 86, 233, 142],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [181, 88, 237, 144],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [187, 97, 243, 153],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [186, 106, 242, 162],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.88\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_044.mp4"}, {"id": "test_data23_seq_013", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [398, 196, 452, 218],\n      \"class_name\": \"car\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [403, 199, 457, 221],\n      \"class_name\": \"car\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [412, 195, 466, 217],\n      \"class_name\": \"car\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [410, 202, 464, 224],\n      \"class_name\": \"car\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [402, 194, 456, 216],\n      \"class_name\": \"car\",\n      \"confidence\": 0.8\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_013.mp4"}, {"id": "test_data01_seq_033", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [194, 72, 248, 114],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [193, 66, 247, 108],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [183, 70, 237, 112],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [191, 66, 245, 108],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [184, 64, 238, 106],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.71\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_033.mp4"}, {"id": "test_data01_seq_046", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [38, 135, 90, 181],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [29, 128, 81, 174],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [27, 133, 79, 179],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [26, 130, 78, 176],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [28, 133, 80, 179],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.72\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_046.mp4"}, {"id": "test_data01_seq_024", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [25, 88, 85, 148],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [18, 94, 78, 154],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [27, 86, 87, 146],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [28, 95, 88, 155],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [32, 90, 92, 150],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.7\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_024.mp4"}, {"id": "test_data01_seq_031", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [28, 69, 60, 107],\n      \"class_name\": \"car\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [34, 77, 66, 115],\n      \"class_name\": \"car\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [25, 77, 57, 115],\n      \"class_name\": \"car\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [30, 77, 62, 115],\n      \"class_name\": \"car\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [27, 79, 59, 117],\n      \"class_name\": \"car\",\n      \"confidence\": 0.71\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_031.mp4"}, {"id": "test_data01_seq_037", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [186, 131, 226, 151],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [178, 125, 218, 145],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [172, 123, 212, 143],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [162, 115, 202, 135],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [158, 121, 198, 141],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.95\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_037.mp4"}, {"id": "test_data01_seq_036", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [35, 83, 61, 121],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [26, 80, 52, 118],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [36, 74, 62, 112],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [43, 77, 69, 115],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [38, 68, 64, 106],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.87\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_036.mp4"}, {"id": "test_data01_seq_004", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [138, 39, 178, 65],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [134, 40, 174, 66],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [138, 46, 178, 72],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [142, 41, 182, 67],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [148, 50, 188, 76],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.7\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_004.mp4"}, {"id": "test_data23_seq_012", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [344, 277, 388, 337],\n      \"class_name\": \"car\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [353, 272, 397, 332],\n      \"class_name\": \"car\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [361, 271, 405, 331],\n      \"class_name\": \"car\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [356, 262, 400, 322],\n      \"class_name\": \"car\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [360, 271, 404, 331],\n      \"class_name\": \"car\",\n      \"confidence\": 0.82\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_012.mp4"}, {"id": "test_data01_seq_019", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [121, 50, 161, 96],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [116, 47, 156, 93],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [106, 40, 146, 86],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [109, 33, 149, 79],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [99, 31, 139, 77],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.9\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_019.mp4"}, {"id": "test_data23_seq_018", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [314, 248, 364, 306],\n      \"class_name\": \"car\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [319, 244, 369, 302],\n      \"class_name\": \"car\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [310, 238, 360, 296],\n      \"class_name\": \"car\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [303, 240, 353, 298],\n      \"class_name\": \"car\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [294, 232, 344, 290],\n      \"class_name\": \"car\",\n      \"confidence\": 0.77\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_018.mp4"}, {"id": "test_data01_seq_057", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [85, 175, 141, 225],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [95, 184, 151, 234],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [101, 176, 157, 226],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [96, 178, 152, 228],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.7\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [96, 186, 152, 236],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.91\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_057.mp4"}, {"id": "test_data01_seq_055", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [64, 165, 102, 189],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [72, 160, 110, 184],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [76, 157, 114, 181],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [78, 167, 116, 191],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [78, 165, 116, 189],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.89\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_055.mp4"}, {"id": "test_data23_seq_014", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [293, 200, 345, 234],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [294, 203, 346, 237],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [290, 211, 342, 245],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [299, 206, 351, 240],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [289, 197, 341, 231],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.9\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_014.mp4"}, {"id": "test_data01_seq_053", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [37, 100, 61, 124],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [31, 107, 55, 131],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [25, 107, 49, 131],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [18, 101, 42, 125],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [20, 109, 44, 133],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.83\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_053.mp4"}, {"id": "test_data23_seq_004", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [337, 149, 379, 195],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [344, 141, 386, 187],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [340, 143, 382, 189],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [341, 133, 383, 179],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [331, 135, 373, 181],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.89\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_004.mp4"}, {"id": "test_data23_seq_025", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [184, 278, 244, 304],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [177, 284, 237, 310],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [174, 274, 234, 300],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [178, 269, 238, 295],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [172, 277, 232, 303],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.94\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_025.mp4"}, {"id": "test_data23_seq_021", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [230, 263, 282, 309],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [221, 256, 273, 302],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [219, 261, 271, 307],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [218, 258, 270, 304],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [220, 261, 272, 307],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.72\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_021.mp4"}, {"id": "test_data23_seq_032", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [219, 349, 253, 375],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [229, 357, 263, 383],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [219, 347, 253, 373],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [220, 347, 254, 373],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [218, 347, 252, 373],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.84\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_032.mp4"}, {"id": "test_data23_seq_007", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [318, 275, 356, 325],\n      \"class_name\": \"car\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [318, 283, 356, 333],\n      \"class_name\": \"car\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [314, 279, 352, 329],\n      \"class_name\": \"car\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [321, 283, 359, 333],\n      \"class_name\": \"car\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [328, 283, 366, 333],\n      \"class_name\": \"car\",\n      \"confidence\": 0.83\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_007.mp4"}, {"id": "test_data01_seq_009", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [170, 92, 226, 138],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [172, 86, 228, 132],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [169, 78, 225, 124],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [160, 87, 216, 133],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [161, 91, 217, 137],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.74\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_009.mp4"}, {"id": "test_data01_seq_058", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [109, 0, 137, 55],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [110, 0, 138, 48],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [109, 0, 137, 55],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [108, 5, 136, 63],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [103, 7, 131, 65],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.87\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_058.mp4"}, {"id": "test_data01_seq_011", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [23, 148, 55, 172],\n      \"class_name\": \"car\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [25, 150, 57, 174],\n      \"class_name\": \"car\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [29, 149, 61, 173],\n      \"class_name\": \"car\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [26, 150, 58, 174],\n      \"class_name\": \"car\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [27, 156, 59, 180],\n      \"class_name\": \"car\",\n      \"confidence\": 0.83\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_011.mp4"}, {"id": "test_data01_seq_000", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [167, 199, 199, 241],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [175, 207, 207, 249],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [178, 215, 210, 256],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [181, 208, 213, 250],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [188, 218, 220, 256],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.89\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_000.mp4"}, {"id": "test_data01_seq_049", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [28, 89, 62, 117],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [26, 95, 60, 123],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [32, 91, 66, 119],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [29, 89, 63, 117],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [32, 99, 66, 127],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.87\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_049.mp4"}, {"id": "test_data01_seq_002", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [77, 120, 119, 164],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [74, 126, 116, 170],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [74, 133, 116, 177],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [74, 142, 116, 186],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [81, 141, 123, 185],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.84\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_002.mp4"}, {"id": "test_data01_seq_027", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [130, 116, 176, 160],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [136, 109, 182, 153],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [129, 101, 175, 145],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [133, 92, 179, 136],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [127, 88, 173, 132],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.75\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_027.mp4"}, {"id": "test_data23_seq_001", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [279, 189, 313, 221],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [286, 198, 320, 230],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [293, 208, 327, 240],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [283, 206, 317, 238],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [288, 201, 322, 233],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.9\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_001.mp4"}, {"id": "test_data01_seq_042", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [158, 41, 180, 65],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [160, 50, 182, 74],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [153, 52, 175, 76],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [146, 54, 168, 78],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [155, 63, 177, 87],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.92\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_042.mp4"}, {"id": "test_data23_seq_026", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [273, 148, 297, 208],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [267, 156, 291, 216],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [260, 154, 284, 214],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [255, 157, 279, 217],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [255, 164, 279, 224],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.77\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_026.mp4"}, {"id": "test_data23_seq_016", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [361, 144, 399, 184],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [368, 143, 406, 183],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [378, 141, 416, 181],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [375, 141, 413, 181],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [370, 135, 408, 175],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.84\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_016.mp4"}, {"id": "test_data01_seq_023", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [134, 42, 158, 96],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [128, 40, 152, 94],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [126, 33, 150, 87],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [124, 32, 148, 86],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [132, 25, 156, 79],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.82\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_023.mp4"}, {"id": "test_data01_seq_006", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [173, 85, 199, 123],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [180, 87, 206, 125],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [170, 80, 196, 118],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [165, 78, 191, 116],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [171, 82, 197, 120],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.91\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_006.mp4"}, {"id": "test_data01_seq_005", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [79, 184, 123, 216],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [69, 179, 113, 211],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [69, 171, 113, 203],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [69, 172, 113, 204],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [75, 162, 119, 194],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.91\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_005.mp4"}, {"id": "test_data01_seq_039", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [13, 170, 63, 214],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [9, 170, 59, 214],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [9, 175, 59, 219],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [13, 184, 63, 228],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [18, 178, 68, 222],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.84\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_039.mp4"}, {"id": "test_data01_seq_007", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [56, 13, 112, 65],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [48, 18, 104, 70],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [53, 22, 109, 74],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [51, 31, 107, 83],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [58, 30, 114, 82],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.75\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_007.mp4"}, {"id": "test_data01_seq_051", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [152, 96, 174, 128],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [150, 87, 172, 119],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [142, 87, 164, 119],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [137, 91, 159, 123],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [136, 81, 158, 113],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.94\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_051.mp4"}, {"id": "test_data01_seq_021", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [56, 156, 100, 216],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [51, 156, 95, 216],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [61, 159, 105, 219],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [70, 162, 114, 222],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [60, 167, 104, 227],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.81\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_021.mp4"}, {"id": "test_data01_seq_018", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [145, 155, 197, 183],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [136, 164, 188, 192],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [127, 166, 179, 194],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [130, 167, 182, 195],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [122, 177, 174, 205],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.76\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_018.mp4"}, {"id": "test_data01_seq_017", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [161, 113, 189, 155],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [155, 109, 183, 151],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [150, 116, 178, 158],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [142, 111, 170, 153],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [145, 110, 173, 152],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.83\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_017.mp4"}, {"id": "test_data23_seq_011", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [312, 284, 370, 340],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [309, 283, 367, 339],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [308, 275, 366, 331],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [317, 285, 375, 341],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [313, 295, 371, 351],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.92\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_011.mp4"}, {"id": "test_data01_seq_030", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [170, 191, 220, 215],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [165, 188, 215, 212],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [175, 183, 225, 207],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [181, 173, 231, 197],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [181, 172, 231, 196],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.95\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_030.mp4"}, {"id": "test_data01_seq_050", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [124, 40, 148, 90],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [134, 38, 158, 88],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.95\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [139, 31, 163, 81],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [139, 31, 163, 81],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [130, 38, 154, 88],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.72\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_050.mp4"}, {"id": "test_data23_seq_009", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [354, 173, 394, 207],\n      \"class_name\": \"car\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [360, 179, 400, 213],\n      \"class_name\": \"car\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [359, 187, 399, 221],\n      \"class_name\": \"car\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [360, 187, 400, 221],\n      \"class_name\": \"car\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [352, 191, 392, 225],\n      \"class_name\": \"car\",\n      \"confidence\": 0.8\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_009.mp4"}, {"id": "test_data23_seq_008", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [215, 178, 255, 214],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [209, 183, 249, 219],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [209, 184, 249, 220],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [204, 184, 244, 220],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [207, 175, 247, 211],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.7\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_008.mp4"}, {"id": "test_data01_seq_012", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [171, 84, 223, 128],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [179, 93, 231, 137],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [169, 96, 221, 140],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [171, 101, 223, 145],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [180, 106, 232, 150],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.87\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_012.mp4"}, {"id": "test_data01_seq_025", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [113, 81, 151, 123],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [122, 72, 160, 114],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [119, 70, 157, 112],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [117, 64, 155, 106],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [108, 57, 146, 99],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.94\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_025.mp4"}, {"id": "test_data01_seq_014", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [11, 186, 47, 230],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [4, 178, 40, 222],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [6, 169, 42, 213],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [10, 162, 46, 206],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [20, 167, 56, 211],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.76\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_014.mp4"}, {"id": "test_data23_seq_010", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [244, 146, 290, 206],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [238, 144, 284, 204],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [232, 149, 278, 209],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [235, 142, 281, 202],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [229, 141, 275, 201],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.86\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_010.mp4"}, {"id": "test_data01_seq_035", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [164, 129, 224, 153],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [168, 123, 228, 147],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [164, 119, 224, 143],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [155, 119, 215, 143],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [155, 111, 215, 135],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.93\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_035.mp4"}, {"id": "test_data01_seq_032", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [71, 104, 131, 138],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [71, 101, 131, 135],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [63, 95, 123, 129],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [56, 96, 116, 130],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [52, 88, 112, 122],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.94\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_032.mp4"}, {"id": "test_data23_seq_003", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [196, 260, 252, 300],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [195, 255, 251, 295],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [198, 245, 254, 285],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [206, 248, 262, 288],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [198, 253, 254, 293],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.94\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_003.mp4"}, {"id": "test_data01_seq_029", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [20, 135, 56, 157],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [25, 136, 61, 158],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [15, 134, 51, 156],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [11, 137, 47, 159],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [18, 130, 54, 152],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.75\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_029.mp4"}, {"id": "test_data01_seq_045", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [64, 23, 116, 69],\n      \"class_name\": \"car\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [59, 20, 111, 66],\n      \"class_name\": \"car\",\n      \"confidence\": 0.7\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [61, 28, 113, 74],\n      \"class_name\": \"car\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [60, 18, 112, 64],\n      \"class_name\": \"car\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [50, 26, 102, 72],\n      \"class_name\": \"car\",\n      \"confidence\": 0.81\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_045.mp4"}, {"id": "test_data01_seq_048", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [46, 88, 84, 132],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [49, 95, 87, 139],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [54, 88, 92, 132],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [44, 96, 82, 140],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [44, 102, 82, 146],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.79\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_048.mp4"}, {"id": "test_data01_seq_010", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [28, 57, 74, 89],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [22, 55, 68, 87],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [12, 50, 58, 82],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.7\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [7, 43, 53, 75],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [0, 44, 43, 76],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.81\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_010.mp4"}, {"id": "test_data23_seq_029", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [252, 273, 304, 313],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [260, 273, 312, 313],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [265, 283, 317, 323],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [258, 285, 310, 325],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [251, 293, 303, 333],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.77\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_029.mp4"}, {"id": "test_data23_seq_005", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [217, 287, 261, 327],\n      \"class_name\": \"car\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [224, 280, 268, 320],\n      \"class_name\": \"car\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [220, 282, 264, 322],\n      \"class_name\": \"car\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [226, 290, 270, 330],\n      \"class_name\": \"car\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [236, 299, 280, 339],\n      \"class_name\": \"car\",\n      \"confidence\": 0.92\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_005.mp4"}, {"id": "test_data23_seq_022", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [255, 186, 291, 220],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [255, 189, 291, 223],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [262, 185, 298, 219],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [261, 177, 297, 211],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [262, 169, 298, 203],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.9\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_022.mp4"}, {"id": "test_data01_seq_001", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [82, 77, 114, 109],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [89, 68, 121, 100],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [84, 64, 116, 96],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [74, 62, 106, 94],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [69, 61, 101, 93],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.72\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_001.mp4"}, {"id": "test_data01_seq_034", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [180, 207, 222, 241],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.75\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [184, 210, 226, 244],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [180, 213, 222, 247],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [188, 212, 230, 246],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [179, 206, 221, 240],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.84\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_034.mp4"}, {"id": "test_data23_seq_023", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [223, 272, 259, 324],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.8\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [213, 275, 249, 327],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [210, 273, 246, 325],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [205, 270, 241, 322],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [197, 265, 233, 317],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.81\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_023.mp4"}, {"id": "test_data01_seq_054", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [140, 75, 162, 123],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [133, 73, 155, 121],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [141, 68, 163, 116],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [133, 69, 155, 117],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.89\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [126, 59, 148, 107],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.78\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_054.mp4"}, {"id": "test_data01_seq_043", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [157, 94, 181, 138],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [162, 91, 186, 135],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [159, 89, 183, 133],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [168, 80, 192, 124],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [158, 82, 182, 126],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.76\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_043.mp4"}, {"id": "test_data23_seq_002", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [340, 240, 386, 268],\n      \"class_name\": \"car\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [336, 230, 382, 258],\n      \"class_name\": \"car\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [339, 221, 385, 249],\n      \"class_name\": \"car\",\n      \"confidence\": 0.83\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [335, 211, 381, 239],\n      \"class_name\": \"car\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [327, 204, 373, 232],\n      \"class_name\": \"car\",\n      \"confidence\": 0.74\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_002.mp4"}, {"id": "test_data23_seq_020", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [352, 188, 372, 220],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [360, 193, 380, 225],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [353, 201, 373, 233],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [351, 204, 371, 236],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [359, 212, 379, 244],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.75\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_020.mp4"}, {"id": "test_data01_seq_047", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [126, 142, 156, 166],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [123, 132, 153, 156],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [122, 128, 152, 152],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [121, 130, 151, 154],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.82\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [114, 131, 144, 155],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.89\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_047.mp4"}, {"id": "test_data23_seq_027", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [320, 308, 356, 346],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [327, 308, 363, 346],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [329, 303, 365, 341],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [326, 308, 362, 346],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [336, 316, 372, 354],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.86\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_027.mp4"}, {"id": "test_data01_seq_015", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [172, 125, 210, 167],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [174, 135, 212, 177],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [181, 133, 219, 175],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [182, 137, 220, 179],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [181, 133, 219, 175],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.94\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_015.mp4"}, {"id": "test_data23_seq_030", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [216, 279, 244, 303],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [226, 274, 254, 298],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [224, 266, 252, 290],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [219, 269, 247, 293],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [219, 259, 247, 283],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.83\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_030.mp4"}, {"id": "test_data01_seq_041", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [120, 125, 178, 157],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [126, 117, 184, 149],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [125, 113, 183, 145],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [133, 123, 191, 155],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [127, 114, 185, 146],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.88\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_041.mp4"}, {"id": "test_data01_seq_016", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [110, 145, 142, 177],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [103, 150, 135, 182],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [109, 148, 141, 180],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.94\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [114, 156, 146, 188],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [109, 153, 141, 185],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.72\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_016.mp4"}, {"id": "test_data01_seq_028", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [23, 141, 71, 161],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [20, 138, 68, 158],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [13, 141, 61, 161],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.76\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [18, 135, 66, 155],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [23, 134, 71, 154],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.72\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_028.mp4"}, {"id": "test_data23_seq_017", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [251, 158, 287, 196],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [252, 157, 288, 195],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [250, 164, 286, 202],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [240, 167, 276, 205],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [244, 168, 280, 206],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.9\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_017.mp4"}, {"id": "test_data01_seq_038", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [123, 123, 183, 179],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [117, 125, 177, 181],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.71\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [118, 127, 178, 183],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [126, 136, 186, 192],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [126, 135, 186, 191],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.72\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_038.mp4"}, {"id": "test_data01_seq_056", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [36, 168, 68, 196],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [29, 176, 61, 204],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.74\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [32, 168, 64, 196],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [36, 174, 68, 202],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [30, 170, 62, 198],\n      \"class_name\": \"bus\",\n      \"confidence\": 0.76\n    }\n  ]\n}"}], "video": "data/videos/data01_seq_056.mp4"}, {"id": "test_data23_seq_019", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [282, 230, 310, 270],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.85\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [288, 227, 316, 267],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.79\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [293, 219, 321, 259],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [299, 209, 327, 249],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.84\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [296, 205, 324, 245],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.71\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_019.mp4"}, {"id": "test_data23_seq_006", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [271, 249, 297, 305],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [268, 251, 294, 307],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.9\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [274, 244, 300, 300],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.7\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [278, 252, 304, 308],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.73\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [285, 262, 311, 318],\n      \"class_name\": \"pedestrian\",\n      \"confidence\": 0.82\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_006.mp4"}, {"id": "test_data23_seq_000", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [352, 188, 372, 220],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.88\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [360, 193, 380, 225],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.92\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [353, 201, 373, 233],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [351, 204, 371, 236],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [359, 212, 379, 244],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.75\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_000.mp4"}, {"id": "test_data23_seq_024", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [336, 209, 374, 261],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.77\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [338, 219, 376, 271],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.93\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [346, 220, 384, 272],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.72\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [350, 219, 388, 271],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.91\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [341, 213, 379, 265],\n      \"class_name\": \"cyclist\",\n      \"confidence\": 0.78\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_024.mp4"}, {"id": "test_data23_seq_034", "conversations": [{"from": "user", "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"}, {"from": "assistant", "value": "{\n  \"frame_001\": [\n    {\n      \"bbox\": [343, 317, 397, 373],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.81\n    }\n  ],\n  \"frame_002\": [\n    {\n      \"bbox\": [339, 327, 393, 383],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.78\n    }\n  ],\n  \"frame_003\": [\n    {\n      \"bbox\": [333, 327, 387, 383],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.87\n    }\n  ],\n  \"frame_004\": [\n    {\n      \"bbox\": [333, 327, 387, 383],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.86\n    }\n  ],\n  \"frame_005\": [\n    {\n      \"bbox\": [329, 323, 383, 379],\n      \"class_name\": \"ship\",\n      \"confidence\": 0.92\n    }\n  ]\n}"}], "video": "data/videos/data23_seq_034.mp4"}]