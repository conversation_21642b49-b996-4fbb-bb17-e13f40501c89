#!/usr/bin/env python3
"""
红外图像微小目标检测数据预处理模块
用于将YOLO格式标注数据转换为Qwen2.5-VL微调所需的格式
"""

import os
import json
import cv2
import numpy as np
from pathlib import Path
from typing import List, Dict, Any, Tuple
import logging
from PIL import Image
import random

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class InfraredDataPreprocessor:
    """红外图像微小目标检测数据预处理器"""
    
    def __init__(self, data_root: str, output_dir: str):
        """
        初始化预处理器
        
        Args:
            data_root: 数据根目录路径
            output_dir: 输出目录路径
        """
        self.data_root = Path(data_root)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 目标序列列表（仅使用指定的8个序列）
        self.target_sequences = ['data01', 'data02', 'data04', 'data05', 'data06', 'data07', 'data23', 'data25', 'data26']
        
        # 目标类别映射
        self.class_mapping = {
            "0": "drone",
            "1": "car", 
            "2": "ship",
            "3": "bus",
            "4": "pedestrian",
            "5": "cyclist"
        }
        
        # 视频参数
        self.frame_interval = 5  # 每隔5帧取一帧
        self.sequence_length = 5  # 每个视频序列包含5帧
        self.fps = 5.0  # 视频帧率
        self.frame_percentage = 0.2  # 只使用前20%的帧

        # 数据集划分参数
        self.test_ratio = 0.2  # 测试集比例（20%）
        self.random_seed = 42  # 随机种子，确保可重复性
        
        # 创建输出子目录
        self.videos_dir = self.output_dir / "videos"
        self.videos_dir.mkdir(exist_ok=True)
        
        logger.info(f"数据预处理器初始化完成")
        logger.info(f"数据根目录: {self.data_root}")
        logger.info(f"输出目录: {self.output_dir}")
        logger.info(f"目标序列: {self.target_sequences}")
    
    def load_yolo_annotations(self, label_file: Path, image_width: int, image_height: int) -> List[Dict]:
        """
        加载YOLO格式标注文件并转换为绝对坐标
        
        Args:
            label_file: 标注文件路径
            image_width: 图像宽度
            image_height: 图像高度
            
        Returns:
            标注列表，每个标注包含class_name, bbox等信息
        """
        annotations = []
        
        if not label_file.exists():
            return annotations
            
        try:
            with open(label_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if not line:
                        continue
                        
                    parts = line.split()
                    if len(parts) < 5:
                        continue
                        
                    class_id = parts[0]
                    x_center_norm = float(parts[1])
                    y_center_norm = float(parts[2])
                    width_norm = float(parts[3])
                    height_norm = float(parts[4])
                    
                    # 转换为绝对坐标
                    x_center = x_center_norm * image_width
                    y_center = y_center_norm * image_height
                    width = width_norm * image_width
                    height = height_norm * image_height
                    
                    # 计算边界框坐标 (x1, y1, x2, y2)
                    x1 = int(x_center - width / 2)
                    y1 = int(y_center - height / 2)
                    x2 = int(x_center + width / 2)
                    y2 = int(y_center + height / 2)
                    
                    # 确保坐标在图像范围内
                    x1 = max(0, min(x1, image_width - 1))
                    y1 = max(0, min(y1, image_height - 1))
                    x2 = max(0, min(x2, image_width - 1))
                    y2 = max(0, min(y2, image_height - 1))
                    
                    annotation = {
                        "bbox": [x1, y1, x2, y2],
                        "class_name": self.class_mapping.get(class_id, "unknown"),
                        "confidence": 1.0  # 真实标注的置信度设为1.0
                    }
                    annotations.append(annotation)
                    
        except Exception as e:
            logger.error(f"读取标注文件失败 {label_file}: {e}")
            
        return annotations
    
    def get_frame_sequence(self, sequence_name: str) -> List[List[str]]:
        """
        获取帧序列
        
        Args:
            sequence_name: 序列名称
            
        Returns:
            帧序列列表，每个序列包含多个帧的路径
        """
        sequence_dir = self.data_root / "images" / sequence_name
        if not sequence_dir.exists():
            logger.error(f"序列目录不存在: {sequence_dir}")
            return []
            
        # 获取所有帧文件并按顺序排序
        frame_files = []
        
        if sequence_name in ['data23', 'data25', 'data26']:
            # 特殊命名格式的序列
            for f in sequence_dir.glob("*.jpg"):
                frame_files.append(f)
            frame_files = sorted(frame_files, key=lambda x: x.name)
        else:
            # 标准数字命名的序列
            for f in sequence_dir.glob("*.bmp"):
                try:
                    int(f.stem)
                    frame_files.append(f)
                except ValueError:
                    continue
            frame_files = sorted(frame_files, key=lambda x: int(x.stem))
        
        if len(frame_files) == 0:
            logger.error(f"序列目录中没有找到帧文件: {sequence_dir}")
            return []
        
        # 计算要处理的帧数量（前20%）
        total_frames_to_process = int(len(frame_files) * self.frame_percentage)
        frame_files_to_process = frame_files[:total_frames_to_process]
        
        logger.info(f"序列 {sequence_name}: 总帧数 {len(frame_files)}, 处理帧数 {total_frames_to_process}")
        
        # 生成帧序列（分组滑动窗口方式）
        sequences = []
        group_size = self.frame_interval * self.sequence_length  # 每组的帧数
        
        group_start = 0
        while group_start < len(frame_files_to_process):
            group_end = min(group_start + group_size, len(frame_files_to_process))
            
            # 在当前组内生成序列（滑动窗口）
            for offset in range(self.frame_interval):
                start_frame = group_start + offset
                
                frame_indices = []
                for i in range(self.sequence_length):
                    frame_idx = start_frame + i * self.frame_interval
                    if frame_idx < group_end and frame_idx < len(frame_files_to_process):
                        frame_indices.append(frame_idx)
                    else:
                        break
                
                # 如果序列长度不足，但至少有2帧，也可以生成序列
                if len(frame_indices) >= 2:
                    frame_paths = [str(frame_files_to_process[idx]) for idx in frame_indices]
                    sequences.append(frame_paths)
                else:
                    break
            
            group_start += group_size
        
        logger.info(f"序列 {sequence_name} 生成了 {len(sequences)} 个帧序列")
        return sequences
    
    def create_video_from_frames(self, frame_paths: List[str], sequence_name: str, sequence_id: int) -> str:
        """
        从帧序列创建视频文件
        
        Args:
            frame_paths: 帧文件路径列表
            sequence_name: 序列名称
            sequence_id: 序列ID
            
        Returns:
            生成的视频文件路径
        """
        video_filename = f"{sequence_name}_seq_{sequence_id:03d}.mp4"
        video_path = self.videos_dir / video_filename
        
        if len(frame_paths) == 0:
            logger.error("帧路径列表为空")
            return str(video_path)
        
        # 读取第一帧获取尺寸
        first_frame = cv2.imread(frame_paths[0])
        if first_frame is None:
            logger.error(f"无法读取第一帧: {frame_paths[0]}")
            return str(video_path)
        
        height, width, _ = first_frame.shape
        
        # 创建视频写入器
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(str(video_path), fourcc, self.fps, (width, height))
        
        try:
            for frame_path in frame_paths:
                frame = cv2.imread(frame_path)
                if frame is not None:
                    out.write(frame)
                else:
                    logger.warning(f"无法读取帧: {frame_path}")
        finally:
            out.release()
        
        logger.info(f"视频创建完成: {video_path}")
        return str(video_path)
    
    def create_detection_prompt(self, frame_paths: List[str], annotations_list: List[List[Dict]]) -> str:
        """
        创建检测提示词
        
        Args:
            frame_paths: 帧路径列表
            annotations_list: 每帧的标注列表
            
        Returns:
            检测提示词
        """
        frame_ids = [Path(path).stem for path in frame_paths]
        
        # 构建示例输出格式
        example_output = "{\n"
        for i, (frame_id, annotations) in enumerate(zip(frame_ids, annotations_list)):
            example_output += f'  "{frame_id}": [\n'
            
            if annotations:
                for j, ann in enumerate(annotations):
                    example_output += '    {\n'
                    example_output += f'      "bbox": {ann["bbox"]},\n'
                    example_output += f'      "class_name": "{ann["class_name"]}",\n'
                    example_output += f'      "confidence": {ann["confidence"]}\n'
                    example_output += '    }'
                    if j < len(annotations) - 1:
                        example_output += ','
                    example_output += '\n'
            
            if i < len(frame_ids) - 1:
                example_output += '  ],\n'
            else:
                example_output += '  ]\n'
        example_output += "}"
        
        prompt = f"""这是一个红外视频序列，包含连续的{len(frame_paths)}帧图像。请分析这个视频中的微小移动目标。

视频分析任务：
1. 这是一个视频序列，不是静态图片，请利用帧间时序信息
2. 检测每帧中的微小目标，特别是移动的小白点或小亮点
3. 通过观察目标在不同帧中的位置变化来确认真实目标
4. 对检测到的目标进行分类和定位

检测重点：
- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）
- 利用视频时序性识别运动模式
- 排除静态背景噪声
- 精确的空间定位

请以JSON格式输出每一帧的检测结果：
```json
{example_output}
```

目标类别：
- drone: 无人机
- car: 汽车
- ship: 船只
- bus: 公交车
- pedestrian: 行人
- cyclist: 骑行者

注意：这是视频序列分析，请仔细观察每帧（{', '.join(frame_ids)}）中的微小目标，利用时序信息提高检测准确性。"""

        return prompt

    def process_sequence(self, sequence_name: str) -> List[Dict]:
        """
        处理单个序列，生成训练数据

        Args:
            sequence_name: 序列名称

        Returns:
            训练数据列表
        """
        logger.info(f"开始处理序列: {sequence_name}")

        # 获取帧序列
        frame_sequences = self.get_frame_sequence(sequence_name)
        if not frame_sequences:
            logger.warning(f"序列 {sequence_name} 没有生成任何帧序列")
            return []

        training_data = []

        for seq_id, frame_paths in enumerate(frame_sequences):
            try:
                # 获取图像尺寸（从第一帧）
                first_image = Image.open(frame_paths[0])
                image_width, image_height = first_image.size

                # 加载每帧的标注
                annotations_list = []
                for frame_path in frame_paths:
                    frame_name = Path(frame_path).stem
                    label_file = self.data_root / "labels" / sequence_name / f"{frame_name}.txt"
                    annotations = self.load_yolo_annotations(label_file, image_width, image_height)
                    annotations_list.append(annotations)

                # 创建视频文件
                video_path = self.create_video_from_frames(frame_paths, sequence_name, seq_id)

                # 生成检测提示词
                prompt = self.create_detection_prompt(frame_paths, annotations_list)

                # 构建期望的输出格式
                expected_output = {}
                for frame_path, annotations in zip(frame_paths, annotations_list):
                    frame_id = Path(frame_path).stem
                    expected_output[frame_id] = annotations

                # 构建训练数据项
                training_item = {
                    "messages": [
                        {
                            "role": "user",
                            "content": f"<video>{prompt}"
                        },
                        {
                            "role": "assistant",
                            "content": json.dumps(expected_output, ensure_ascii=False, separators=(',', ':'))
                        }
                    ],
                    "videos": [os.path.relpath(video_path, self.output_dir)]
                }

                training_data.append(training_item)
                logger.info(f"序列 {sequence_name} 第 {seq_id+1} 个视频序列处理完成")

            except Exception as e:
                logger.error(f"处理序列 {sequence_name} 第 {seq_id+1} 个视频序列时出错: {e}")
                continue

        logger.info(f"序列 {sequence_name} 处理完成，生成 {len(training_data)} 个训练样本")
        return training_data

    def split_train_test(self, sequence_data: List[Dict], sequence_name: str) -> Tuple[List[Dict], List[Dict]]:
        """
        将序列数据划分为训练集和测试集

        Args:
            sequence_data: 序列数据列表
            sequence_name: 序列名称

        Returns:
            (训练集, 测试集)
        """
        # 设置随机种子确保可重复性
        random.seed(self.random_seed + hash(sequence_name))

        # 随机打乱数据
        shuffled_data = sequence_data.copy()
        random.shuffle(shuffled_data)

        # 计算测试集大小
        test_size = max(1, int(len(shuffled_data) * self.test_ratio))

        # 划分数据
        test_data = shuffled_data[:test_size]
        train_data = shuffled_data[test_size:]

        logger.info(f"序列 {sequence_name}: 总样本 {len(sequence_data)}, 训练集 {len(train_data)}, 测试集 {len(test_data)}")

        return train_data, test_data

    def generate_training_data(self) -> Tuple[str, str]:
        """
        生成训练数据和测试数据

        Returns:
            (训练数据文件路径, 测试数据文件路径)
        """
        logger.info("开始生成训练数据和测试数据...")

        all_training_data = []
        all_test_data = []

        for sequence_name in self.target_sequences:
            sequence_data = self.process_sequence(sequence_name)

            # 划分训练集和测试集
            train_data, test_data = self.split_train_test(sequence_data, sequence_name)

            all_training_data.extend(train_data)
            all_test_data.extend(test_data)

        # 保存训练数据
        training_data_file = self.output_dir / "infrared_detection_train.json"
        with open(training_data_file, 'w', encoding='utf-8') as f:
            json.dump(all_training_data, f, ensure_ascii=False, indent=2)

        # 保存测试数据
        test_data_file = self.output_dir / "infrared_detection_test.json"
        with open(test_data_file, 'w', encoding='utf-8') as f:
            json.dump(all_test_data, f, ensure_ascii=False, indent=2)

        logger.info(f"数据生成完成！")
        logger.info(f"训练集样本数: {len(all_training_data)}")
        logger.info(f"测试集样本数: {len(all_test_data)}")
        logger.info(f"训练数据文件: {training_data_file}")
        logger.info(f"测试数据文件: {test_data_file}")

        # 生成统计信息
        self.generate_statistics(all_training_data, all_test_data)

        return str(training_data_file), str(test_data_file)

    def generate_statistics(self, training_data: List[Dict], test_data: List[Dict] = None):
        """
        生成数据统计信息

        Args:
            training_data: 训练数据列表
            test_data: 测试数据列表（可选）
        """
        def analyze_data(data: List[Dict], data_type: str):
            """分析数据集"""
            stats = {
                "total_samples": len(data),
                "sequences": {},
                "class_distribution": {},
                "total_annotations": 0
            }

            for item in data:
                video_path = item["videos"][0]
                sequence_name = video_path.split('_')[0]

                if sequence_name not in stats["sequences"]:
                    stats["sequences"][sequence_name] = 0
                stats["sequences"][sequence_name] += 1

                # 解析标注信息
                try:
                    output_data = json.loads(item["messages"][1]["content"])
                    for frame_id, annotations in output_data.items():
                        for ann in annotations:
                            class_name = ann["class_name"]
                            if class_name not in stats["class_distribution"]:
                                stats["class_distribution"][class_name] = 0
                            stats["class_distribution"][class_name] += 1
                            stats["total_annotations"] += 1
                except:
                    continue

            return stats

        # 分析训练集
        train_stats = analyze_data(training_data, "train")

        # 分析测试集（如果提供）
        test_stats = None
        if test_data:
            test_stats = analyze_data(test_data, "test")

        # 合并统计信息
        combined_stats = {
            "train": train_stats,
            "test": test_stats if test_stats else {},
            "summary": {
                "total_train_samples": train_stats["total_samples"],
                "total_test_samples": test_stats["total_samples"] if test_stats else 0,
                "total_samples": train_stats["total_samples"] + (test_stats["total_samples"] if test_stats else 0),
                "train_annotations": train_stats["total_annotations"],
                "test_annotations": test_stats["total_annotations"] if test_stats else 0,
                "total_annotations": train_stats["total_annotations"] + (test_stats["total_annotations"] if test_stats else 0)
            }
        }

        # 保存统计信息
        stats_file = self.output_dir / "data_statistics.json"
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(combined_stats, f, ensure_ascii=False, indent=2)

        logger.info(f"数据统计信息已保存到: {stats_file}")
        logger.info(f"统计摘要:")
        logger.info(f"  训练集样本数: {combined_stats['summary']['total_train_samples']}")
        logger.info(f"  测试集样本数: {combined_stats['summary']['total_test_samples']}")
        logger.info(f"  总样本数: {combined_stats['summary']['total_samples']}")
        logger.info(f"  训练集标注数: {combined_stats['summary']['train_annotations']}")
        logger.info(f"  测试集标注数: {combined_stats['summary']['test_annotations']}")
        logger.info(f"  总标注数: {combined_stats['summary']['total_annotations']}")
        logger.info(f"  训练集序列分布: {train_stats['sequences']}")
        logger.info(f"  训练集类别分布: {train_stats['class_distribution']}")
        if test_stats:
            logger.info(f"  测试集序列分布: {test_stats['sequences']}")
            logger.info(f"  测试集类别分布: {test_stats['class_distribution']}")

def main():
    """主函数"""
    # 配置路径
    data_root = "/home/<USER>/Qwen/Qwen2.5-VL/tiaozhanbei_datasets"
    output_dir = "/home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/data"

    try:
        # 创建数据预处理器
        preprocessor = InfraredDataPreprocessor(data_root, output_dir)

        # 生成训练数据和测试数据
        training_data_file, test_data_file = preprocessor.generate_training_data()

        logger.info(f"数据预处理完成！")
        logger.info(f"训练数据文件: {training_data_file}")
        logger.info(f"测试数据文件: {test_data_file}")

    except Exception as e:
        logger.error(f"数据预处理过程出错: {e}")
        raise

if __name__ == "__main__":
    main()
