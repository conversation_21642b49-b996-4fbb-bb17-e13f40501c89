"""
红外视频数据处理器 - 正确的视频合成方式
按照指定的帧间隔采样方式生成视频序列
"""

import os
import json
import logging
import numpy as np
from pathlib import Path
from PIL import Image
import cv2

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class InfraredVideoProcessor:
    """红外视频数据处理器"""
    
    def __init__(self, base_images_dir, base_labels_dir, output_dir):
        self.base_images_dir = Path(base_images_dir)
        self.base_labels_dir = Path(base_labels_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 数据集配置
        self.dataset_config = {
            'data01': {'count': 60, 'split': 'test', 'size': (256, 256)},
            'data02': {'count': 30, 'split': 'train', 'size': (256, 256)},
            'data04': {'count': 15, 'split': 'train', 'size': (256, 256)},
            'data05': {'count': 15, 'split': 'train', 'size': (256, 256)},
            'data06': {'count': 15, 'split': 'train', 'size': (256, 256)},
            'data07': {'count': 15, 'split': 'train', 'size': (256, 256)},
            'data23': {'count': 30, 'split': 'test', 'size': (640, 512)},
            'data25': {'count': 60, 'split': 'train', 'size': (640, 512)},
            'data26': {'count': 60, 'split': 'train', 'size': (640, 512)},
        }
    
    def generate_video_sequences(self, sequence_name, frames_per_video=5):
        """
        按照正确的帧间隔采样方式生成视频序列
        
        第1组 (帧0-24):
        • 序列0: 帧0, 5, 10, 15, 20
        • 序列1: 帧1, 6, 11, 16, 21  
        • 序列2: 帧2, 7, 12, 17, 22
        • 序列3: 帧3, 8, 13, 18, 23
        • 序列4: 帧4, 9, 14, 19, 24
        """
        if sequence_name not in self.dataset_config:
            logger.error(f"未知序列: {sequence_name}")
            return []
        
        config = self.dataset_config[sequence_name]
        images_dir = self.base_images_dir / sequence_name
        labels_dir = self.base_labels_dir / sequence_name
        
        if not images_dir.exists():
            logger.error(f"图像目录不存在: {images_dir}")
            return []
        
        # 获取所有图像文件
        image_files = sorted([f for f in images_dir.glob('*.jpg')])
        total_frames = len(image_files)
        
        if total_frames == 0:
            logger.error(f"序列 {sequence_name} 没有图像文件")
            return []
        
        logger.info(f"序列 {sequence_name}: 总帧数={total_frames}")
        
        video_sequences = []
        video_count = 0
        
        # 按25帧为一组进行处理
        group_size = 25
        for group_start in range(0, total_frames, group_size):
            group_end = min(group_start + group_size, total_frames)
            group_frames = group_end - group_start
            
            if group_frames < frames_per_video:
                logger.warning(f"组 {group_start}-{group_end} 帧数不足: {group_frames}")
                continue
            
            # 在每组内生成5个序列
            for seq_offset in range(frames_per_video):
                if group_start + seq_offset >= total_frames:
                    break
                
                # 按间隔5采样帧
                frame_indices = []
                for i in range(frames_per_video):
                    frame_idx = group_start + seq_offset + i * 5
                    if frame_idx < group_end:
                        frame_indices.append(frame_idx)
                    else:
                        # 如果超出范围，使用最后一帧
                        frame_indices.append(group_end - 1)
                
                # 确保有足够的帧
                if len(frame_indices) < frames_per_video:
                    # 填充最后一帧
                    while len(frame_indices) < frames_per_video:
                        frame_indices.append(frame_indices[-1])
                
                # 生成视频文件
                video_filename = f"{sequence_name}_seq_{video_count:03d}.mp4"
                video_path = self.output_dir / "videos" / video_filename
                video_path.parent.mkdir(parents=True, exist_ok=True)
                
                # 创建视频
                if self.create_video(image_files, frame_indices, video_path, config['size']):
                    # 收集标注信息
                    annotations = self.collect_annotations(
                        labels_dir, frame_indices, image_files
                    )
                    
                    video_sequences.append({
                        'video_filename': video_filename,
                        'frame_indices': frame_indices,
                        'annotations': annotations,
                        'sequence': sequence_name,
                        'video_id': video_count
                    })
                    
                    video_count += 1
        
        logger.info(f"序列 {sequence_name} 生成 {len(video_sequences)} 个视频")
        return video_sequences
    
    def create_video(self, image_files, frame_indices, output_path, target_size):
        """创建视频文件"""
        try:
            # 视频编码器设置
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            fps = 1.0  # 1 FPS，便于查看
            
            out = cv2.VideoWriter(str(output_path), fourcc, fps, target_size)
            
            for frame_idx in frame_indices:
                if frame_idx < len(image_files):
                    image_path = image_files[frame_idx]
                    
                    # 读取图像
                    image = cv2.imread(str(image_path))
                    if image is None:
                        logger.warning(f"无法读取图像: {image_path}")
                        continue
                    
                    # 调整大小
                    image = cv2.resize(image, target_size)
                    out.write(image)
            
            out.release()
            return True
            
        except Exception as e:
            logger.error(f"创建视频失败 {output_path}: {e}")
            return False
    
    def collect_annotations(self, labels_dir, frame_indices, image_files):
        """收集标注信息"""
        annotations = []
        
        for frame_idx in frame_indices:
            if frame_idx < len(image_files):
                image_file = image_files[frame_idx]
                # 从图像文件名推导标注文件名
                label_file = labels_dir / f"{image_file.stem}.txt"
                
                frame_annotations = []
                if label_file.exists():
                    try:
                        with open(label_file, 'r') as f:
                            for line in f:
                                line = line.strip()
                                if line:
                                    parts = line.split()
                                    if len(parts) >= 5:
                                        class_id = int(parts[0])
                                        x_center = float(parts[1])
                                        y_center = float(parts[2])
                                        width = float(parts[3])
                                        height = float(parts[4])
                                        
                                        frame_annotations.append({
                                            'class': class_id,
                                            'bbox': [x_center, y_center, width, height]
                                        })
                    except Exception as e:
                        logger.warning(f"读取标注失败 {label_file}: {e}")
                
                annotations.append(frame_annotations)
        
        return annotations
    
    def process_all_sequences(self):
        """处理所有序列"""
        all_data = {'train': [], 'test': []}
        
        for sequence_name, config in self.dataset_config.items():
            logger.info(f"处理序列: {sequence_name}")
            
            video_sequences = self.generate_video_sequences(sequence_name)
            
            for seq_data in video_sequences:
                # 生成对话数据
                conversation_data = self.generate_conversation(seq_data)
                all_data[config['split']].append(conversation_data)
        
        # 保存数据
        self.save_datasets(all_data)
        
        return all_data
    
    def generate_conversation(self, seq_data):
        """生成对话数据"""
        video_path = f"data/videos/{seq_data['video_filename']}"
        
        # 构建检测结果文本
        detection_results = []
        for frame_idx, frame_annotations in enumerate(seq_data['annotations']):
            if frame_annotations:
                frame_detections = []
                for ann in frame_annotations:
                    bbox_str = f"[{ann['bbox'][0]:.3f}, {ann['bbox'][1]:.3f}, {ann['bbox'][2]:.3f}, {ann['bbox'][3]:.3f}]"
                    frame_detections.append(f'{{"class": {ann["class"]}, "bbox": {bbox_str}}}')
                
                detection_results.append(f'帧{frame_idx}: [{", ".join(frame_detections)}]')
            else:
                detection_results.append(f'帧{frame_idx}: []')
        
        assistant_response = "检测结果:\n" + "\n".join(detection_results)
        
        return {
            'id': f"{seq_data['sequence']}_{seq_data['video_id']:03d}",
            'video': video_path,
            'conversations': [
                {
                    'from': 'human',
                    'value': '<video>\n请检测视频中每一帧的红外目标，返回边界框坐标。'
                },
                {
                    'from': 'gpt',
                    'value': assistant_response
                }
            ]
        }
    
    def save_datasets(self, all_data):
        """保存数据集"""
        # 保存训练集
        train_file = self.output_dir / 'infrared_video_train.json'
        with open(train_file, 'w', encoding='utf-8') as f:
            json.dump(all_data['train'], f, indent=2, ensure_ascii=False)
        
        # 保存测试集
        test_file = self.output_dir / 'infrared_video_test.json'
        with open(test_file, 'w', encoding='utf-8') as f:
            json.dump(all_data['test'], f, indent=2, ensure_ascii=False)
        
        logger.info(f"数据集保存完成:")
        logger.info(f"  训练集: {len(all_data['train'])} 样本 -> {train_file}")
        logger.info(f"  测试集: {len(all_data['test'])} 样本 -> {test_file}")

def main():
    """主函数"""
    processor = InfraredVideoProcessor(
        base_images_dir="/home/<USER>/Qwen/Qwen2.5-VL/tiaozhanbei_datasets/images",
        base_labels_dir="/home/<USER>/Qwen/Qwen2.5-VL/tiaozhanbei_datasets/labels",
        output_dir="data"
    )
    
    # 处理所有序列
    all_data = processor.process_all_sequences()
    
    logger.info("✅ 数据处理完成!")
    logger.info(f"训练样本: {len(all_data['train'])}")
    logger.info(f"测试样本: {len(all_data['test'])}")

if __name__ == "__main__":
    main()
