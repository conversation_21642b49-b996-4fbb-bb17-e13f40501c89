import json

# 修复训练数据格式
with open('data/infrared_video_train.json', 'r') as f:
    data = json.load(f)

for sample in data:
    for conv in sample['conversations']:
        if conv['from'] == 'user':
            conv['from'] = 'human'
        elif conv['from'] == 'assistant':
            conv['from'] = 'gpt'

with open('data/infrared_video_train.json', 'w') as f:
    json.dump(data, f, indent=2, ensure_ascii=False)

print("训练数据格式修复完成")
