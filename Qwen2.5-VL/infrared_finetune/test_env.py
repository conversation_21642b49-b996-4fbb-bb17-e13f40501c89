#!/usr/bin/env python3
"""
测试Qwen环境
"""

import os
import sys

def test_environment():
    """测试环境"""
    print("🔍 测试Qwen环境...")
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    
    # 测试关键包
    packages = ['torch', 'transformers', 'peft', 'accelerate']
    
    for pkg in packages:
        try:
            module = __import__(pkg)
            version = getattr(module, '__version__', 'unknown')
            print(f"✅ {pkg}: {version}")
        except ImportError as e:
            print(f"❌ {pkg}: {e}")
    
    # 测试CUDA
    try:
        import torch
        print(f"🔥 CUDA可用: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"   GPU数量: {torch.cuda.device_count()}")
            print(f"   当前GPU: {torch.cuda.current_device()}")
            print(f"   GPU名称: {torch.cuda.get_device_name()}")
    except:
        print("❌ 无法检测CUDA")
    
    # 测试Qwen2.5-VL
    try:
        from transformers import Qwen2VLForConditionalGeneration, Qwen2VLProcessor
        print("✅ Qwen2.5-VL模块导入成功")
    except ImportError as e:
        print(f"❌ Qwen2.5-VL模块导入失败: {e}")

if __name__ == "__main__":
    test_environment()
