"""
红外视频LoRA微调 - 固定帧数解决方案
确保所有视频都有统一的帧数，从数据预处理开始解决形状不一致问题
"""

import os
import json
import logging
import torch
import numpy as np
from pathlib import Path
from transformers import (
    AutoProcessor,
    Qwen2_5_VLForConditionalGeneration,
    TrainingArguments,
    Trainer
)
from peft import LoraConfig, get_peft_model, TaskType
from torch.utils.data import Dataset
from decord import VideoReader
from PIL import Image

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FixedFrameVideoDataset(Dataset):
    """固定帧数的视频数据集 - 确保所有视频都有相同的帧数"""
    
    def __init__(self, data_list, processor, fixed_frames=5):
        self.data_list = data_list
        self.processor = processor
        self.fixed_frames = fixed_frames  # 固定帧数
        
        # 设置左填充
        self.processor.tokenizer.padding_side = 'left'
        
    def __len__(self):
        return len(self.data_list)
    
    def load_video_fixed_frames(self, video_path):
        """加载视频并确保固定帧数"""
        try:
            if not os.path.exists(video_path):
                logger.warning(f"视频不存在: {video_path}")
                return self._dummy_frames()
            
            vr = VideoReader(video_path, num_threads=1)
            total_frames = len(vr)
            
            if total_frames == 0:
                return self._dummy_frames()
            
            # 均匀采样固定数量的帧
            if total_frames <= self.fixed_frames:
                # 如果视频帧数不足，重复采样
                frame_indices = list(range(total_frames))
                # 填充到固定帧数（重复最后一帧的索引）
                while len(frame_indices) < self.fixed_frames:
                    frame_indices.append(frame_indices[-1])
            else:
                # 均匀采样
                frame_indices = np.linspace(0, total_frames - 1, self.fixed_frames, dtype=int)
            
            # 确保索引数量正确
            frame_indices = frame_indices[:self.fixed_frames]
            
            # 加载帧
            frames = vr.get_batch(frame_indices).asnumpy()
            
            # 转换为PIL图像
            pil_frames = [Image.fromarray(frame) for frame in frames]
            
            # 确保帧数完全一致
            while len(pil_frames) < self.fixed_frames:
                pil_frames.append(pil_frames[-1])  # 重复最后一帧
            
            pil_frames = pil_frames[:self.fixed_frames]  # 确保不超过固定帧数
            
            logger.info(f"视频 {video_path}: 原始帧数={total_frames}, 处理后帧数={len(pil_frames)}")
            return pil_frames
            
        except Exception as e:
            logger.warning(f"视频加载失败 {video_path}: {e}")
            return self._dummy_frames()
    
    def _dummy_frames(self):
        """虚拟帧"""
        frame = Image.new('RGB', (336, 336), 'black')
        return [frame] * self.fixed_frames
    
    def __getitem__(self, idx):
        """获取数据项"""
        if not isinstance(idx, int):
            raise TypeError(f"索引必须是整数，得到: {type(idx)}")
        
        item = self.data_list[idx]
        conversations = item['conversations']
        video_path = item['video']
        
        # 加载固定帧数的视频
        frames = self.load_video_fixed_frames(video_path)
        
        # 构建文本
        user_text = conversations[0]['value'].replace('<video>\n', '').strip()
        assistant_text = conversations[1]['value'].strip()
        
        # 使用官方的对话格式
        messages = [
            {
                "role": "user",
                "content": [
                    {"type": "video", "video": video_path},
                    {"type": "text", "text": user_text}
                ]
            },
            {
                "role": "assistant", 
                "content": assistant_text
            }
        ]
        
        # 使用processor处理
        try:
            text = self.processor.apply_chat_template(
                messages, 
                tokenize=False, 
                add_generation_prompt=False
            )
            
            # 处理视频和文本
            inputs = self.processor(
                images=frames,  # 固定帧数的图像列表
                text=text,
                padding=False,
                truncation=True,
                max_length=4096,
                return_tensors="pt"
            )
            
            input_ids = inputs['input_ids'].squeeze(0)
            attention_mask = inputs['attention_mask'].squeeze(0)
            pixel_values = inputs.get('pixel_values', torch.zeros(self.fixed_frames, 3, 336, 336))
            
            # 确保pixel_values是正确的形状
            if pixel_values.dim() == 5:  # [1, frames, channels, height, width]
                pixel_values = pixel_values.squeeze(0)
            elif pixel_values.dim() == 3:  # [channels, height, width]
                pixel_values = pixel_values.unsqueeze(0)
            
            # 验证帧数
            if pixel_values.shape[0] != self.fixed_frames:
                logger.warning(f"帧数不匹配: {pixel_values.shape[0]} != {self.fixed_frames}")
                # 强制调整
                if pixel_values.shape[0] > self.fixed_frames:
                    pixel_values = pixel_values[:self.fixed_frames]
                else:
                    # 填充
                    pad_frames = self.fixed_frames - pixel_values.shape[0]
                    last_frame = pixel_values[-1:].repeat(pad_frames, 1, 1, 1)
                    pixel_values = torch.cat([pixel_values, last_frame], dim=0)
            
            # 计算grid_thw
            grid_thw = torch.tensor([[self.fixed_frames, 24, 24]], dtype=torch.long)
            
            return {
                'input_ids': input_ids,
                'attention_mask': attention_mask,
                'labels': input_ids.clone(),
                'pixel_values_videos': pixel_values,
                'video_grid_thw': grid_thw
            }
            
        except Exception as e:
            logger.error(f"处理失败 {video_path}: {e}")
            # 返回虚拟数据
            dummy_ids = torch.tensor([151643] * 100)
            return {
                'input_ids': dummy_ids,
                'attention_mask': torch.ones_like(dummy_ids),
                'labels': dummy_ids.clone(),
                'pixel_values_videos': torch.zeros(self.fixed_frames, 3, 336, 336),
                'video_grid_thw': torch.tensor([[self.fixed_frames, 24, 24]], dtype=torch.long)
            }

def simple_collate_fn(batch):
    """简化的数据整理函数 - 所有tensor应该已经是统一形状"""
    print(f"DEBUG - Collating batch of size: {len(batch)}")
    
    input_ids = [item['input_ids'] for item in batch]
    attention_masks = [item['attention_mask'] for item in batch]
    labels = [item['labels'] for item in batch]
    pixel_values_videos = [item['pixel_values_videos'] for item in batch]
    video_grid_thws = [item['video_grid_thw'] for item in batch]
    
    # 调试：检查每个视频tensor的形状
    print("DEBUG - Video tensor shapes:")
    for i, pv in enumerate(pixel_values_videos):
        print(f"  Video {i}: {pv.shape}")
    
    # 验证所有视频tensor形状是否一致
    shapes = [pv.shape for pv in pixel_values_videos]
    if len(set(shapes)) > 1:
        raise RuntimeError(f"视频tensor形状不一致: {shapes}")
    
    # 处理文本数据的填充
    max_length = max(len(ids) for ids in input_ids)
    pad_token_id = 151643
    
    padded_input_ids = []
    padded_attention_masks = []
    padded_labels = []
    
    for i in range(len(batch)):
        pad_length = max_length - len(input_ids[i])
        
        padded_input_ids.append(torch.cat([
            torch.full((pad_length,), pad_token_id, dtype=input_ids[i].dtype),
            input_ids[i]
        ]))
        
        padded_attention_masks.append(torch.cat([
            torch.zeros(pad_length, dtype=attention_masks[i].dtype),
            attention_masks[i]
        ]))
        
        padded_labels.append(torch.cat([
            torch.full((pad_length,), -100, dtype=labels[i].dtype),
            labels[i]
        ]))
    
    print("DEBUG - Final shapes before stacking:")
    print(f"  Input IDs: {[pid.shape for pid in padded_input_ids]}")
    print(f"  Videos: {[pv.shape for pv in pixel_values_videos]}")
    
    return {
        'input_ids': torch.stack(padded_input_ids),
        'attention_mask': torch.stack(padded_attention_masks),
        'labels': torch.stack(padded_labels),
        'pixel_values_videos': torch.stack(pixel_values_videos),
        'video_grid_thw': torch.cat(video_grid_thws, dim=0)
    }

def main():
    """主函数"""
    logger.info("🚀 开始固定帧数视频LoRA微调...")
    
    # 配置
    model_path = "/home/<USER>/Qwen/Qwen2.5-VL/Qwen2.5-VL-7B-Instruct"
    data_path = "data/infrared_video_train.json"
    output_path = "output/fixed_frames_lora"
    fixed_frames = 5  # 固定帧数
    
    Path(output_path).mkdir(parents=True, exist_ok=True)
    
    # 加载数据
    logger.info("📊 加载数据...")
    with open(data_path, 'r') as f:
        train_data = json.load(f)
    logger.info(f"✅ 数据: {len(train_data)} 个样本")
    
    # 加载processor
    logger.info("🔤 加载processor...")
    processor = AutoProcessor.from_pretrained(model_path, trust_remote_code=True)
    processor.tokenizer.padding_side = 'left'
    
    # 创建数据集
    logger.info(f"📝 创建数据集（固定{fixed_frames}帧）...")
    dataset = FixedFrameVideoDataset(train_data, processor, fixed_frames=fixed_frames)
    
    # 加载模型
    logger.info("🤖 加载模型...")
    model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
        model_path,
        torch_dtype=torch.bfloat16,
        device_map={"": "cuda:0"},
        trust_remote_code=True,
        attn_implementation="flash_attention_2"
    )
    
    # LoRA配置
    logger.info("⚙️ 配置LoRA...")
    lora_config = LoraConfig(
        task_type=TaskType.CAUSAL_LM,
        target_modules=["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"],
        inference_mode=False,
        r=64,
        lora_alpha=64,
        lora_dropout=0.05,
        bias="none",
    )
    
    model = get_peft_model(model, lora_config)
    model.print_trainable_parameters()
    
    # 训练参数
    training_args = TrainingArguments(
        output_dir=output_path,
        per_device_train_batch_size=2,  # 小批次
        gradient_accumulation_steps=8,  # 增加梯度累积
        num_train_epochs=0.5,
        learning_rate=2e-7,
        weight_decay=0,
        warmup_ratio=0.03,
        max_grad_norm=1.0,
        lr_scheduler_type="cosine",
        logging_steps=1,
        save_steps=50,
        save_strategy="steps",
        save_total_limit=1,
        remove_unused_columns=False,
        bf16=True,
        dataloader_num_workers=0,  # 禁用多进程
        gradient_checkpointing=True,
        report_to="none",
    )
    
    # 训练器
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=dataset,
        data_collator=simple_collate_fn,
        processing_class=processor,
    )
    
    # 开始训练
    logger.info("🎯 开始训练（固定帧数）...")
    logger.info(f"样本数: {len(dataset)}")
    logger.info(f"固定帧数: {fixed_frames}")
    logger.info(f"批次大小: {training_args.per_device_train_batch_size}")
    logger.info(f"梯度累积: {training_args.gradient_accumulation_steps}")
    
    trainer.train()
    
    # 保存
    logger.info("💾 保存模型...")
    trainer.save_model()
    processor.save_pretrained(output_path)
    
    logger.info("🎉 训练完成!")
    logger.info(f"📁 保存位置: {output_path}")

if __name__ == "__main__":
    main()
