"""
红外视频目标检测器
使用方法: python detect.py [--lora] [--video VIDEO_PATH]
"""
import os
import cv2
import torch
import argparse
import logging
from typing import List, Dict
from pathlib import Path
from PIL import Image
from transformers import Qwen2_5_VLForConditionalGeneration, AutoProcessor
from qwen_vl_utils import process_vision_info

# 设置环境和日志
os.environ["CUDA_VISIBLE_DEVICES"] = "2"
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class InfraredDetector:
    """红外视频目标检测器"""
    
    def __init__(self, use_lora=False):
        """
        初始化检测器
        
        Args:
            use_lora: 是否使用LoRA微调模型
        """
        self.use_lora = use_lora
        self.base_model_path = "/home/<USER>/Qwen/Qwen2.5-VL/Qwen2.5-VL-7B-Instruct"
        self.lora_path = "output/lora_checkpoints"
        
        self.model = None
        self.processor = None
        self._load_model()
    
    def _load_model(self):
        """加载模型"""
        try:
            logger.info("🤖 加载模型...")
            
            # 加载基础模型
            self.model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
                self.base_model_path,
                torch_dtype=torch.bfloat16,
                device_map={"": "cuda:0"},
                trust_remote_code=True,
                attn_implementation="flash_attention_2"
            )
            
            # 如果使用LoRA，加载LoRA权重
            if self.use_lora and os.path.exists(self.lora_path):
                logger.info("🔧 加载LoRA权重...")
                from peft import PeftModel
                self.model = PeftModel.from_pretrained(
                    self.model, 
                    self.lora_path,
                    torch_dtype=torch.bfloat16
                )
                logger.info("✅ LoRA模型加载成功")
            else:
                logger.info("✅ 基础模型加载成功")
            
            # 加载处理器
            self.processor = AutoProcessor.from_pretrained(
                self.base_model_path,
                trust_remote_code=True,
                min_pixels=256*28*28,
                max_pixels=1280*28*28
            )
            
        except Exception as e:
            logger.error(f"❌ 模型加载失败: {str(e)}")
            raise
    
    def detect_video(self, video_path: str, max_frames: int = 5) -> List[Dict]:
        """
        检测视频中的目标
        
        Args:
            video_path: 视频文件路径
            max_frames: 最大处理帧数
            
        Returns:
            检测结果列表
        """
        logger.info(f"🎯 开始检测视频: {video_path}")
        
        # 读取视频帧
        frames = self._extract_frames(video_path, max_frames)
        if not frames:
            logger.error("❌ 无法提取视频帧")
            return []
        
        # 逐帧检测
        results = []
        for i, frame in enumerate(frames):
            logger.info(f"🔍 检测第 {i+1}/{len(frames)} 帧...")
            result = self._detect_frame(frame, i)
            results.append(result)
        
        logger.info(f"✅ 检测完成，共处理 {len(frames)} 帧")
        return results
    
    def _extract_frames(self, video_path: str, max_frames: int) -> List[Image.Image]:
        """提取视频帧"""
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            return []
        
        frames = []
        for i in range(max_frames):
            ret, frame = cap.read()
            if not ret:
                break
            
            # 转换为RGB
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            # 调整大小
            height, width = frame_rgb.shape[:2]
            if width > 640:
                scale = 640 / width
                new_width = 640
                new_height = int(height * scale)
                frame_rgb = cv2.resize(frame_rgb, (new_width, new_height))
            
            frames.append(Image.fromarray(frame_rgb))
        
        cap.release()
        return frames
    
    def _detect_frame(self, frame: Image.Image, frame_idx: int) -> Dict:
        """检测单帧"""
        try:
            # 构建消息
            messages = [
                {
                    "role": "user",
                    "content": [
                        {"type": "image", "image": frame},
                        {"type": "text", "text": "这是一个红外图像帧。请检测其中的微小移动目标，如无人机、汽车、船只等。请以JSON格式输出检测结果：{\"frame_001\": [{\"bbox\": [x1, y1, x2, y2], \"class_name\": \"drone\", \"confidence\": 0.95}]}"}
                    ]
                }
            ]
            
            # 处理输入
            text = self.processor.apply_chat_template(
                messages, tokenize=False, add_generation_prompt=True
            )
            
            image_inputs, video_inputs = process_vision_info(messages)
            
            inputs = self.processor(
                text=[text],
                images=image_inputs,
                videos=video_inputs,
                padding=True,
                return_tensors="pt"
            )
            inputs = inputs.to("cuda:0")
            
            # 生成检测结果
            with torch.no_grad():
                generated_ids = self.model.generate(
                    **inputs,
                    max_new_tokens=256,
                    do_sample=False,
                    temperature=0.1
                )
            
            # 解码结果
            generated_ids_trimmed = [
                out_ids[len(in_ids):] for in_ids, out_ids in zip(inputs.input_ids, generated_ids)
            ]
            
            output_text = self.processor.batch_decode(
                generated_ids_trimmed, skip_special_tokens=True, clean_up_tokenization_spaces=False
            )[0]
            
            # 解析检测结果
            has_target, confidence, bbox = self._parse_output(output_text, frame.size)
            
            return {
                'frame_id': frame_idx,
                'has_target': has_target,
                'confidence': confidence,
                'bbox': bbox,
                'raw_output': output_text
            }
            
        except Exception as e:
            logger.error(f"❌ 帧 {frame_idx} 检测失败: {str(e)}")
            return {
                'frame_id': frame_idx,
                'has_target': False,
                'confidence': 0.0,
                'bbox': None,
                'raw_output': f"检测失败: {str(e)}"
            }
    
    def _parse_output(self, output_text: str, frame_size):
        """解析模型输出"""
        import json
        import re
        
        try:
            # 尝试解析JSON格式
            json_match = re.search(r'\{[^{}]*"frame_\d+[^{}]*\[[^]]*\][^{}]*\}', output_text, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                detection_data = json.loads(json_str)
                
                for frame_key, detections in detection_data.items():
                    if isinstance(detections, list) and len(detections) > 0:
                        detection = detections[0]
                        if 'bbox' in detection and 'confidence' in detection:
                            bbox = detection['bbox']
                            confidence = detection['confidence']
                            return True, confidence, bbox
            
            # 关键词检测
            if any(keyword in output_text.lower() for keyword in ['target', 'drone', 'car', 'ship']):
                return True, 0.6, None
            else:
                return False, 0.0, None
                
        except:
            return False, 0.0, None

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="红外视频目标检测")
    parser.add_argument("--lora", action="store_true", help="使用LoRA微调模型")
    parser.add_argument("--video", type=str, default="data/videos/data01_seq_000.mp4", help="视频文件路径")
    parser.add_argument("--frames", type=int, default=5, help="处理帧数")
    args = parser.parse_args()
    
    # 创建检测器
    detector = InfraredDetector(use_lora=args.lora)
    
    # 检测视频
    if os.path.exists(args.video):
        results = detector.detect_video(args.video, args.frames)
        
        # 输出结果
        print("\n📊 检测结果:")
        for result in results:
            status = "✔ 有目标" if result['has_target'] else "✗ 无目标"
            print(f"帧 {result['frame_id']}: {status} (置信度: {result['confidence']:.3f})")
            if result['bbox']:
                print(f"   边界框: {result['bbox']}")
    else:
        print(f"❌ 视频文件不存在: {args.video}")

if __name__ == "__main__":
    main()
