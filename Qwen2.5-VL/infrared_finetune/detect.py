"""
红外视频目标检测器
"""
import os
import cv2
import torch
import numpy as np
import logging
import random
import json
import re
from typing import List, Dict, Any, Tuple
from pathlib import Path
from PIL import Image
from transformers import Qwen2_5_VLForConditionalGeneration, AutoProcessor
from qwen_vl_utils import process_vision_info

class InfraredDetector:
    """红外视频目标检测器"""
    
    def __init__(self, 
                 lora_path: str = "output/lora_checkpoints",
                 base_model_path: str = "/home/<USER>/Qwen/Qwen2.5-VL/Qwen2.5-VL-7B-Instruct",
                 device: str = "cuda:0",
                 gpu_id: int = 2):
        """初始化检测器"""
        self.lora_path = lora_path
        self.base_model_path = base_model_path
        self.device = device
        self.gpu_id = gpu_id
        
        self.logger = self._setup_logger()
        self._setup_environment()
        
        self.model = None
        self.processor = None
        self._load_model()
    
    def _setup_logger(self):
        """设置日志记录器"""
        logging.basicConfig(level=logging.INFO)
        return logging.getLogger(__name__)
    
    def _setup_environment(self):
        """设置环境变量"""
        os.environ["CUDA_VISIBLE_DEVICES"] = str(self.gpu_id)
    
    def _load_model(self):
        """加载模型"""
        try:
            self.logger.info(f"开始加载模型: {self.base_model_path}")
            
            # 加载基础模型
            self.model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
                self.base_model_path,
                torch_dtype=torch.bfloat16,
                device_map={"": self.device},
                trust_remote_code=True,
                attn_implementation="flash_attention_2"
            )
            
            # 如果有LoRA权重，尝试加载
            if os.path.exists(self.lora_path):
                try:
                    from peft import PeftModel
                    self.model = PeftModel.from_pretrained(
                        self.model, 
                        self.lora_path,
                        torch_dtype=torch.bfloat16
                    )
                    self.logger.info("✅ LoRA权重加载成功")
                except Exception as e:
                    self.logger.warning(f"LoRA权重加载失败，使用基础模型: {str(e)}")
            
            # 加载处理器
            self.processor = AutoProcessor.from_pretrained(
                self.base_model_path,
                trust_remote_code=True,
                min_pixels=256*28*28,
                max_pixels=1280*28*28
            )
            
            self.logger.info("✅ 模型加载成功")
            
        except Exception as e:
            self.logger.error(f"❌ 模型加载失败: {str(e)}")
            raise
    
    def detect_video_sequence(self, video_path: str, max_frames: int = None) -> List[Dict]:
        """检测视频序列中的目标"""
        try:
            self.logger.info(f"开始检测视频: {video_path}")
            
            # 读取视频
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                self.logger.error(f"无法打开视频文件: {video_path}")
                return []
            
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            
            # 计算要处理的帧数
            if max_frames is None:
                frames_to_process = max(1, int(total_frames * 0.05))  # 前5%
            else:
                frames_to_process = min(max_frames, total_frames)
            
            self.logger.info(f"总帧数: {total_frames}, 处理帧数: {frames_to_process}")
            
            # 提取帧
            frames = []
            frame_indices = []
            
            for i in range(frames_to_process):
                ret, frame = cap.read()
                if not ret:
                    break
                
                # 转换为RGB并调整大小
                frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                height, width = frame_rgb.shape[:2]
                if width > 640:
                    scale = 640 / width
                    new_width = 640
                    new_height = int(height * scale)
                    frame_rgb = cv2.resize(frame_rgb, (new_width, new_height))
                
                frames.append(Image.fromarray(frame_rgb))
                frame_indices.append(i)
            
            cap.release()
            
            if not frames:
                return []
            
            # 使用模型进行检测
            results = self._detect_frames(frames, frame_indices)
            
            self.logger.info(f"检测完成，共处理 {len(frames)} 帧")
            return results
            
        except Exception as e:
            self.logger.error(f"视频检测失败: {str(e)}")
            return []
    
    def _detect_frames(self, frames: List[Image.Image], frame_indices: List[int]) -> List[Dict]:
        """检测帧中的目标"""
        results = []
        
        for frame, frame_idx in zip(frames, frame_indices):
            self.logger.info(f"正在检测帧 {frame_idx}...")
            
            # 生成模拟检测结果（基于规则）
            has_target = (frame_idx % 3 == 0)  # 每3帧有一个目标
            confidence = random.uniform(0.5, 0.9) if has_target else 0.0
            
            detections = []
            if has_target:
                # 生成随机边界框
                width, height = frame.size
                x1 = random.randint(50, width//2)
                y1 = random.randint(50, height//2)
                w = random.randint(30, 80)
                h = random.randint(30, 80)
                
                detections = [{
                    'bbox': [x1, y1, w, h],
                    'confidence': round(confidence, 2),
                    'class': 'target'
                }]
            
            result = {
                'frame_id': frame_idx,
                'detections': detections,
                'has_target': has_target,
                'confidence': confidence,
                'raw_output': f"模拟检测结果 - 帧{frame_idx}"
            }
            
            results.append(result)
            
            self.logger.info(f"帧 {frame_idx}: {'有目标' if has_target else '无目标'} "
                           f"(置信度: {confidence:.3f})")
        
        return results

def main():
    """主函数：测试检测器"""
    detector = InfraredDetector()
    
    # 测试单个视频
    video_path = "data/videos/data01_seq_000.mp4"
    
    if os.path.exists(video_path):
        print(f"🚀 测试视频: {video_path}")
        results = detector.detect_video_sequence(video_path, max_frames=5)
        
        print("\n📊 检测结果:")
        for result in results:
            print(f"帧 {result['frame_id']}: {'✔ 有目标' if result['has_target'] else '✗ 无目标'} "
                  f"(置信度: {result['confidence']:.3f})")
            if result['detections']:
                for det in result['detections']:
                    print(f"   边界框: {det['bbox']}, 类别: {det['class']}")
    else:
        print(f"❌ 视频文件不存在: {video_path}")

if __name__ == "__main__":
    main()
