#!/usr/bin/env python3
"""
测试训练脚本
使用最小的数据集进行测试
"""

import os
import sys
import json
import logging

# 添加LLaMA-Factory路径
sys.path.insert(0, '/home/<USER>/Qwen/Qwen2.5-VL/LLaMA-Factory/src')

# 设置环境变量
os.environ['CUDA_VISIBLE_DEVICES'] = '0'

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_minimal_dataset():
    """创建最小的测试数据集"""
    
    # 切换到LLaMA-Factory目录
    os.chdir('/home/<USER>/Qwen/Qwen2.5-VL/LLaMA-Factory')
    
    # 读取原始训练数据
    with open('data/infrared_detection_train.json', 'r', encoding='utf-8') as f:
        full_data = json.load(f)
    
    # 只取前5个样本进行测试
    test_data = full_data[:5]
    
    # 保存测试数据
    with open('data/infrared_detection_test_mini.json', 'w', encoding='utf-8') as f:
        json.dump(test_data, f, ensure_ascii=False, indent=2)
    
    logger.info(f"创建了包含 {len(test_data)} 个样本的测试数据集")
    
    # 验证视频文件是否存在
    for i, item in enumerate(test_data):
        if 'videos' in item:
            for video_path in item['videos']:
                exists = os.path.exists(video_path)
                logger.info(f"样本 {i+1}: {os.path.basename(video_path)} - 存在: {exists}")
                if not exists:
                    logger.error(f"视频文件不存在: {video_path}")
                    return False
    
    return True

def update_dataset_info():
    """更新数据集信息"""
    
    dataset_info_file = 'data/dataset_info.json'
    
    # 读取现有的dataset_info.json
    with open(dataset_info_file, 'r', encoding='utf-8') as f:
        dataset_info = json.load(f)
    
    # 添加测试数据集信息
    dataset_info['infrared_detection_test_mini'] = {
        "file_name": "infrared_detection_test_mini.json",
        "formatting": "sharegpt",
        "columns": {
            "messages": "messages",
            "videos": "videos"
        },
        "tags": {
            "role_tag": "role",
            "content_tag": "content",
            "user_tag": "user",
            "assistant_tag": "assistant"
        }
    }
    
    # 保存更新后的dataset_info.json
    with open(dataset_info_file, 'w', encoding='utf-8') as f:
        json.dump(dataset_info, f, ensure_ascii=False, indent=2)
    
    logger.info("数据集信息已更新")

def run_test_training():
    """运行测试训练"""
    
    try:
        logger.info("开始测试训练...")
        
        # 设置训练参数
        sys.argv = [
            'train.py',
            '--model_name_or_path', '/home/<USER>/Qwen/Qwen2.5-VL/Qwen2.5-VL-7B-Instruct',
            '--stage', 'sft',
            '--do_train',
            '--finetuning_type', 'lora',
            '--dataset', 'infrared_detection_test_mini',
            '--template', 'qwen2_vl',
            '--cutoff_len', '4096',
            '--learning_rate', '5e-05',
            '--num_train_epochs', '0.1',  # 很少的训练步数
            '--per_device_train_batch_size', '1',
            '--gradient_accumulation_steps', '2',
            '--lr_scheduler_type', 'cosine',
            '--warmup_ratio', '0.1',
            '--bf16',
            '--logging_steps', '1',
            '--save_steps', '10',
            '--output_dir', '/home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/output/test_checkpoints',
            '--lora_rank', '32',
            '--lora_alpha', '8',
            '--lora_dropout', '0.05',
            '--lora_target', 'q_proj,k_proj,v_proj,o_proj',
            '--max_new_tokens', '1024',
            '--overwrite_output_dir'
        ]
        
        from llamafactory.train.tuner import run_exp
        run_exp()
        
        logger.info("测试训练完成！")
        return True
        
    except Exception as e:
        logger.error(f"测试训练失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    logger.info("开始训练测试...")
    
    # 创建最小数据集
    if not create_minimal_dataset():
        logger.error("创建测试数据集失败")
        return
    
    # 更新数据集信息
    update_dataset_info()
    
    # 运行测试训练
    success = run_test_training()
    
    if success:
        logger.info("训练测试成功！")
        
        # 检查输出目录
        output_dir = "/home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/output/test_checkpoints"
        if os.path.exists(output_dir):
            files = os.listdir(output_dir)
            logger.info(f"输出文件: {files}")
        else:
            logger.warning("输出目录不存在")
    else:
        logger.error("训练测试失败")

if __name__ == "__main__":
    main()
