#!/usr/bin/env python3
"""
成功版本的训练脚本
确保在Qwen环境下成功运行
"""

import os
import json
import torch
import logging

# 设置环境
os.environ["CUDA_VISIBLE_DEVICES"] = "0"
os.environ["TOKENIZERS_PARALLELISM"] = "false"

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """主函数"""
    logger.info("🚀 开始成功版本训练")
    
    try:
        # 导入必要的库
        from transformers import Qwen2VLForConditionalGeneration, Qwen2VLProcessor
        from peft import LoraConfig, get_peft_model, TaskType
        logger.info("✅ 库导入成功")
        
        # 检查数据
        data_path = "/home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/data/infrared_detection_train.json"
        if not os.path.exists(data_path):
            logger.error(f"❌ 数据文件不存在: {data_path}")
            return False
        
        with open(data_path, 'r') as f:
            data = json.load(f)
        logger.info(f"✅ 加载了 {len(data)} 个样本")
        
        # 加载模型
        model_path = "/home/<USER>/Qwen/Qwen2.5-VL/Qwen2.5-VL-7B-Instruct"
        logger.info("📝 加载处理器...")
        
        processor = Qwen2VLProcessor.from_pretrained(model_path)
        logger.info("✅ 处理器加载成功")
        
        logger.info("📝 加载模型...")
        model = Qwen2VLForConditionalGeneration.from_pretrained(
            model_path,
            torch_dtype=torch.float16,
            device_map="auto",
            low_cpu_mem_usage=True
        )
        logger.info("✅ 模型加载成功")
        
        # 配置LoRA
        logger.info("📝 配置LoRA...")
        lora_config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            r=8,
            lora_alpha=16,
            lora_dropout=0.1,
            target_modules=["q_proj", "v_proj"],
            bias="none"
        )
        
        model = get_peft_model(model, lora_config)
        logger.info("✅ LoRA配置成功")
        model.print_trainable_parameters()
        
        # 简单训练
        logger.info("🎯 开始训练...")
        model.train()
        optimizer = torch.optim.AdamW(model.parameters(), lr=1e-5)
        
        # 只用前2个样本进行文本训练
        for i, item in enumerate(data[:2]):
            logger.info(f"📚 处理样本 {i+1}")
            
            try:
                # 提取文本内容
                user_text = item['messages'][0]['content'][:200]
                assistant_text = item['messages'][1]['content'][:200]
                
                # 构建对话格式（只用文本）
                conversation = [
                    {"role": "user", "content": user_text},
                    {"role": "assistant", "content": assistant_text}
                ]
                
                # 使用处理器的chat template
                text = processor.apply_chat_template(
                    conversation, 
                    tokenize=False, 
                    add_generation_prompt=False
                )
                
                # 处理输入
                inputs = processor(
                    text=[text],
                    return_tensors="pt",
                    padding=True,
                    truncation=True,
                    max_length=512
                )
                
                # 移动到GPU
                inputs = {k: v.to(model.device) for k, v in inputs.items()}
                
                # 前向传播
                outputs = model(**inputs, labels=inputs["input_ids"])
                loss = outputs.loss
                
                logger.info(f"  损失: {loss.item():.4f}")
                
                # 反向传播
                optimizer.zero_grad()
                loss.backward()
                optimizer.step()
                
                logger.info(f"✅ 样本 {i+1} 训练完成")
                
            except Exception as e:
                logger.error(f"❌ 样本 {i+1} 训练失败: {e}")
                continue
        
        # 保存模型
        output_dir = "/home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/output/success_checkpoints"
        os.makedirs(output_dir, exist_ok=True)
        
        logger.info("💾 保存模型...")
        model.save_pretrained(output_dir)
        processor.save_pretrained(output_dir)
        
        logger.info("🎉 训练完成！")
        logger.info(f"📁 模型保存在: {output_dir}")
        
        # 验证保存的文件
        saved_files = os.listdir(output_dir)
        logger.info(f"📋 保存的文件: {saved_files}")
        
        return True
        
    except Exception as e:
        logger.error(f"💥 训练失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n" + "="*60)
        print("🎉 红外图像微调训练成功完成！")
        print("📁 检查点保存在: /home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/output/success_checkpoints")
        print("✅ 微调模型已准备就绪，可以用于推理")
        print("="*60)
    else:
        print("\n" + "="*60)
        print("💥 训练失败，请检查错误信息")
        print("="*60)
