"""
Qwen2.5-VL红外视频目标检测微调训练器
基于官方qwen-vl-finetune框架进行微调
"""
import os
import sys
import torch
import logging
from pathlib import Path

# 添加qwenvl路径
sys.path.append('/home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/qwenvl')

from qwenvl.train.train_qwen import train

class InfraredFineTuner:
    """红外视频检测微调器"""
    
    def __init__(self, 
                 model_path: str = "/home/<USER>/Qwen/Qwen2.5-VL/Qwen2.5-VL-7B-Instruct",
                 data_path: str = "/home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/data",
                 output_path: str = "/home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/output/official_checkpoints",
                 gpu_id: int = 2):
        """
        初始化微调器
        
        Args:
            model_path: 基础模型路径
            data_path: 数据路径
            output_path: 输出路径
            gpu_id: GPU设备ID
        """
        self.model_path = model_path
        self.data_path = data_path
        self.output_path = output_path
        self.gpu_id = gpu_id
        
        self.logger = self._setup_logger()
        self._setup_environment()
    
    def _setup_logger(self):
        """设置日志记录器"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        return logging.getLogger(__name__)
    
    def _setup_environment(self):
        """设置环境变量"""
        os.environ["CUDA_VISIBLE_DEVICES"] = str(self.gpu_id)
        os.environ["TOKENIZERS_PARALLELISM"] = "false"
        
        # 创建输出目录
        Path(self.output_path).mkdir(parents=True, exist_ok=True)
    
    def prepare_training_config(self) -> dict:
        """
        准备训练配置
        
        Returns:
            训练配置字典
        """
        config = {
            # 模型配置
            "model_name_or_path": self.model_path,
            "output_dir": self.output_path,
            
            # 数据配置
            "data_path": f"{self.data_path}/infrared_video_train.json",
            "eval_data_path": f"{self.data_path}/infrared_video_test.json",
            
            # 训练参数
            "num_train_epochs": 1,
            "per_device_train_batch_size": 1,
            "per_device_eval_batch_size": 1,
            "gradient_accumulation_steps": 8,
            "learning_rate": 1e-5,
            "weight_decay": 0.01,
            "warmup_ratio": 0.03,
            
            # 优化器配置
            "optim": "adamw_torch",
            "adam_beta1": 0.9,
            "adam_beta2": 0.999,
            "adam_epsilon": 1e-8,
            
            # 调度器配置
            "lr_scheduler_type": "cosine",
            
            # 保存配置
            "save_strategy": "epoch",
            "save_total_limit": 2,
            "save_steps": 500,
            
            # 评估配置
            "evaluation_strategy": "no",  # 暂时关闭评估以节省时间
            "eval_steps": 500,
            
            # 日志配置
            "logging_steps": 10,
            "report_to": "none",
            
            # 其他配置
            "remove_unused_columns": False,
            "dataloader_pin_memory": False,
            "dataloader_num_workers": 0,
            
            # 模型特定配置
            "model_max_length": 2048,
            "freeze_vision_tower": True,  # 冻结视觉编码器
            "freeze_llm": False,          # 训练LLM层
            "tune_merger": True,          # 训练融合层
            
            # Flash Attention
            "attn_implementation": "flash_attention_2",
            
            # 混合精度
            "bf16": True,
            "tf32": True,
            
            # 梯度相关
            "gradient_checkpointing": True,
            "max_grad_norm": 1.0,
            
            # 随机种子
            "seed": 42,
            "data_seed": 42,
        }
        
        return config
    
    def start_training(self):
        """开始微调训练"""
        self.logger.info("🚀 开始红外视频目标检测微调训练")
        self.logger.info(f"📁 模型路径: {self.model_path}")
        self.logger.info(f"📁 数据路径: {self.data_path}")
        self.logger.info(f"📁 输出路径: {self.output_path}")
        self.logger.info(f"🎯 使用GPU: {self.gpu_id}")
        
        try:
            # 检查数据文件
            train_file = f"{self.data_path}/infrared_video_train.json"
            if not os.path.exists(train_file):
                raise FileNotFoundError(f"训练数据文件不存在: {train_file}")
            
            # 检查模型路径
            if not os.path.exists(self.model_path):
                raise FileNotFoundError(f"模型路径不存在: {self.model_path}")
            
            self.logger.info("✅ 数据和模型检查通过")
            
            # 准备训练配置
            config = self.prepare_training_config()
            
            # 设置训练参数
            sys.argv = [
                "train_qwen.py",
                "--model_name_or_path", config["model_name_or_path"],
                "--data_path", config["data_path"],
                "--output_dir", config["output_dir"],
                "--num_train_epochs", str(config["num_train_epochs"]),
                "--per_device_train_batch_size", str(config["per_device_train_batch_size"]),
                "--gradient_accumulation_steps", str(config["gradient_accumulation_steps"]),
                "--learning_rate", str(config["learning_rate"]),
                "--weight_decay", str(config["weight_decay"]),
                "--warmup_ratio", str(config["warmup_ratio"]),
                "--lr_scheduler_type", config["lr_scheduler_type"],
                "--logging_steps", str(config["logging_steps"]),
                "--save_strategy", config["save_strategy"],
                "--save_total_limit", str(config["save_total_limit"]),
                "--model_max_length", str(config["model_max_length"]),
                "--remove_unused_columns", str(config["remove_unused_columns"]).lower(),
                "--dataloader_pin_memory", str(config["dataloader_pin_memory"]).lower(),
                "--dataloader_num_workers", str(config["dataloader_num_workers"]),
                "--bf16", str(config["bf16"]).lower(),
                "--tf32", str(config["tf32"]).lower(),
                "--gradient_checkpointing", str(config["gradient_checkpointing"]).lower(),
                "--max_grad_norm", str(config["max_grad_norm"]),
                "--seed", str(config["seed"]),
                "--report_to", config["report_to"]
            ]
            
            self.logger.info("🎯 开始训练...")
            
            # 调用官方训练函数
            train(attn_implementation="flash_attention_2")
            
            self.logger.info("🎉 微调训练完成!")
            self.logger.info(f"📁 模型保存在: {self.output_path}")
            
        except Exception as e:
            self.logger.error(f"❌ 训练失败: {str(e)}")
            raise
    
    def validate_checkpoint(self) -> bool:
        """验证检查点是否正确保存"""
        try:
            checkpoint_files = list(Path(self.output_path).glob("*.bin"))
            config_file = Path(self.output_path) / "config.json"
            
            if checkpoint_files and config_file.exists():
                self.logger.info(f"✅ 检查点验证通过，找到 {len(checkpoint_files)} 个模型文件")
                return True
            else:
                self.logger.warning("⚠️ 检查点验证失败，未找到完整的模型文件")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ 检查点验证出错: {str(e)}")
            return False


def main():
    """主函数"""
    # 创建微调器
    trainer = InfraredFineTuner(
        model_path="/home/<USER>/Qwen/Qwen2.5-VL/Qwen2.5-VL-7B-Instruct",
        data_path="/home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/data",
        output_path="/home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/output/official_checkpoints",
        gpu_id=2
    )
    
    # 开始训练
    trainer.start_training()
    
    # 验证检查点
    if trainer.validate_checkpoint():
        print("🎉 微调训练成功完成!")
    else:
        print("⚠️ 训练完成但检查点可能有问题")


if __name__ == "__main__":
    main()
