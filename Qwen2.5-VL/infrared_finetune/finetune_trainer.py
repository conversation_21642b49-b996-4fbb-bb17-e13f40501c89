"""
Qwen2.5-VL红外视频目标检测LoRA微调训练器
参考官方目标检测项目，使用LoRA进行参数高效微调
"""
import os
import sys
import torch
import torch.nn as nn
import torch.nn.functional as F
import logging
import json
import re
from pathlib import Path
from typing import Dict, Any, List, Tuple

# 导入必要的库
from transformers import (
    Qwen2_5_VLForConditionalGeneration,
    AutoProcessor,
    TrainingArguments,
    Trainer,
    DataCollatorForSeq2Seq
)
from peft import LoraConfig, TaskType, get_peft_model
from datasets import Dataset
from qwen_vl_utils import process_vision_info
import torch.nn as nn
import torch.nn.functional as F
import re
import json


class IoULoss(nn.Module):
    """IoU损失函数，用于边界框回归"""

    def __init__(self):
        super(IoULoss, self).__init__()

    def forward(self, pred_boxes, target_boxes):
        """
        计算IoU损失

        Args:
            pred_boxes: 预测边界框 [N, 4] (x1, y1, x2, y2)
            target_boxes: 真实边界框 [N, 4] (x1, y1, x2, y2)

        Returns:
            IoU损失值
        """
        # 计算交集
        x1 = torch.max(pred_boxes[:, 0], target_boxes[:, 0])
        y1 = torch.max(pred_boxes[:, 1], target_boxes[:, 1])
        x2 = torch.min(pred_boxes[:, 2], target_boxes[:, 2])
        y2 = torch.min(pred_boxes[:, 3], target_boxes[:, 3])

        # 计算交集面积
        intersection = torch.clamp(x2 - x1, min=0) * torch.clamp(y2 - y1, min=0)

        # 计算各自面积
        area_pred = (pred_boxes[:, 2] - pred_boxes[:, 0]) * (pred_boxes[:, 3] - pred_boxes[:, 1])
        area_target = (target_boxes[:, 2] - target_boxes[:, 0]) * (target_boxes[:, 3] - target_boxes[:, 1])

        # 计算并集面积
        union = area_pred + area_target - intersection

        # 计算IoU
        iou = intersection / (union + 1e-6)

        # IoU损失 = 1 - IoU
        iou_loss = 1 - iou

        return iou_loss.mean()


class CustomTrainer(Trainer):
    """自定义训练器，包含IoU损失"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.iou_loss_fn = IoULoss()
        self.iou_loss_weight = 0.5  # IoU损失权重50%
        self.logger = logging.getLogger(__name__)

    def extract_bbox_from_text(self, text):
        """从文本中提取边界框坐标"""
        try:
            # 查找bbox_2d格式
            bbox_match = re.search(r'"bbox_2d":\s*\[([^\]]+)\]', text)
            if bbox_match:
                coords_str = bbox_match.group(1)
                coords = [float(x.strip()) for x in coords_str.split(',')]
                if len(coords) == 4:
                    return torch.tensor(coords)
            return None
        except:
            return None

    def extract_bbox_tokens(self, input_ids, labels):
        """提取边界框相关的token位置"""
        bbox_token_positions = []

        # 查找包含数字的token（边界框坐标）
        for i, (input_id, label) in enumerate(zip(input_ids, labels)):
            if label != -100:  # 只考虑需要预测的token
                token_text = self.tokenizer.decode([input_id])
                # 检查是否是数字token
                if re.search(r'\d', token_text):
                    bbox_token_positions.append(i)

        return bbox_token_positions

    def compute_loss(self, model, inputs, return_outputs=False):
        """
        自定义损失计算，结合语言模型损失和IoU损失
        """
        # 获取标准的语言模型损失和输出
        outputs = model(**inputs)
        lm_loss = outputs.loss
        logits = outputs.logits

        # 计算IoU相关的损失
        iou_loss = torch.tensor(0.0, device=lm_loss.device)

        try:
            if hasattr(inputs, 'labels') and inputs.labels is not None:
                batch_size = inputs.labels.shape[0]

                for batch_idx in range(batch_size):
                    # 获取当前样本的标签和logits
                    sample_labels = inputs.labels[batch_idx]
                    sample_logits = logits[batch_idx]

                    # 找到边界框相关的token位置
                    bbox_positions = self.extract_bbox_tokens(
                        inputs.input_ids[batch_idx],
                        sample_labels
                    )

                    if bbox_positions:
                        # 对边界框相关的token应用额外的损失权重
                        bbox_logits = sample_logits[bbox_positions]
                        bbox_labels = sample_labels[bbox_positions]

                        # 计算边界框token的交叉熵损失
                        bbox_loss = F.cross_entropy(
                            bbox_logits.view(-1, bbox_logits.size(-1)),
                            bbox_labels.view(-1),
                            ignore_index=-100,
                            reduction='mean'
                        )

                        iou_loss += bbox_loss

                # 平均化IoU损失
                if batch_size > 0:
                    iou_loss = iou_loss / batch_size

        except Exception as e:
            # 如果IoU损失计算失败，只使用语言模型损失
            pass

        # 组合损失：50% 语言模型损失 + 50% IoU损失
        total_loss = (1 - self.iou_loss_weight) * lm_loss + self.iou_loss_weight * iou_loss

        # 记录损失信息（每100步记录一次）
        if hasattr(self, 'state') and self.state.global_step % 100 == 0:
            print(f"Step {self.state.global_step}: LM Loss: {lm_loss:.4f}, IoU Loss: {iou_loss:.4f}, Total: {total_loss:.4f}")

        return (total_loss, outputs) if return_outputs else total_loss


class IoULoss(nn.Module):
    """IoU损失函数，用于边界框回归"""

    def __init__(self, loss_type='iou', reduction='mean'):
        """
        初始化IoU损失

        Args:
            loss_type: 损失类型 ('iou', 'giou', 'diou', 'ciou')
            reduction: 损失聚合方式 ('mean', 'sum', 'none')
        """
        super(IoULoss, self).__init__()
        self.loss_type = loss_type
        self.reduction = reduction

    def forward(self, pred_boxes: torch.Tensor, target_boxes: torch.Tensor) -> torch.Tensor:
        """
        计算IoU损失

        Args:
            pred_boxes: 预测边界框 [N, 4] (x1, y1, x2, y2)
            target_boxes: 真实边界框 [N, 4] (x1, y1, x2, y2)

        Returns:
            IoU损失值
        """
        if pred_boxes.shape[0] == 0:
            return torch.tensor(0.0, device=pred_boxes.device, requires_grad=True)

        # 计算IoU
        iou = self._calculate_iou(pred_boxes, target_boxes)

        if self.loss_type == 'iou':
            loss = 1 - iou
        elif self.loss_type == 'giou':
            loss = 1 - self._calculate_giou(pred_boxes, target_boxes, iou)
        else:
            loss = 1 - iou

        if self.reduction == 'mean':
            return loss.mean()
        elif self.reduction == 'sum':
            return loss.sum()
        else:
            return loss

    def _calculate_iou(self, boxes1: torch.Tensor, boxes2: torch.Tensor) -> torch.Tensor:
        """计算IoU"""
        # 计算交集
        x1 = torch.max(boxes1[:, 0], boxes2[:, 0])
        y1 = torch.max(boxes1[:, 1], boxes2[:, 1])
        x2 = torch.min(boxes1[:, 2], boxes2[:, 2])
        y2 = torch.min(boxes1[:, 3], boxes2[:, 3])

        intersection = torch.clamp(x2 - x1, min=0) * torch.clamp(y2 - y1, min=0)

        # 计算并集
        area1 = (boxes1[:, 2] - boxes1[:, 0]) * (boxes1[:, 3] - boxes1[:, 1])
        area2 = (boxes2[:, 2] - boxes2[:, 0]) * (boxes2[:, 3] - boxes2[:, 1])
        union = area1 + area2 - intersection

        # 避免除零
        iou = intersection / (union + 1e-7)
        return iou

    def _calculate_giou(self, boxes1: torch.Tensor, boxes2: torch.Tensor, iou: torch.Tensor) -> torch.Tensor:
        """计算GIoU"""
        # 计算最小外接矩形
        x1_min = torch.min(boxes1[:, 0], boxes2[:, 0])
        y1_min = torch.min(boxes1[:, 1], boxes2[:, 1])
        x2_max = torch.max(boxes1[:, 2], boxes2[:, 2])
        y2_max = torch.max(boxes1[:, 3], boxes2[:, 3])

        # 外接矩形面积
        enclosing_area = (x2_max - x1_min) * (y2_max - y1_min)

        # 并集面积
        area1 = (boxes1[:, 2] - boxes1[:, 0]) * (boxes1[:, 3] - boxes1[:, 1])
        area2 = (boxes2[:, 2] - boxes2[:, 0]) * (boxes2[:, 3] - boxes2[:, 1])
        union = area1 + area2 - iou * area1  # 这里需要重新计算intersection

        # GIoU
        giou = iou - (enclosing_area - union) / (enclosing_area + 1e-7)
        return giou


class CustomTrainer(Trainer):
    """自定义训练器，集成IoU损失"""

    def __init__(self, iou_loss_weight=0.5, **kwargs):
        super().__init__(**kwargs)
        self.iou_loss_weight = iou_loss_weight
        self.iou_loss_fn = IoULoss(loss_type='giou')

    def compute_loss(self, model, inputs, return_outputs=False):
        # 标准的语言模型损失
        outputs = model(**inputs)
        lm_loss = outputs.loss if hasattr(outputs, 'loss') else outputs.get('loss')

        # 提取边界框损失
        iou_loss = self._compute_bbox_loss(model, inputs, outputs)

        # 总损失
        total_loss = lm_loss + self.iou_loss_weight * iou_loss

        return (total_loss, outputs) if return_outputs else total_loss

    def _compute_bbox_loss(self, model, inputs, outputs):
        """计算边界框IoU损失"""
        try:
            # 从模型输出中提取预测的边界框
            pred_boxes = self._extract_predicted_boxes(outputs, inputs)
            target_boxes = self._extract_target_boxes(inputs)

            if len(pred_boxes) == 0 or len(target_boxes) == 0:
                return torch.tensor(0.0, device=model.device, requires_grad=True)

            # 确保形状匹配
            min_len = min(len(pred_boxes), len(target_boxes))
            if min_len == 0:
                return torch.tensor(0.0, device=model.device, requires_grad=True)

            pred_boxes = pred_boxes[:min_len]
            target_boxes = target_boxes[:min_len]

            # 计算IoU损失
            iou_loss = self.iou_loss_fn(pred_boxes, target_boxes)
            return iou_loss

        except Exception as e:
            # 如果边界框提取失败，返回零损失
            return torch.tensor(0.0, device=model.device, requires_grad=True)

    def _extract_predicted_boxes(self, outputs, inputs):
        """从模型输出中提取预测的边界框"""
        try:
            # 获取生成的token
            if hasattr(outputs, 'logits'):
                logits = outputs.logits
                predicted_ids = torch.argmax(logits, dim=-1)

                # 解码为文本
                tokenizer = self.tokenizer
                predicted_texts = tokenizer.batch_decode(predicted_ids, skip_special_tokens=True)

                # 从文本中提取边界框
                boxes = []
                for text in predicted_texts:
                    bbox = self._parse_bbox_from_text(text)
                    if bbox is not None:
                        boxes.append(bbox)

                if boxes:
                    return torch.tensor(boxes, device=outputs.logits.device, dtype=torch.float32)

            return torch.empty((0, 4), device=outputs.logits.device if hasattr(outputs, 'logits') else inputs['input_ids'].device)

        except Exception:
            device = outputs.logits.device if hasattr(outputs, 'logits') else inputs['input_ids'].device
            return torch.empty((0, 4), device=device)

    def _extract_target_boxes(self, inputs):
        """从输入标签中提取目标边界框"""
        try:
            # 从labels中解码目标边界框
            if 'labels' in inputs:
                labels = inputs['labels']
                tokenizer = self.tokenizer

                # 过滤掉-100的token
                valid_labels = []
                for label_seq in labels:
                    valid_tokens = label_seq[label_seq != -100]
                    if len(valid_tokens) > 0:
                        valid_labels.append(valid_tokens)

                if not valid_labels:
                    return torch.empty((0, 4), device=labels.device)

                # 解码为文本
                target_texts = tokenizer.batch_decode(valid_labels, skip_special_tokens=True)

                # 从文本中提取边界框
                boxes = []
                for text in target_texts:
                    bbox = self._parse_bbox_from_text(text)
                    if bbox is not None:
                        boxes.append(bbox)

                if boxes:
                    return torch.tensor(boxes, device=labels.device, dtype=torch.float32)

            return torch.empty((0, 4), device=inputs['input_ids'].device)

        except Exception:
            return torch.empty((0, 4), device=inputs['input_ids'].device)

    def _parse_bbox_from_text(self, text: str) -> List[float]:
        """从文本中解析边界框坐标"""
        try:
            # 查找bbox_2d格式
            bbox_pattern = r'"bbox_2d":\s*\[([^\]]+)\]'
            match = re.search(bbox_pattern, text)

            if match:
                coords_str = match.group(1)
                coords = [float(x.strip()) for x in coords_str.split(',')]
                if len(coords) == 4:
                    return coords

            # 查找其他可能的边界框格式
            number_pattern = r'\[([0-9.,\s]+)\]'
            matches = re.findall(number_pattern, text)

            for match in matches:
                coords = [float(x.strip()) for x in match.split(',') if x.strip()]
                if len(coords) == 4:
                    return coords

            return None

        except Exception:
            return None


class InfraredLoRATrainer:
    """红外视频检测LoRA微调器"""

    def __init__(self,
                 model_path: str = "/home/<USER>/Qwen/Qwen2.5-VL/Qwen2.5-VL-7B-Instruct",
                 data_path: str = "/home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/data",
                 output_path: str = "/home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/output/lora_checkpoints",
                 gpu_id: int = 2):
        """
        初始化LoRA微调器

        Args:
            model_path: 基础模型路径
            data_path: 数据路径
            output_path: LoRA输出路径
            gpu_id: GPU设备ID
        """
        self.model_path = model_path
        self.data_path = data_path
        self.output_path = output_path
        self.gpu_id = gpu_id

        self.logger = self._setup_logger()
        self._setup_environment()

        # 初始化模型和处理器
        self.model = None
        self.processor = None
        self.lora_model = None
    
    def _setup_logger(self):
        """设置日志记录器"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        return logging.getLogger(__name__)
    
    def _setup_environment(self):
        """设置环境变量"""
        os.environ["CUDA_VISIBLE_DEVICES"] = str(self.gpu_id)
        os.environ["TOKENIZERS_PARALLELISM"] = "false"
        
        # 创建输出目录
        Path(self.output_path).mkdir(parents=True, exist_ok=True)
    
    def prepare_lora_config(self) -> LoraConfig:
        """
        准备LoRA配置 - 参考官方目标检测项目

        Returns:
            LoRA配置
        """
        lora_config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            target_modules=[
                "q_proj", "k_proj", "v_proj", "o_proj",  # attention层
                "gate_proj", "up_proj", "down_proj"      # MLP层
            ],
            inference_mode=False,  # 训练模式
            r=64,                  # LoRA秩，参考官方项目
            lora_alpha=16,         # LoRA alpha参数
            lora_dropout=0.05,     # Dropout比例
            bias="none",           # 不训练bias
        )

        self.logger.info(f"LoRA配置: r={lora_config.r}, alpha={lora_config.lora_alpha}")
        return lora_config

    def load_model_and_processor(self):
        """加载模型和处理器"""
        try:
            self.logger.info(f"加载基础模型: {self.model_path}")

            # 加载模型
            self.model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
                self.model_path,
                torch_dtype=torch.bfloat16,
                device_map={"": f"cuda:{self.gpu_id}"},
                trust_remote_code=True,
                attn_implementation="flash_attention_2"
            )

            # 加载处理器
            self.processor = AutoProcessor.from_pretrained(
                self.model_path,
                trust_remote_code=True,
                min_pixels=256*28*28,
                max_pixels=1280*28*28
            )

            # 应用LoRA配置
            lora_config = self.prepare_lora_config()
            self.lora_model = get_peft_model(self.model, lora_config)
            self.lora_model.config.use_cache = False

            # 打印可训练参数
            self.lora_model.print_trainable_parameters()

            self.logger.info("✅ 模型和LoRA配置加载成功")

        except Exception as e:
            self.logger.error(f"❌ 模型加载失败: {str(e)}")
            raise
    
    def load_dataset(self):
        """加载和处理数据集"""
        try:
            # 加载训练数据
            train_file = f"{self.data_path}/infrared_video_train.json"
            if not os.path.exists(train_file):
                raise FileNotFoundError(f"训练数据文件不存在: {train_file}")

            with open(train_file, 'r', encoding='utf-8') as f:
                train_data = json.load(f)

            # 转换为Dataset格式
            train_dataset = Dataset.from_list(train_data)

            self.logger.info(f"✅ 加载训练数据: {len(train_data)} 条")
            return train_dataset

        except Exception as e:
            self.logger.error(f"❌ 数据加载失败: {str(e)}")
            raise

    def create_data_collator(self):
        """创建数据整理器"""
        def collate_fn(batch):
            """自定义数据整理函数"""
            texts = []

            for item in batch:
                conversations = item['conversations']

                # 构建对话文本
                user_text = conversations[0]['value'].replace('<video>\n', '')
                assistant_text = conversations[1]['value']

                # 简化为纯文本训练（避免视频处理复杂性）
                full_text = f"User: {user_text}\nAssistant: {assistant_text}"
                texts.append(full_text)

            # 使用tokenizer处理文本
            model_inputs = self.processor.tokenizer(
                texts,
                truncation=True,
                padding=True,
                max_length=1024,
                return_tensors="pt"
            )

            # 设置labels
            model_inputs["labels"] = model_inputs["input_ids"].clone()

            return model_inputs

        return collate_fn
    
    def start_lora_training(self):
        """开始LoRA微调训练"""
        self.logger.info("🚀 开始红外视频目标检测LoRA微调训练")
        self.logger.info(f"📁 模型路径: {self.model_path}")
        self.logger.info(f"📁 数据路径: {self.data_path}")
        self.logger.info(f"📁 输出路径: {self.output_path}")
        self.logger.info(f"🎯 使用GPU: {self.gpu_id}")

        try:
            # 加载模型和处理器
            self.load_model_and_processor()

            # 加载数据集
            train_dataset = self.load_dataset()

            # 配置训练参数
            training_args = TrainingArguments(
                output_dir=self.output_path,
                per_device_train_batch_size=1,
                gradient_accumulation_steps=8,
                num_train_epochs=2,
                learning_rate=1e-4,
                weight_decay=0.01,
                warmup_ratio=0.03,
                logging_steps=10,
                save_steps=100,
                save_strategy="steps",
                save_total_limit=2,
                remove_unused_columns=False,
                dataloader_pin_memory=False,
                dataloader_num_workers=0,
                bf16=True,
                tf32=True,
                gradient_checkpointing=True,
                max_grad_norm=1.0,
                report_to="none",
                seed=42,
            )

            # 数据整理器
            data_collator = self.create_data_collator()

            # 创建自定义训练器（包含IoU损失）
            trainer = CustomTrainer(
                model=self.lora_model,
                args=training_args,
                train_dataset=train_dataset,
                data_collator=data_collator,
                tokenizer=self.processor.tokenizer,
            )

            self.logger.info("🎯 开始LoRA训练...")

            # 开始训练
            trainer.train()

            # 保存LoRA权重
            trainer.save_model()
            self.processor.save_pretrained(self.output_path)

            self.logger.info("🎉 LoRA微调训练完成!")
            self.logger.info(f"📁 LoRA权重保存在: {self.output_path}")

        except Exception as e:
            self.logger.error(f"❌ LoRA训练失败: {str(e)}")
            raise

    def validate_lora_checkpoint(self) -> bool:
        """验证LoRA检查点是否正确保存"""
        try:
            adapter_config = Path(self.output_path) / "adapter_config.json"
            adapter_model = Path(self.output_path) / "adapter_model.safetensors"

            if adapter_config.exists() and adapter_model.exists():
                self.logger.info("✅ LoRA检查点验证通过")
                return True
            else:
                self.logger.warning("⚠️ LoRA检查点验证失败")
                return False

        except Exception as e:
            self.logger.error(f"❌ LoRA检查点验证出错: {str(e)}")
            return False


def main():
    """主函数"""
    # 创建LoRA微调器
    trainer = InfraredLoRATrainer(
        model_path="/home/<USER>/Qwen/Qwen2.5-VL/Qwen2.5-VL-7B-Instruct",
        data_path="/home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/data",
        output_path="/home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/output/lora_checkpoints",
        gpu_id=2
    )

    # 开始LoRA训练
    trainer.start_lora_training()

    # 验证LoRA检查点
    if trainer.validate_lora_checkpoint():
        print("🎉 LoRA微调训练成功完成!")
    else:
        print("⚠️ LoRA训练完成但检查点可能有问题")


if __name__ == "__main__":
    main()
