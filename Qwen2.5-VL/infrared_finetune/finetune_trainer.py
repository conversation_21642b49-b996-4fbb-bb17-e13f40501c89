"""
Qwen2.5-VL红外视频目标检测LoRA微调训练器
参考官方目标检测项目，使用LoRA进行参数高效微调
"""
import os
import sys
import torch
import logging
import json
from pathlib import Path
from typing import Dict, Any

# 导入必要的库
from transformers import (
    Qwen2_5_VLForConditionalGeneration,
    AutoProcessor,
    TrainingArguments,
    Trainer,
    DataCollatorForSeq2Seq
)
from peft import LoraConfig, TaskType, get_peft_model
from datasets import Dataset
from qwen_vl_utils import process_vision_info

class InfraredLoRATrainer:
    """红外视频检测LoRA微调器"""

    def __init__(self,
                 model_path: str = "/home/<USER>/Qwen/Qwen2.5-VL/Qwen2.5-VL-7B-Instruct",
                 data_path: str = "/home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/data",
                 output_path: str = "/home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/output/lora_checkpoints",
                 gpu_id: int = 2):
        """
        初始化LoRA微调器

        Args:
            model_path: 基础模型路径
            data_path: 数据路径
            output_path: LoRA输出路径
            gpu_id: GPU设备ID
        """
        self.model_path = model_path
        self.data_path = data_path
        self.output_path = output_path
        self.gpu_id = gpu_id

        self.logger = self._setup_logger()
        self._setup_environment()

        # 初始化模型和处理器
        self.model = None
        self.processor = None
        self.lora_model = None
    
    def _setup_logger(self):
        """设置日志记录器"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        return logging.getLogger(__name__)
    
    def _setup_environment(self):
        """设置环境变量"""
        os.environ["CUDA_VISIBLE_DEVICES"] = str(self.gpu_id)
        os.environ["TOKENIZERS_PARALLELISM"] = "false"
        
        # 创建输出目录
        Path(self.output_path).mkdir(parents=True, exist_ok=True)
    
    def prepare_lora_config(self) -> LoraConfig:
        """
        准备LoRA配置 - 参考官方目标检测项目

        Returns:
            LoRA配置
        """
        lora_config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            target_modules=[
                "q_proj", "k_proj", "v_proj", "o_proj",  # attention层
                "gate_proj", "up_proj", "down_proj"      # MLP层
            ],
            inference_mode=False,  # 训练模式
            r=64,                  # LoRA秩，参考官方项目
            lora_alpha=16,         # LoRA alpha参数
            lora_dropout=0.05,     # Dropout比例
            bias="none",           # 不训练bias
        )

        self.logger.info(f"LoRA配置: r={lora_config.r}, alpha={lora_config.lora_alpha}")
        return lora_config

    def load_model_and_processor(self):
        """加载模型和处理器"""
        try:
            self.logger.info(f"加载基础模型: {self.model_path}")

            # 加载模型
            self.model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
                self.model_path,
                torch_dtype=torch.bfloat16,
                device_map={"": f"cuda:{self.gpu_id}"},
                trust_remote_code=True,
                attn_implementation="flash_attention_2"
            )

            # 加载处理器
            self.processor = AutoProcessor.from_pretrained(
                self.model_path,
                trust_remote_code=True,
                min_pixels=256*28*28,
                max_pixels=1280*28*28
            )

            # 应用LoRA配置
            lora_config = self.prepare_lora_config()
            self.lora_model = get_peft_model(self.model, lora_config)
            self.lora_model.config.use_cache = False

            # 打印可训练参数
            self.lora_model.print_trainable_parameters()

            self.logger.info("✅ 模型和LoRA配置加载成功")

        except Exception as e:
            self.logger.error(f"❌ 模型加载失败: {str(e)}")
            raise
    
    def load_dataset(self):
        """加载和处理数据集"""
        try:
            # 加载训练数据
            train_file = f"{self.data_path}/infrared_video_train.json"
            if not os.path.exists(train_file):
                raise FileNotFoundError(f"训练数据文件不存在: {train_file}")

            with open(train_file, 'r', encoding='utf-8') as f:
                train_data = json.load(f)

            # 转换为Dataset格式
            train_dataset = Dataset.from_list(train_data)

            self.logger.info(f"✅ 加载训练数据: {len(train_data)} 条")
            return train_dataset

        except Exception as e:
            self.logger.error(f"❌ 数据加载失败: {str(e)}")
            raise

    def process_batch(self, examples):
        """批处理数据"""
        batch_messages = []

        for i in range(len(examples['conversations'])):
            # 构建消息格式
            conversations = examples['conversations'][i]
            video_path = examples['video'][i]

            # 构建完整的视频路径
            full_video_path = os.path.join(os.path.dirname(self.data_path), video_path)

            messages = [
                {
                    "role": "user",
                    "content": [
                        {"type": "video", "video": full_video_path},
                        {"type": "text", "text": conversations[0]['value'].replace('<video>\n', '')}
                    ]
                },
                {
                    "role": "assistant",
                    "content": conversations[1]['value']
                }
            ]

            batch_messages.append(messages)

        # 处理消息
        texts = []
        videos = []

        for messages in batch_messages:
            # 应用chat template
            text = self.processor.apply_chat_template(
                messages, tokenize=False, add_generation_prompt=True
            )
            texts.append(text)

            # 提取视频信息
            image_inputs, video_inputs = process_vision_info(messages)
            videos.extend(video_inputs)

        # 批量处理
        inputs = self.processor(
            text=texts,
            videos=videos,
            padding=True,
            return_tensors="pt"
        )

        return inputs
    
    def start_lora_training(self):
        """开始LoRA微调训练"""
        self.logger.info("🚀 开始红外视频目标检测LoRA微调训练")
        self.logger.info(f"📁 模型路径: {self.model_path}")
        self.logger.info(f"📁 数据路径: {self.data_path}")
        self.logger.info(f"📁 输出路径: {self.output_path}")
        self.logger.info(f"🎯 使用GPU: {self.gpu_id}")

        try:
            # 加载模型和处理器
            self.load_model_and_processor()

            # 加载数据集
            train_dataset = self.load_dataset()

            # 配置训练参数
            training_args = TrainingArguments(
                output_dir=self.output_path,
                per_device_train_batch_size=1,
                gradient_accumulation_steps=8,
                num_train_epochs=2,
                learning_rate=1e-4,
                weight_decay=0.01,
                warmup_ratio=0.03,
                logging_steps=10,
                save_steps=100,
                save_strategy="steps",
                save_total_limit=2,
                remove_unused_columns=False,
                dataloader_pin_memory=False,
                dataloader_num_workers=0,
                bf16=True,
                tf32=True,
                gradient_checkpointing=True,
                max_grad_norm=1.0,
                report_to="none",
                seed=42,
            )

            # 数据整理器
            data_collator = DataCollatorForSeq2Seq(
                tokenizer=self.processor.tokenizer,
                model=self.lora_model,
                padding=True
            )

            # 创建训练器
            trainer = Trainer(
                model=self.lora_model,
                args=training_args,
                train_dataset=train_dataset,
                data_collator=data_collator,
                tokenizer=self.processor.tokenizer,
            )

            self.logger.info("🎯 开始LoRA训练...")

            # 开始训练
            trainer.train()

            # 保存LoRA权重
            trainer.save_model()
            self.processor.save_pretrained(self.output_path)

            self.logger.info("🎉 LoRA微调训练完成!")
            self.logger.info(f"📁 LoRA权重保存在: {self.output_path}")

        except Exception as e:
            self.logger.error(f"❌ LoRA训练失败: {str(e)}")
            raise

    def validate_lora_checkpoint(self) -> bool:
        """验证LoRA检查点是否正确保存"""
        try:
            adapter_config = Path(self.output_path) / "adapter_config.json"
            adapter_model = Path(self.output_path) / "adapter_model.safetensors"

            if adapter_config.exists() and adapter_model.exists():
                self.logger.info("✅ LoRA检查点验证通过")
                return True
            else:
                self.logger.warning("⚠️ LoRA检查点验证失败")
                return False

        except Exception as e:
            self.logger.error(f"❌ LoRA检查点验证出错: {str(e)}")
            return False


def main():
    """主函数"""
    # 创建LoRA微调器
    trainer = InfraredLoRATrainer(
        model_path="/home/<USER>/Qwen/Qwen2.5-VL/Qwen2.5-VL-7B-Instruct",
        data_path="/home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/data",
        output_path="/home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/output/lora_checkpoints",
        gpu_id=2
    )

    # 开始LoRA训练
    trainer.start_lora_training()

    # 验证LoRA检查点
    if trainer.validate_lora_checkpoint():
        print("🎉 LoRA微调训练成功完成!")
    else:
        print("⚠️ LoRA训练完成但检查点可能有问题")


if __name__ == "__main__":
    main()
