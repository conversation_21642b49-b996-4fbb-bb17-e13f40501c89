# 红外图像微小目标检测微调项目

基于Qwen2.5-VL模型的红外图像微小目标检测微调项目，使用独立的训练框架。

## 项目概述

本项目使用Qwen2.5-VL多模态大语言模型对红外图像中的微小目标（如无人机）进行检测。通过LoRA（Low-Rank Adaptation）技术对预训练模型进行微调，以适应特定的红外图像目标检测任务。

### 主要特性

- **独立训练框架**: 不依赖LLaMA-Factory，使用transformers和peft直接训练
- **视频序列训练**: 采用视频序列训练方式，利用时序信息提升检测精度
- **LoRA微调**: 使用低秩适应技术，高效微调大模型
- **自动化流程**: 从数据预处理到模型评估的完整自动化流程

## 项目结构

```
infrared_finetune/
├── README.md                 # 项目说明文档
├── main.py                   # 主程序入口
├── train.py                  # 独立训练脚本
├── evaluate.py               # 模型评估脚本
├── data_preprocessor.py      # 数据预处理模块
├── requirements.txt          # 依赖包列表
├── data/                     # 训练数据目录
│   ├── videos/              # 生成的视频文件
│   ├── infrared_detection_train.json  # 训练数据
│   ├── infrared_detection_test.json   # 测试数据
│   └── data_statistics.json # 数据统计信息
└── output/                   # 输出目录
    ├── checkpoints/         # 模型检查点
    └── evaluation_results.json # 评估结果
```

## 环境要求

- Python 3.8+
- CUDA 11.8+
- 显存 >= 24GB (推荐)

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 快速开始

运行完整的微调流程：

```bash
cd /home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune
python main.py
```

### 2. 分步执行

#### 仅数据预处理
```bash
python main.py --preprocess-only
```

#### 仅训练
```bash
python main.py --train-only
```

#### 仅评估
```bash
python main.py --eval-only
```

#### 强制重新生成数据
```bash
python main.py --force-regenerate
```

### 3. 直接运行训练

```bash
python train.py
```

### 4. 直接运行评估

```bash
python evaluate.py --lora_path ./output/checkpoints
```

## 数据格式

### 输入数据
- 原始数据：YOLO格式的图像和标注文件
- 处理后：视频序列 + JSON格式的对话数据

### 训练数据格式
```json
{
  "messages": [
    {
      "role": "user",
      "content": "请分析这个红外视频序列中的目标检测结果..."
    },
    {
      "role": "assistant", 
      "content": "{\"25\":[{\"bbox\":[63,106,68,111],\"class_name\":\"drone\",\"confidence\":1.0}]}"
    }
  ],
  "videos": ["/path/to/video.mp4"]
}
```

## 模型配置

### LoRA参数
- **rank**: 64
- **alpha**: 16
- **dropout**: 0.05
- **target_modules**: q_proj, k_proj, v_proj, o_proj, gate_proj, up_proj, down_proj

### 训练参数
- **学习率**: 5e-5
- **训练轮数**: 2
- **批次大小**: 1
- **梯度累积**: 8步
- **调度器**: cosine
- **预热比例**: 0.1

## 数据统计

- **处理序列**: 8个 (data01, data02, data04, data05, data06, data07, data23, data25, data26)
- **总视频数**: 305个
- **训练样本**: 244个
- **测试样本**: 61个
- **总标注数**: 1424个
- **目标类别**: drone

## 评估指标

- **准确率**: 预测正确的样本比例
- **召回率**: 成功检测到目标的比例
- **时序一致性**: 连续帧间检测结果的一致性

## 输出文件

训练完成后，将生成以下文件：

- `output/checkpoints/adapter_model.safetensors`: LoRA适配器权重
- `output/checkpoints/adapter_config.json`: LoRA配置文件
- `output/evaluation_results.json`: 详细评估结果
- `training.log`: 训练日志

## 故障排除

### 常见问题

1. **CUDA内存不足**
   - 减少批次大小
   - 增加梯度累积步数
   - 使用更小的LoRA rank

2. **数据加载失败**
   - 检查视频文件路径是否正确
   - 确认数据格式是否符合要求

3. **训练速度慢**
   - 启用Flash Attention 2
   - 使用bf16混合精度训练

## 许可证

本项目遵循MIT许可证。
