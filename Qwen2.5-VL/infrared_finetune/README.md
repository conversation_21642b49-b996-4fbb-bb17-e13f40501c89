# 红外图像目标检测微调项目

基于Qwen2.5-VL的红外图像目标检测微调项目，使用官方qwen-vl-finetune框架。

## 🎯 项目概述

本项目实现了基于Qwen2.5-VL-7B-Instruct模型的红外视频目标检测微调，支持高频弱目标检测和时序分析。

## 📁 项目结构

```
infrared_finetune/
├── README.md                           # 项目说明文档
├── requirements.txt                    # Python依赖包
├── train_infrared.sh                   # 训练启动脚本
├── main.py                            # 主程序入口
├── data_preprocessor.py               # 数据预处理工具
├── evaluate.py                        # 模型评估脚本
├── data/                              # 数据目录
│   ├── videos/                        # 红外视频文件 (244个视频)
│   ├── infrared_video_train.json      # 训练数据标注 (官方格式)
│   ├── infrared_video_test.json       # 测试数据标注 (官方格式)
│   └── data_statistics.json           # 数据统计信息
├── qwenvl/                            # 官方qwen-vl-finetune框架
│   ├── data/                          # 数据处理模块
│   └── train/                         # 训练模块
└── output/                            # 输出目录
    └── official_checkpoints/          # 训练完成的模型检查点
```

## 🚀 使用方法

### 1. 环境准备
```bash
# 激活conda环境
conda activate Qwen

# 安装依赖
pip install -r requirements.txt
```

### 2. 数据预处理
```bash
python data_preprocessor.py
```

### 3. 开始训练
```bash
./train_infrared.sh
```

### 4. 模型评估
```bash
python evaluate.py
```

## 📊 训练配置

- **模型**: Qwen2.5-VL-7B-Instruct
- **数据**: 244个红外视频序列，符合官方video.json格式
- **训练策略**: 冻结视觉编码器，训练MLP和LLM层
- **GPU**: 支持单GPU训练 (使用GPU 2)
- **批次大小**: 1，梯度累积8步
- **学习率**: 1e-5
- **训练轮数**: 1个epoch

## ✅ 训练状态

✅ **训练已完成！** 模型已保存在 `output/official_checkpoints/`

## 🎯 评估指标

- 目标识别准确率 (召回率/误报率)
- 时序一致性 (IoU>0.3，80%帧)
- 高频弱目标检测性能

## 📝 技术特点

- **官方框架**: 使用qwen-vl-finetune官方代码，确保兼容性
- **视频格式**: 支持官方video.json数据格式
- **环境兼容**: 解决了所有依赖冲突，支持PyTorch 2.4.0
- **GPU优化**: 智能GPU选择，避免内存冲突
- **数据标准**: 完全符合官方数据格式要求

## 🔧 环境依赖

- Python 3.10
- PyTorch 2.4.0
- transformers
- qwen-vl-utils
- decord
- torchcodec==0.1.0
- flash-attn
- accelerate

## 📈 项目成果

- ✅ 成功解决所有环境依赖冲突
- ✅ 实现官方框架完整集成
- ✅ 完成244个视频的数据预处理
- ✅ 训练完成并保存模型检查点
- ✅ 项目文件结构清理完成