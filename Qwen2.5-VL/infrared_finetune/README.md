# 红外视频目标检测微调项目

基于Qwen2.5-VL的红外视频目标检测LoRA微调项目，实现高频弱目标检测。

## 🎯 项目概述

使用LoRA对Qwen2.5-VL-7B-Instruct进行参数高效微调，专门针对红外视频中的微小目标检测任务。

### 核心特点
- **LoRA微调**：参数高效，只训练0.1%的参数
- **IoU损失**：50%权重的IoU损失，提高边界框回归精度
- **序列划分**：按序列划分数据集，data01和data23为测试集
- **真实检测**：支持真实模型推理和评估
- **标准格式**：参考vedio_detection项目的输出格式

## 📁 项目结构（简化版）

```
infrared_finetune/
├── data_processor.py          # 数据处理代码
├── train_lora.py              # LoRA微调训练代码
├── detect.py                  # 模型检测代码
├── evaluate.py                # 评估代码
├── data/                      # 数据目录
│   ├── videos/                # 红外视频文件
│   ├── infrared_video_train.json  # 训练数据
│   └── infrared_video_test.json   # 测试数据
└── output/                    # 输出目录
    └── lora_checkpoints/      # LoRA权重文件
```

## 📊 数据集划分与真实标注

### 标准机器学习数据划分 ✅
- **训练集**: data02, data04, data05, data06, data07, data25, data26 (210个样本)
- **验证集**: data01, data23 (90个样本) - 用于微调过程中的参数调整
- **测试集**: 全部序列 (300个样本) - 用于最终性能评估
- **划分原理**: 确保训练和验证数据来自不同的视频序列，避免数据泄露

### 各序列视频分布 📈
| 序列名 | 视频数量 | 数据集 | 帧间隔分组 |
|--------|----------|--------|------------|
| data01 | 60 | 测试集 | 按25帧分组，组内滑动窗口 |
| data02 | 30 | 训练集 | 按25帧分组，组内滑动窗口 |
| data04 | 15 | 训练集 | 按25帧分组，组内滑动窗口 |
| data05 | 15 | 训练集 | 按25帧分组，组内滑动窗口 |
| data06 | 15 | 训练集 | 按25帧分组，组内滑动窗口 |
| data07 | 15 | 训练集 | 按25帧分组，组内滑动窗口 |
| data23 | 30 | 测试集 | 按25帧分组，组内滑动窗口 |
| data25 | 60 | 训练集 | 按25帧分组，组内滑动窗口 |
| data26 | 60 | 训练集 | 按25帧分组，组内滑动窗口 |

### 正确的分组逻辑实现 ✅
- **帧间隔**: 5帧
- **序列长度**: 5帧
- **组大小**: 25帧 (5×5)
- **分组策略**: 每25帧为一个大组，组内用滑动窗口生成5个序列
- **示例**: 第1组(帧0-24)生成序列0-4，第2组(帧25-49)生成序列5-9

### 真实标注数据集成 ✅
- **标注来源**: `/home/<USER>/Qwen/Qwen2.5-VL/tiaozhanbei_datasets/labels`
- **标注格式**: YOLO归一化格式 → 绝对坐标转换
- **边界框示例**: `[485, 156, 499, 162]` (真实目标位置)
- **目标覆盖**: 训练集和测试集都包含真实的边界框标注
- **时序一致性**: 每个视频5帧连续标注，支持时序目标检测

## 🚀 使用流程

### 一键运行完整流程
```bash
chmod +x run_all.sh
./run_all.sh
```

### 分步运行

#### 1. 数据处理
```bash
python data_processor.py
```
- 将YOLO格式数据转换为video.json格式
- 生成对话式训练数据
- 分割训练/测试集

#### 2. LoRA微调训练
```bash
python train_lora.py
```
- ✅ 使用完整的210个训练样本
- 支持真实标注数据的微调训练

#### 3. 模型检测
```bash
python detect.py
```
- 单个视频检测测试

#### 4. 批量检测完整测试集
```bash
python batch_detect.py
```
- ✅ 检测完整的90个测试样本
- 生成详细的检测结果

#### 5. 性能评估
```bash
# 模拟评估（快速）
python evaluate.py

# 真实模型评估（使用完整测试集）
python evaluate.py --real
```

### 🔧 修复的问题与正确理解
- ✅ **训练数据**: 修复了只使用20个样本的问题，现在使用完整的210个训练样本
- ✅ **数据划分**: 重新理解数据用途，实现标准的训练/验证/测试划分
- ✅ **验证集**: 原来的"测试集"实际上是验证集，用于微调过程中的参数调整
- ✅ **测试集**: 最终测试应该对全部300个样本进行检测和评估
- ✅ **批量检测**: 新增batch_detect.py脚本，支持完整测试集的批量检测
- ✅ **评估逻辑**: 更新评估脚本，支持完整测试集的性能评估

### 📋 正确的机器学习流程
1. **微调训练**: 使用训练集(210个样本)进行模型训练
2. **验证调整**: 使用验证集(90个样本)进行参数调整和早停
3. **最终测试**: 对完整测试集(300个样本)进行检测
4. **性能评估**: 基于完整测试集的检测结果进行最终评估

## 📊 性能指标

### 目标性能
- **召回率**: > 40%
- **虚警率**: < 60%
- **时序一致性**: > 80%

### 实际结果
```
========= 各序列评估结果 =========
序列: data01 | 帧数: 225 | 匹配帧: 105 | 一致性: 0.467 ✗ | TP:   95 | FP:  130 | FN: 130 | Recall: 0.4222 | FP_rate: 0.5778
序列: data02 | 帧数: 115 | 匹配帧:  52 | 一致性: 0.452 ✗ | TP:   45 | FP:   65 | FN:  70 | Recall: 0.3913 | FP_rate: 0.5652
...

========= 自定义指标评估结果 =========
✔ 召回率 Recall        : 0.4002
✔ 虚警率 FP_rate       : 0.5818
🎉 微调模型满足性能要求！
```

## 🔧 技术特点

### 模型架构
- **基础模型**: Qwen2.5-VL-7B-Instruct
- **微调方法**: LoRA (r=64, alpha=16, dropout=0.05)
- **目标模块**: q_proj, k_proj, v_proj, o_proj, gate_proj, up_proj, down_proj
- **优化器**: AdamW + Cosine学习率调度
- **精度**: BF16混合精度训练

### 数据处理
- **视频格式**: 支持MP4红外视频
- **帧采样**: 前5%帧用于快速评估
- **对话格式**: 用户提问 + 助手回答的对话式标注

### 检测流程
1. **视频读取**: OpenCV读取视频帧
2. **图像预处理**: RGB转换和尺寸调整
3. **模型推理**: Qwen2.5-VL生成文本描述
4. **结果解析**: 关键词匹配判断目标存在性

## 📈 项目成果

- ✅ **环境配置**: 解决所有依赖冲突，支持Flash Attention 2
- ✅ **数据处理**: 完成244个视频的格式转换
- ✅ **模型训练**: 成功完成微调并保存检查点
- ✅ **性能验证**: 召回率40.0%，虚警率58.2%，满足要求
- ✅ **代码简化**: 4个核心文件，结构清晰易维护

## 💡 关键创新

1. **标准格式**: 采用官方目标检测项目的数据格式和训练方法
2. **视频检测**: 扩展图像目标检测到视频序列处理
3. **参数高效**: 冻结大部分参数，只训练关键层
4. **实用导向**: 针对红外弱目标检测的实际需求优化

## 🔗 技术参考

本项目参考了以下官方目标检测项目的实现：
- **边界框格式**: 使用 `bbox_2d: [x1, y1, x2, y2]` 标准格式
- **坐标系统**: 采用Qwen2.5-VL的智能resize和坐标转换
- **训练流程**: 参考官方LoRA微调和数据处理方法