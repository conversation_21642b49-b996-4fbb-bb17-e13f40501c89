# 红外图像微小目标检测微调项目

基于LLaMA-Factory框架对Qwen2.5-VL模型进行微调，用于红外图像微小目标检测任务。

## 项目概述

本项目旨在通过LoRA微调技术改进Qwen2.5-VL模型在红外图像微小目标检测任务上的性能，特别针对指定的8个序列进行优化。

### 主要特性

- **视频序列训练**: 采用视频序列训练方式，利用时序信息提升检测精度
- **YOLO格式支持**: 自动转换YOLO格式标注为Qwen2.5-VL训练格式
- **LoRA微调**: 使用LoRA技术进行高效微调，减少计算资源需求
- **完整流程**: 从数据预处理到模型训练的完整自动化流程

### 目标性能

- 召回率 > 70%
- 虚警率 < 30%
- 时序一致性 > 80%（IoU > 0.3）

## 项目结构

```
infrared_finetune/
├── README.md                 # 项目说明文档
├── main.py                   # 主程序入口
├── data_preprocessor.py      # 数据预处理模块
├── finetune_config.py        # 微调配置模块
├── evaluate_model.py         # 模型评估脚本
├── fix_video_paths.py        # 视频路径修复脚本
├── quick_fix.py              # 快速路径修复脚本
├── train.sh                  # 训练脚本（自动生成）
├── data/                     # 训练数据目录
│   ├── videos/              # 生成的视频文件
│   ├── infrared_detection_train.json  # 训练数据
│   └── data_statistics.json # 数据统计信息
└── output/                   # 输出目录
    ├── checkpoints/         # 模型检查点
    └── logs/               # 训练日志
```

## 环境要求

### 基础环境

- Python 3.8+
- CUDA 11.8+
- PyTorch 2.0+
- transformers >= 4.37.0

### 依赖包

```bash
pip install torch torchvision torchaudio
pip install transformers>=4.37.0
pip install peft
pip install deepspeed
pip install opencv-python
pip install pillow
pip install numpy
pip install qwen-vl-utils
```

### LLaMA-Factory框架

确保LLaMA-Factory框架已正确安装在 `/home/<USER>/Qwen/Qwen2.5-VL/LLaMA-Factory`

## 数据准备

### 数据格式要求

1. **图像数据**: 位于 `tiaozhanbei_datasets/images/` 目录下
2. **标注数据**: 位于 `tiaozhanbei_datasets/labels/` 目录下，YOLO格式
3. **目标序列**: 仅使用指定的8个序列：`['data01', 'data02', 'data04', 'data05', 'data06', 'data07', 'data23', 'data25', 'data26']`

### YOLO标注格式

```
class_id x_center y_center width height
```

其中坐标为归一化坐标（0-1范围）。

## 使用方法

### 1. 快速开始

运行完整的微调流程：

```bash
cd /home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune
python main.py
```

**如果遇到视频路径错误**，请先运行路径修复：

```bash
# 方法1: 使用快速修复脚本
python3 quick_fix.py

# 方法2: 使用完整修复脚本
python3 fix_video_paths.py

# 然后重新运行训练
./train.sh
```

### 2. 分步执行

#### 仅数据预处理

```bash
python main.py --preprocess-only
```

#### 仅环境设置

```bash
python main.py --setup-only
```

#### 跳过训练（仅准备）

```bash
python main.py --skip-training
```

#### 强制重新生成数据

```bash
python main.py --force-regenerate
```

### 3. 手动训练

如果需要手动控制训练过程：

```bash
# 1. 数据预处理
python data_preprocessor.py

# 2. 设置训练环境
python finetune_config.py

# 3. 运行训练
./train.sh
```

### 4. 模型评估

```bash
python evaluate_model.py \
    --base-model /path/to/base/model \
    --lora-path /path/to/lora/weights \
    --output-dir ./evaluation_results
```

## 配置参数

### 数据配置

- `frame_interval`: 帧间隔（默认5）
- `sequence_length`: 序列长度（默认5帧）
- `frame_percentage`: 使用帧百分比（默认0.2，即前20%）
- `fps`: 视频帧率（默认5.0）

### 训练配置

- `lora_rank`: LoRA秩（默认64）
- `lora_alpha`: LoRA alpha（默认16）
- `lora_dropout`: LoRA dropout（默认0.05）
- `learning_rate`: 学习率（默认5e-5）
- `num_train_epochs`: 训练轮数（默认3）
- `per_device_train_batch_size`: 批次大小（默认1）
- `gradient_accumulation_steps`: 梯度累积步数（默认8）

## 输出文件

### 训练数据

- `data/infrared_detection_train.json`: 训练数据文件
- `data/data_statistics.json`: 数据统计信息
- `data/videos/`: 生成的视频文件

### 模型输出

- `output/checkpoints/`: 模型检查点
- `output/logs/`: 训练日志

### 评估结果

- 检测精度报告
- 时序一致性分析
- 类别分布统计

## 故障排除

### 常见问题

1. **视频路径错误** ⚠️ **重要**

   如果遇到 `FileNotFoundError: [Errno 2] No such file or directory: 'videos/data01_seq_xxx.mp4'` 错误，说明训练数据中使用了相对路径。

   **解决方案**：
   ```bash
   # 运行路径修复脚本
   python3 -c "
   import json
   import os

   print('开始修复视频路径...')

   # 修复训练数据路径
   train_file = '/home/<USER>/Qwen/Qwen2.5-VL/LLaMA-Factory/data/infrared_detection_train.json'
   video_base_path = '/home/<USER>/Qwen/Qwen2.5-VL/LLaMA-Factory/data/videos'

   with open(train_file, 'r', encoding='utf-8') as f:
       train_data = json.load(f)

   fixed_count = 0
   for item in train_data:
       if 'videos' in item:
           new_videos = []
           for video_path in item['videos']:
               if video_path.startswith('videos/'):
                   video_name = video_path.replace('videos/', '')
                   absolute_path = os.path.join(video_base_path, video_name)
                   new_videos.append(absolute_path)
                   fixed_count += 1
               else:
                   new_videos.append(video_path)
           item['videos'] = new_videos

   with open(train_file, 'w', encoding='utf-8') as f:
       json.dump(train_data, f, ensure_ascii=False, indent=2)

   print(f'训练数据路径修复完成，修复了 {fixed_count} 个路径')

   # 验证修复结果
   sample_video = train_data[0]['videos'][0]
   exists = os.path.exists(sample_video)
   print(f'示例视频路径: {sample_video}')
   print(f'文件存在: {exists}')
   "
   ```

   然后重新运行训练：
   ```bash
   ./train.sh
   ```

2. **CUDA内存不足**
   - 减少 `per_device_train_batch_size`
   - 增加 `gradient_accumulation_steps`
   - 使用 `fp16` 或 `bf16`

3. **数据加载失败**
   - 检查数据路径是否正确
   - 确认YOLO标注格式是否正确
   - 检查图像文件是否完整

4. **模型加载失败**
   - 确认模型路径是否正确
   - 检查transformers版本是否兼容
   - 确认CUDA环境是否正确配置

### 日志文件

查看详细日志信息：

```bash
tail -f infrared_finetune.log
```

## 性能优化

### 训练加速

1. 使用DeepSpeed进行分布式训练
2. 启用Flash Attention
3. 使用混合精度训练（bf16）
4. 优化数据加载并行度

### 内存优化

1. 使用梯度检查点
2. 启用CPU卸载
3. 优化批次大小和累积步数

## 贡献指南

1. Fork项目
2. 创建特性分支
3. 提交更改
4. 创建Pull Request

## 许可证

本项目遵循MIT许可证。

## 联系方式

如有问题或建议，请通过以下方式联系：

- 项目Issues
- 邮件联系

---

**注意**: 本项目专门针对红外图像微小目标检测任务进行优化，使用前请确保数据格式和环境配置正确。
