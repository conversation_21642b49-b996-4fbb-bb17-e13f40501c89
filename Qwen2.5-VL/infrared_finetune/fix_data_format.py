"""
修复数据格式以匹配官方格式
"""

import json
import os

def fix_conversation_format(data_file):
    """修复对话格式"""
    
    print(f"🔧 修复数据格式: {data_file}")
    
    if not os.path.exists(data_file):
        print(f"❌ 文件不存在: {data_file}")
        return
    
    # 读取数据
    with open(data_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print(f"📊 处理 {len(data)} 个样本...")
    
    # 修复格式
    for sample in data:
        conversations = sample.get('conversations', [])
        for conv in conversations:
            # 修复 from 字段
            if conv.get('from') == 'user':
                conv['from'] = 'human'
            elif conv.get('from') == 'assistant':
                conv['from'] = 'gpt'
    
    # 保存修复后的数据
    backup_file = data_file.replace('.json', '_backup.json')
    if not os.path.exists(backup_file):
        os.rename(data_file, backup_file)
    else:
        print(f"ℹ️  备份文件已存在: {backup_file}")
    
    with open(data_file, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 格式修复完成")
    print(f"📁 原文件备份: {backup_file}")
    print(f"📁 修复后文件: {data_file}")

def main():
    """主函数"""
    print("🚀 修复数据格式以匹配官方格式")
    print("=" * 50)
    
    # 修复所有数据文件
    data_files = [
        'data/infrared_video_train.json',
        'data/infrared_video_val.json', 
        'data/infrared_video_test_full.json'
    ]
    
    for data_file in data_files:
        fix_conversation_format(data_file)
        print()
    
    print("🎉 所有数据格式修复完成!")
    print("现在数据格式与官方格式一致:")
    print('  - "from": "human" (用户输入)')
    print('  - "from": "gpt" (模型输出)')

if __name__ == "__main__":
    main()
