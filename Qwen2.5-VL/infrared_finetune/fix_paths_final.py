#!/usr/bin/env python3
"""
最终路径修复脚本
"""

import json
import os

def fix_paths():
    """修复所有路径"""
    
    # 文件路径
    train_file = "/home/<USER>/Qwen/Qwen2.5-VL/LLaMA-Factory/data/infrared_detection_train.json"
    test_file = "/home/<USER>/Qwen/Qwen2.5-VL/LLaMA-Factory/data/infrared_detection_test.json"
    
    # 视频目录的绝对路径
    video_base_path = "/home/<USER>/Qwen/Qwen2.5-VL/LLaMA-Factory/data/videos"
    
    print("开始修复训练数据路径...")
    
    # 修复训练数据
    with open(train_file, 'r', encoding='utf-8') as f:
        train_data = json.load(f)
    
    fixed_count = 0
    for item in train_data:
        if 'videos' in item:
            new_videos = []
            for video_path in item['videos']:
                if video_path.startswith('videos/'):
                    # 转换为绝对路径
                    video_name = video_path.replace('videos/', '')
                    absolute_path = os.path.join(video_base_path, video_name)
                    new_videos.append(absolute_path)
                    fixed_count += 1
                elif not video_path.startswith('/'):
                    # 处理其他相对路径
                    absolute_path = os.path.join(video_base_path, video_path)
                    new_videos.append(absolute_path)
                    fixed_count += 1
                else:
                    new_videos.append(video_path)
            item['videos'] = new_videos
    
    # 保存修复后的训练数据
    with open(train_file, 'w', encoding='utf-8') as f:
        json.dump(train_data, f, ensure_ascii=False, indent=2)
    
    print(f"训练数据路径修复完成，修复了 {fixed_count} 个路径")
    
    # 修复测试数据
    if os.path.exists(test_file):
        print("开始修复测试数据路径...")
        
        with open(test_file, 'r', encoding='utf-8') as f:
            test_data = json.load(f)
        
        test_fixed_count = 0
        for item in test_data:
            if 'videos' in item:
                new_videos = []
                for video_path in item['videos']:
                    if video_path.startswith('videos/'):
                        # 转换为绝对路径
                        video_name = video_path.replace('videos/', '')
                        absolute_path = os.path.join(video_base_path, video_name)
                        new_videos.append(absolute_path)
                        test_fixed_count += 1
                    elif not video_path.startswith('/'):
                        # 处理其他相对路径
                        absolute_path = os.path.join(video_base_path, video_path)
                        new_videos.append(absolute_path)
                        test_fixed_count += 1
                    else:
                        new_videos.append(video_path)
                item['videos'] = new_videos
        
        # 保存修复后的测试数据
        with open(test_file, 'w', encoding='utf-8') as f:
            json.dump(test_data, f, ensure_ascii=False, indent=2)
        
        print(f"测试数据路径修复完成，修复了 {test_fixed_count} 个路径")
    
    # 验证修复结果
    print("\n验证修复结果...")
    
    # 检查训练数据的前几个样本
    for i, item in enumerate(train_data[:3]):
        if 'videos' in item:
            for video_path in item['videos']:
                exists = os.path.exists(video_path)
                print(f"训练样本 {i+1}: {os.path.basename(video_path)} - 存在: {exists}")
                if not exists:
                    print(f"  完整路径: {video_path}")
    
    print("路径修复完成！")

if __name__ == "__main__":
    fix_paths()
