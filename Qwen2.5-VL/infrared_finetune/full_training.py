#!/usr/bin/env python3
"""
完整训练脚本
使用完整的数据集进行训练
"""

import os
import sys
import logging

# 添加LLaMA-Factory路径
sys.path.insert(0, '/home/<USER>/Qwen/Qwen2.5-VL/LLaMA-Factory/src')

# 设置环境变量
os.environ['CUDA_VISIBLE_DEVICES'] = '0'

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_full_training():
    """运行完整训练"""
    
    try:
        # 切换到LLaMA-Factory目录
        os.chdir('/home/<USER>/Qwen/Qwen2.5-VL/LLaMA-Factory')
        
        logger.info("开始完整训练...")
        
        # 设置训练参数
        sys.argv = [
            'train.py',
            '--model_name_or_path', '/home/<USER>/Qwen/Qwen2.5-VL/Qwen2.5-VL-7B-Instruct',
            '--stage', 'sft',
            '--do_train',
            '--finetuning_type', 'lora',
            '--dataset', 'infrared_detection',
            '--template', 'qwen2_vl',
            '--cutoff_len', '8192',
            '--learning_rate', '5e-05',
            '--num_train_epochs', '2.0',  # 2个epoch
            '--per_device_train_batch_size', '1',
            '--gradient_accumulation_steps', '8',
            '--lr_scheduler_type', 'cosine',
            '--warmup_ratio', '0.1',
            '--bf16',
            '--logging_steps', '10',
            '--save_steps', '50',
            '--output_dir', '/home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/output/checkpoints',
            '--lora_rank', '64',
            '--lora_alpha', '16',
            '--lora_dropout', '0.05',
            '--lora_target', 'q_proj,k_proj,v_proj,o_proj,gate_proj,up_proj,down_proj',
            '--max_new_tokens', '2048',
            '--plot_loss',
            '--overwrite_output_dir'
        ]
        
        from llamafactory.train.tuner import run_exp
        run_exp()
        
        logger.info("完整训练完成！")
        return True
        
    except Exception as e:
        logger.error(f"完整训练失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    logger.info("开始完整训练流程...")
    
    # 运行完整训练
    success = run_full_training()
    
    if success:
        logger.info("训练成功完成！")
        
        # 检查输出目录
        output_dir = "/home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/output/checkpoints"
        if os.path.exists(output_dir):
            files = os.listdir(output_dir)
            logger.info(f"输出文件: {files}")
            
            # 查找最新的检查点
            checkpoints = [f for f in files if f.startswith('checkpoint-')]
            if checkpoints:
                latest_checkpoint = max(checkpoints, key=lambda x: int(x.split('-')[1]))
                logger.info(f"最新检查点: {latest_checkpoint}")
        else:
            logger.warning("输出目录不存在")
    else:
        logger.error("训练失败")

if __name__ == "__main__":
    main()
