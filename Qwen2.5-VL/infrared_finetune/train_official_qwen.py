"""
红外视频LoRA微调 - 完全基于Qwen官方实现
严格按照 /home/<USER>/Qwen/Qwen2.5-VL/qwen-vl-finetune/ 的标准
"""

import os
import json
import logging
import torch
import copy
import numpy as np
import math
import time
from pathlib import Path
from dataclasses import dataclass, field
from typing import Dict, Optional, List
from transformers import (
    AutoProcessor,
    Qwen2_5_VLForConditionalGeneration,
    TrainingArguments,
    Trainer,
    HfArgumentParser
)
from peft import LoraConfig, get_peft_model, TaskType
from torch.utils.data import Dataset
from decord import VideoReader
from PIL import Image

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 官方常量定义
IGNORE_INDEX = -100
IMAGE_TOKEN_INDEX = 151655
VIDEO_TOKEN_INDEX = 151656
DEFAULT_IMAGE_TOKEN = "<image>"
DEFAULT_VIDEO_TOKEN = "<video>"

@dataclass
class ModelArguments:
    model_name_or_path: Optional[str] = field(default="/home/<USER>/Qwen/Qwen2.5-VL/Qwen2.5-VL-7B-Instruct")
    tune_mm_llm: bool = field(default=True)
    tune_mm_mlp: bool = field(default=True)
    tune_mm_vision: bool = field(default=False)  # 官方建议：同时使用图像和视频时设为False

@dataclass
class DataArguments:
    dataset_use: str = field(default="infrared_video")
    video_max_frames: Optional[int] = field(default=8)
    video_min_frames: Optional[int] = field(default=4)
    data_flatten: bool = field(default=True)  # 官方建议
    data_packing: bool = field(default=False)
    base_interval: int = field(default=4)  # 官方默认值
    max_pixels: int = field(default=50176)  # 官方推荐值
    min_pixels: int = field(default=784)    # 官方推荐值
    video_max_frame_pixels: int = field(default=32 * 28 * 28)  # 官方配置
    video_min_frame_pixels: int = field(default=4 * 28 * 28)   # 官方配置

@dataclass
class CustomTrainingArguments(TrainingArguments):
    cache_dir: Optional[str] = field(default=None)
    optim: str = field(default="adamw_torch")
    model_max_length: int = field(default=8192)  # 官方推荐，不允许截断
    mm_projector_lr: Optional[float] = None
    vision_tower_lr: Optional[float] = None

class OfficialQwenVideoDataset(Dataset):
    """完全基于官方实现的视频数据集"""
    
    def __init__(self, tokenizer, data_args):
        super(OfficialQwenVideoDataset, self).__init__()
        self.tokenizer = tokenizer
        self.data_args = data_args
        
        # 加载数据
        train_file = "data/infrared_video_train.json"
        if os.path.exists(train_file):
            with open(train_file, 'r') as f:
                self.list_data_dict = json.load(f)
        else:
            self.list_data_dict = []

        logger.info(f"Total training samples: {len(self.list_data_dict)}")

        # 如果没有数据，创建一些虚拟数据用于测试
        if len(self.list_data_dict) == 0:
            logger.warning("没有找到训练数据，创建虚拟数据用于测试...")
            video_dir = Path("data/videos")
            if video_dir.exists():
                video_files = list(video_dir.glob("*.mp4"))[:10]  # 只取前10个
                for video_file in video_files:
                    self.list_data_dict.append({
                        "video": str(video_file),
                        "conversations": [
                            {
                                "from": "human",
                                "value": "请分析这个红外视频中的目标。"
                            },
                            {
                                "from": "gpt",
                                "value": "我看到了红外视频中的目标物体。"
                            }
                        ]
                    })
            logger.info(f"创建虚拟数据: {len(self.list_data_dict)} 个样本")
        
        # 设置视频处理参数（官方配置）
        self.video_max_total_pixels = getattr(data_args, "video_max_total_pixels", 1664 * 28 * 28)
        self.video_min_total_pixels = getattr(data_args, "video_min_total_pixels", 256 * 28 * 28)
        
    def __len__(self):
        return len(self.list_data_dict)
    
    def process_video_frames(self, video, frame_idx, video_length):
        """官方的视频帧处理函数"""
        fps = len(frame_idx) / video_length
        processor = copy.deepcopy(self.data_args.image_processor)
        processor.max_pixels = self.data_args.video_max_frame_pixels
        processor.min_pixels = self.data_args.video_min_frame_pixels
        
        # 官方的视频处理
        video_processed = processor.preprocess(
            images=None, videos=video, return_tensors="pt"
        )
        video_tensor = video_processed["pixel_values_videos"]
        grid_thw = video_processed["video_grid_thw"][0]
        
        # 官方的时间戳处理
        second_per_grid_ts = [
            self.data_args.image_processor.temporal_patch_size / fps
        ] * len(grid_thw)
        
        return video_tensor, grid_thw, second_per_grid_ts
    
    def video_decord(self, video_file):
        """官方的视频解码函数"""
        if not os.path.exists(video_file):
            logger.warning(f"File not exist: {video_file}")
            return None, None, None
            
        try:
            vr = VideoReader(video_file, num_threads=4)
            total_frames = len(vr)
            avg_fps = vr.get_avg_fps()
            video_length = total_frames / avg_fps
            interval = getattr(self.data_args, "base_interval", 4)

            num_frames_to_sample = round(video_length / interval)
            video_min_frames = getattr(self.data_args, "video_min_frames", 4)
            video_max_frames = getattr(self.data_args, "video_max_frames", 8)

            target_frames = min(
                max(num_frames_to_sample, video_min_frames), video_max_frames
            )
            frame_idx = np.linspace(0, total_frames - 1, target_frames, dtype=int)
            frame_idx = np.unique(frame_idx)
            video = vr.get_batch(frame_idx).asnumpy()
            
            return self.process_video_frames(video, frame_idx, video_length)
        except Exception as e:
            logger.error(f"视频处理失败 {video_file}: {e}")
            return None, None, None
    
    def _get_item(self, i):
        """官方的数据获取函数"""
        sources = self.list_data_dict[i]
        
        # 处理对话格式
        if isinstance(sources, dict):
            sources = [sources]
        
        # 确保conversations格式
        if "conversations" in sources[0]:
            conversations = sources[0]["conversations"]
            video_file = sources[0]["video"]
        else:
            conversations = sources
            video_file = sources[0].get("video", "")
        
        # 处理视频
        video_tensor, grid_thw, second_per_grid_ts = None, None, None
        if video_file and os.path.exists(video_file):
            video_tensor, grid_thw, second_per_grid_ts = self.video_decord(video_file)
        
        if video_tensor is None:
            # 使用虚拟数据
            logger.warning(f"使用虚拟视频数据: {video_file}")
            video_tensor = torch.zeros(1, 3, 336, 336)
            grid_thw = torch.tensor([4, 24, 24])  # 使用官方默认值
            second_per_grid_ts = [0.1] * len(grid_thw)
        
        # 转换为官方格式
        sources_formatted = []
        for conv in conversations:
            role = conv.get("from", conv.get("role", ""))
            content = conv.get("value", conv.get("content", ""))
            
            # 确保视频标记存在
            if role == "human" and "<video>" not in content:
                content = "<video>\n" + content
            
            sources_formatted.append({
                "from": role,
                "value": content
            })
        
        # 使用官方的预处理函数
        grid_thw_video = [int(grid_thw[0] * grid_thw[1] * grid_thw[2])]
        
        data_dict = preprocess_qwen_2_visual(
            [sources_formatted],
            self.tokenizer,
            grid_thw_image=[],
            grid_thw_video=grid_thw_video,
        )
        
        # 添加视频数据
        if isinstance(video_tensor, torch.Tensor):
            if video_tensor.dim() == 5:
                video_tensor = video_tensor.squeeze(0)
            data_dict["pixel_values_videos"] = video_tensor
            data_dict["video_grid_thw"] = grid_thw.unsqueeze(0)
        
        return data_dict
    
    def __getitem__(self, i):
        """官方的重试机制"""
        num_base_retries = 3
        
        # 尝试当前样本
        for attempt_idx in range(num_base_retries):
            try:
                sample = self._get_item(i)
                return sample
            except Exception as e:
                logger.warning(f"[Try #{attempt_idx}] Failed to fetch sample {i}. Exception: {e}")
                time.sleep(1)
        
        # 如果失败，使用虚拟数据
        logger.error(f"All attempts failed for sample {i}, using dummy data")
        dummy_ids = torch.tensor([151643] * 100)
        return {
            'input_ids': dummy_ids,
            'labels': dummy_ids.clone(),
            'pixel_values_videos': torch.zeros(4, 3, 336, 336),
            'video_grid_thw': torch.tensor([[4, 24, 24]])
        }

def preprocess_qwen_2_visual(
    sources,
    tokenizer,
    grid_thw_image: List = [],
    grid_thw_video: List = [],
) -> Dict:
    """官方的视觉数据预处理函数"""
    roles = {"human": "user", "gpt": "assistant"}
    system_message = "You are a helpful assistant."

    tokenizer = copy.deepcopy(tokenizer)
    chat_template = "{% for message in messages %}{{'<|im_start|>' + message['role'] + '\n' + message['content'] + '<|im_end|>' + '\n'}}{% endfor %}{% if add_generation_prompt %}{{ '<|im_start|>assistant\n' }}{% endif %}"
    tokenizer.chat_template = chat_template

    visual_replicate_index_video = 0
    input_ids, targets = [], []

    for i, source in enumerate(sources):
        try:
            if roles[source[0]["from"]] != roles["human"]:
                source = source[1:]
        except:
            pass

        input_id, target = [], []

        input_id += tokenizer.apply_chat_template(
            [{"role": "system", "content": system_message}]
        )
        target += [IGNORE_INDEX] * len(input_id)

        for conv in source:
            role = conv.get("from", conv.get("role", ""))
            content = conv.get("value", conv.get("content", ""))

            role = roles.get(role, role)
            if role == "user":
                if "<video>" in content:
                    parts = content.split("<video>")
                    new_parts = []
                    for i in range(len(parts) - 1):
                        new_parts.append(parts[i])
                        replacement = (
                            "<|vision_start|>"
                            + f"<|video_pad|>"
                            * grid_thw_video[visual_replicate_index_video]
                            + "<|vision_end|>"
                        )
                        new_parts.append(replacement)
                        visual_replicate_index_video += 1
                    new_parts.append(parts[-1])
                    content = "".join(new_parts)

            conv_formatted = [{"role": role, "content": content}]
            encode_id = tokenizer.apply_chat_template(conv_formatted)
            input_id += encode_id
            if role in ["user", "system"]:
                target += [IGNORE_INDEX] * len(encode_id)
            else:
                target_mask = encode_id.copy()
                target_mask[:3] = [IGNORE_INDEX] * 3
                target += target_mask

        assert len(input_id) == len(target), f"{len(input_id)} != {len(target)}"
        input_ids.append(input_id)
        targets.append(target)

    input_ids = torch.tensor(input_ids, dtype=torch.long)
    targets = torch.tensor(targets, dtype=torch.long)

    return dict(
        input_ids=input_ids,
        labels=targets,
    )

def official_data_collator(features):
    """官方的数据整理函数"""
    if not features:
        return {}

    # 获取所有键
    keys = features[0].keys()
    batch = {}

    for key in keys:
        if key in ["pixel_values_videos", "video_grid_thw"]:
            # 视频数据特殊处理
            values = [f[key] for f in features if key in f]
            if values:
                if key == "pixel_values_videos":
                    # 确保所有视频tensor形状一致
                    max_shape = max([v.shape for v in values], key=lambda x: x[0])
                    padded_values = []
                    for v in values:
                        if v.shape != max_shape:
                            # 填充到最大形状
                            pad_size = max_shape[0] - v.shape[0]
                            if pad_size > 0:
                                padding = torch.zeros(pad_size, *v.shape[1:], dtype=v.dtype, device=v.device)
                                v = torch.cat([v, padding], dim=0)
                            elif pad_size < 0:
                                v = v[:max_shape[0]]
                        padded_values.append(v)
                    batch[key] = torch.stack(padded_values)
                else:
                    batch[key] = torch.cat(values, dim=0)
        else:
            # 标准数据处理
            values = [f[key] for f in features]
            if isinstance(values[0], torch.Tensor):
                # 填充到相同长度
                max_len = max(v.shape[0] for v in values)
                padded_values = []
                for v in values:
                    if v.shape[0] < max_len:
                        pad_size = max_len - v.shape[0]
                        if key == "labels":
                            padding = torch.full((pad_size,), IGNORE_INDEX, dtype=v.dtype)
                        else:
                            padding = torch.zeros(pad_size, dtype=v.dtype)
                        v = torch.cat([v, padding], dim=0)
                    padded_values.append(v)
                batch[key] = torch.stack(padded_values)
            else:
                batch[key] = values

    return batch

def main():
    """主训练函数 - 完全基于官方实现"""
    logger.info("🚀 开始官方Qwen视频LoRA微调...")

    # 解析参数
    parser = HfArgumentParser((ModelArguments, DataArguments, CustomTrainingArguments))
    model_args, data_args, training_args = parser.parse_args_into_dataclasses()

    # 设置输出目录
    output_path = "output/infrared_video_lora_official"
    os.makedirs(output_path, exist_ok=True)

    logger.info("📊 加载数据...")

    # 检查是否已有训练数据
    train_file = "data/infrared_video_train.json"
    if os.path.exists(train_file):
        with open(train_file, 'r') as f:
            existing_data = json.load(f)
        if len(existing_data) > 0:
            logger.info(f"✅ 使用现有训练数据: {len(existing_data)} 个样本")
            data_list = existing_data
        else:
            logger.info("训练数据文件为空，重新创建...")
            data_list = []
    else:
        logger.info("训练数据文件不存在，创建新数据...")
        data_list = []

    # 如果没有数据，创建训练数据
    if len(data_list) == 0:
        video_dir = Path("data/videos")

        if video_dir.exists():
            video_files = list(video_dir.glob("*.mp4"))
            logger.info(f"✅ 找到 {len(video_files)} 个视频文件")

            # 过滤训练集视频（排除data01和data23，它们是测试集）
            train_videos = [vf for vf in video_files if not ('data01' in vf.name or 'data23' in vf.name)]
            logger.info(f"✅ 训练集视频: {len(train_videos)} 个")

            for video_file in train_videos[:100]:  # 限制数量用于测试
                data_list.append({
                    "video": str(video_file),
                    "conversations": [
                        {
                            "from": "human",
                            "value": "请分析这个红外视频中的目标。"
                        },
                        {
                            "from": "gpt",
                            "value": "我看到了红外视频中的目标物体。"
                        }
                    ]
                })

        # 保存训练数据
        with open(train_file, 'w') as f:
            json.dump(data_list, f, ensure_ascii=False, indent=2)
        logger.info(f"✅ 创建训练数据: {len(data_list)} 个样本")

    logger.info(f"✅ 数据: {len(data_list)} 个样本")

    logger.info("🔤 加载processor...")
    processor = AutoProcessor.from_pretrained(
        model_args.model_name_or_path,
        trust_remote_code=True
    )

    # 设置数据参数
    data_args.image_processor = processor.image_processor

    logger.info("📝 创建数据集...")
    train_dataset = OfficialQwenVideoDataset(
        tokenizer=processor.tokenizer,
        data_args=data_args
    )

    logger.info("🤖 加载模型...")
    model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
        model_args.model_name_or_path,
        torch_dtype=torch.bfloat16,
        device_map="auto",
        trust_remote_code=True,
        attn_implementation="flash_attention_2"  # 官方推荐
    )

    logger.info("⚙️ 配置LoRA...")
    # 官方LoRA配置
    lora_config = LoraConfig(
        task_type=TaskType.CAUSAL_LM,
        r=128,
        lora_alpha=256,
        target_modules=[
            "q_proj", "k_proj", "v_proj", "o_proj",
            "gate_proj", "up_proj", "down_proj"
        ] if model_args.tune_mm_llm else [],
        lora_dropout=0.05,
        bias="none",
    )

    model = get_peft_model(model, lora_config)
    model.print_trainable_parameters()

    # 官方训练参数
    training_args = CustomTrainingArguments(
        output_dir=output_path,
        per_device_train_batch_size=2,  # 官方推荐
        gradient_accumulation_steps=8,  # 官方推荐
        num_train_epochs=0.5,
        learning_rate=2e-7,  # 官方推荐范围
        weight_decay=0,
        warmup_ratio=0.03,
        max_grad_norm=1.0,
        lr_scheduler_type="cosine",
        logging_steps=1,
        save_steps=1000,  # 官方配置
        save_strategy="steps",
        save_total_limit=1,
        remove_unused_columns=False,
        bf16=True,
        dataloader_num_workers=4,  # 官方配置
        gradient_checkpointing=True,
        report_to="none",
        model_max_length=8192,  # 官方推荐，不允许截断
        dataloader_drop_last=True,
    )

    logger.info("🎯 开始训练（官方配置）...")
    logger.info(f"样本数: {len(train_dataset)}")
    logger.info(f"批次大小: {training_args.per_device_train_batch_size}")
    logger.info(f"梯度累积: {training_args.gradient_accumulation_steps}")
    logger.info(f"学习率: {training_args.learning_rate}")
    logger.info(f"最大像素: {data_args.max_pixels}")
    logger.info(f"最小像素: {data_args.min_pixels}")

    # 创建Trainer
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=train_dataset,
        data_collator=official_data_collator,
        tokenizer=processor.tokenizer,
    )

    # 开始训练
    trainer.train()

    # 保存模型
    trainer.save_model()
    logger.info(f"✅ 训练完成！模型已保存到: {output_path}")

if __name__ == "__main__":
    main()
