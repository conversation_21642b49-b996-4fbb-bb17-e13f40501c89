#!/bin/bash

# 红外图像微小目标检测微调训练脚本
# 基于LLaMA-Factory框架

export CUDA_VISIBLE_DEVICES=0
export PYTHONPATH="/home/<USER>/Qwen/Qwen2.5-VL/LLaMA-Factory/src:$PYTHONPATH"

# 确保在LLaMA-Factory目录中运行
cd /home/<USER>/Qwen/Qwen2.5-VL/LLaMA-Factory

echo "当前工作目录: $(pwd)"
echo "检查数据文件..."
ls -la data/infrared_detection_train.json
echo "检查视频目录..."
ls -la data/videos/ | head -10

python src/train.py \
    --model_name_or_path /home/<USER>/Qwen/Qwen2.5-VL/Qwen2.5-VL-7B-Instruct \
    --stage sft \
    --do_train True \
    --finetuning_type lora \
    --dataset infrared_detection \
    --template qwen2_vl \
    --cutoff_len 8192 \
    --learning_rate 5e-05 \
    --num_train_epochs 3.0 \
    --per_device_train_batch_size 1 \
    --gradient_accumulation_steps 8 \
    --lr_scheduler_type cosine \
    --warmup_ratio 0.1 \
    --bf16 True \
    --logging_steps 10 \
    --save_steps 500 \
    --output_dir /home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/output/checkpoints \
    --lora_rank 64 \
    --lora_alpha 16 \
    --lora_dropout 0.05 \
    --lora_target q_proj,k_proj,v_proj,o_proj,gate_proj,up_proj,down_proj \
    --max_new_tokens 2048 \
    --plot_loss \
    --overwrite_output_dir

echo "微调训练完成！"
echo "检查点保存在: /home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/output/checkpoints"
