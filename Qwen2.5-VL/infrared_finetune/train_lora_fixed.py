"""
修复后的LoRA微调训练脚本
参考官方配置进行优化
"""

import os
import json
import logging
import torch
from pathlib import Path
from datasets import Dataset
from transformers import (
    Qwen2_5_VLProcessor, 
    Qwen2_5_VLForConditionalGeneration,
    TrainingArguments,
    Trainer
)
from peft import LoraConfig, get_peft_model, TaskType

def setup_logger():
    """设置日志记录器"""
    logging.basicConfig(level=logging.INFO)
    return logging.getLogger(__name__)

def load_and_process_data(data_path: str, processor):
    """加载和处理数据"""
    logger = setup_logger()
    
    logger.info(f"📊 加载训练数据: {data_path}")
    
    with open(data_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    logger.info(f"✅ 加载了 {len(data)} 个训练样本")
    
    # 转换为文本格式
    processed_data = []
    for item in data:
        conversations = item['conversations']
        
        # 构建对话文本
        text = ""
        for conv in conversations:
            if conv['from'] == 'human':
                text += f"Human: {conv['value']}\n"
            elif conv['from'] == 'gpt':
                text += f"Assistant: {conv['value']}\n"
        
        processed_data.append({
            'text': text,
            'video_path': item['video']
        })
    
    return Dataset.from_list(processed_data)

def main():
    """主函数"""
    logger = setup_logger()
    
    logger.info("🚀 开始红外视频目标检测LoRA微调（修复版）...")
    
    # 配置路径
    model_path = "/home/<USER>/Qwen/Qwen2.5-VL-7B-Instruct"
    data_path = "data/infrared_video_train.json"
    output_path = "output/lora_checkpoints_fixed"
    
    # 创建输出目录
    Path(output_path).mkdir(parents=True, exist_ok=True)
    
    # 加载处理器
    logger.info("🔤 加载tokenizer和processor...")
    processor = Qwen2_5_VLProcessor.from_pretrained(model_path, trust_remote_code=True)
    
    # 加载数据
    dataset = load_and_process_data(data_path, processor)
    
    # 数据预处理
    logger.info("📝 预处理数据...")
    def tokenize_function(examples):
        # 使用processor处理文本
        tokenized = processor(
            text=examples["text"],
            padding=True,
            truncation=True,
            max_length=4096,  # 增加长度以容纳视频tokens
            return_tensors=None
        )
        tokenized["labels"] = tokenized["input_ids"].copy()
        return tokenized

    tokenized_dataset = dataset.map(tokenize_function, batched=True, remove_columns=["text", "video_path"])
    
    # 加载模型
    logger.info("🤖 加载模型...")
    model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
        model_path,
        torch_dtype=torch.bfloat16,
        device_map={"": "cuda:0"},
        trust_remote_code=True,
        attn_implementation="flash_attention_2"
    )
    
    # 配置LoRA（参考官方配置）
    logger.info("⚙️ 配置LoRA...")
    lora_config = LoraConfig(
        task_type=TaskType.CAUSAL_LM,
        target_modules=["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"],
        inference_mode=False,
        r=64,  # 增加rank
        lora_alpha=64,
        lora_dropout=0.05,  # 降低dropout
        bias="none",
    )
    
    # 应用LoRA
    model = get_peft_model(model, lora_config)
    model.print_trainable_parameters()
    
    # 训练参数（参考官方配置）
    training_args = TrainingArguments(
        output_dir=output_path,
        per_device_train_batch_size=2,  # 参考官方配置
        gradient_accumulation_steps=8,  # 增加梯度累积
        num_train_epochs=1,  # 减少训练轮数
        learning_rate=2e-5,  # 降低学习率
        weight_decay=0.01,  # 添加权重衰减
        warmup_ratio=0.03,  # 添加warmup
        max_grad_norm=1.0,  # 添加梯度裁剪
        lr_scheduler_type="cosine",  # 使用cosine学习率调度
        logging_steps=10,
        save_steps=100,
        save_strategy="steps",
        save_total_limit=3,
        remove_unused_columns=False,
        bf16=True,
        dataloader_num_workers=4,  # 增加数据加载workers
        gradient_checkpointing=True,  # 启用梯度检查点
        report_to="none",
        # 视频相关参数
        model_max_length=4096,
    )
    
    # 创建训练器
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=tokenized_dataset,
        processing_class=processor,  # 使用processing_class而不是tokenizer
    )
    
    # 开始训练
    logger.info("🎯 开始LoRA训练（修复版）...")
    trainer.train()
    
    # 保存模型
    logger.info("💾 保存LoRA权重...")
    trainer.save_model()
    processor.save_pretrained(output_path)
    
    logger.info("🎉 LoRA微调完成!")
    logger.info(f"📁 模型保存在: {output_path}")
    
    # 列出保存的文件
    saved_files = os.listdir(output_path)
    logger.info(f"📋 保存的文件: {saved_files}")
    
    # 输出训练统计
    logger.info("📊 训练统计:")
    logger.info(f"   训练样本数: {len(dataset)}")
    logger.info(f"   训练轮数: {training_args.num_train_epochs}")
    logger.info(f"   学习率: {training_args.learning_rate}")
    logger.info(f"   批次大小: {training_args.per_device_train_batch_size}")
    logger.info(f"   梯度累积: {training_args.gradient_accumulation_steps}")
    logger.info(f"   有效批次大小: {training_args.per_device_train_batch_size * training_args.gradient_accumulation_steps}")

if __name__ == "__main__":
    main()
