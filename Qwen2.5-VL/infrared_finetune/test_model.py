#!/usr/bin/env python3
"""
测试模型加载
"""

import os
import torch
from transformers import Qwen2VLForConditionalGeneration, Qwen2VLProcessor

def test_model_loading():
    """测试模型加载"""
    
    print("🔍 测试模型加载...")
    
    # 设置环境
    os.environ["CUDA_VISIBLE_DEVICES"] = "0"
    
    model_path = "/home/<USER>/Qwen/Qwen2.5-VL/Qwen2.5-VL-7B-Instruct"
    
    try:
        print("📝 加载处理器...")
        processor = Qwen2VLProcessor.from_pretrained(model_path)
        print("✅ 处理器加载成功")
        
        print("📝 加载模型...")
        model = Qwen2VLForConditionalGeneration.from_pretrained(
            model_path,
            torch_dtype=torch.float16,
            device_map={"": 0},
            low_cpu_mem_usage=True
        )
        print("✅ 模型加载成功")
        
        print(f"📊 模型设备: {next(model.parameters()).device}")
        print(f"📊 模型数据类型: {next(model.parameters()).dtype}")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_model_loading()
    if success:
        print("🎉 模型加载测试成功！")
    else:
        print("💥 模型加载测试失败！")
