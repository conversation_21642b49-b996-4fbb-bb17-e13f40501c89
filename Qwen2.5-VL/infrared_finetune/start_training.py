#!/usr/bin/env python3
"""
独立的训练启动脚本
绕过任何可能的命令拦截问题
"""

import os
import sys
import subprocess
import json

def verify_setup():
    """验证训练环境"""
    print("🔍 验证训练环境...")
    
    # 检查关键文件
    train_file = "/home/<USER>/Qwen/Qwen2.5-VL/LLaMA-Factory/data/infrared_detection_train.json"
    video_dir = "/home/<USER>/Qwen/Qwen2.5-VL/LLaMA-Factory/data/videos"
    model_dir = "/home/<USER>/Qwen/Qwen2.5-VL/Qwen2.5-VL-7B-Instruct"
    
    if not os.path.exists(train_file):
        print(f"❌ 训练数据文件不存在: {train_file}")
        return False
    
    if not os.path.exists(video_dir):
        print(f"❌ 视频目录不存在: {video_dir}")
        return False
    
    if not os.path.exists(model_dir):
        print(f"❌ 模型目录不存在: {model_dir}")
        return False
    
    # 检查视频文件数量
    video_files = [f for f in os.listdir(video_dir) if f.endswith('.mp4')]
    print(f"✅ 找到 {len(video_files)} 个视频文件")
    
    # 检查训练数据
    with open(train_file, 'r') as f:
        train_data = json.load(f)
    print(f"✅ 训练数据包含 {len(train_data)} 个样本")
    
    # 验证前几个视频文件路径
    success_count = 0
    for i, item in enumerate(train_data[:5]):
        if 'videos' in item:
            for video_path in item['videos']:
                if os.path.exists(video_path):
                    success_count += 1
                    print(f"✅ 视频文件存在: {os.path.basename(video_path)}")
                else:
                    print(f"❌ 视频文件不存在: {video_path}")
    
    if success_count > 0:
        print(f"✅ 验证通过，{success_count} 个视频文件可访问")
        return True
    else:
        print("❌ 验证失败，无法访问视频文件")
        return False

def start_training():
    """启动训练"""
    print("🚀 启动训练...")
    
    # 设置环境变量
    env = os.environ.copy()
    env['CUDA_VISIBLE_DEVICES'] = '0'
    env['PYTHONPATH'] = '/home/<USER>/Qwen/Qwen2.5-VL/LLaMA-Factory/src:' + env.get('PYTHONPATH', '')
    
    # 训练命令
    cmd = [
        'python', 'src/train.py',
        '--model_name_or_path', '/home/<USER>/Qwen/Qwen2.5-VL/Qwen2.5-VL-7B-Instruct',
        '--stage', 'sft',
        '--do_train',
        '--finetuning_type', 'lora',
        '--dataset', 'infrared_detection',
        '--template', 'qwen2_vl',
        '--cutoff_len', '8192',
        '--learning_rate', '5e-05',
        '--num_train_epochs', '2.0',
        '--per_device_train_batch_size', '1',
        '--gradient_accumulation_steps', '8',
        '--lr_scheduler_type', 'cosine',
        '--warmup_ratio', '0.1',
        '--bf16',
        '--logging_steps', '10',
        '--save_steps', '50',
        '--output_dir', '/home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/output/checkpoints',
        '--lora_rank', '64',
        '--lora_alpha', '16',
        '--lora_dropout', '0.05',
        '--lora_target', 'q_proj,k_proj,v_proj,o_proj,gate_proj,up_proj,down_proj',
        '--max_new_tokens', '2048',
        '--plot_loss',
        '--overwrite_output_dir'
    ]
    
    print(f"📝 执行命令: {' '.join(cmd)}")
    print("📁 工作目录: /home/<USER>/Qwen/Qwen2.5-VL/LLaMA-Factory")
    
    try:
        # 切换到LLaMA-Factory目录并运行训练
        result = subprocess.run(
            cmd,
            cwd='/home/<USER>/Qwen/Qwen2.5-VL/LLaMA-Factory',
            env=env,
            capture_output=False,  # 实时显示输出
            text=True
        )
        
        if result.returncode == 0:
            print("✅ 训练完成！")
            print("📁 检查点保存在: /home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/output/checkpoints")
            return True
        else:
            print(f"❌ 训练失败，返回码: {result.returncode}")
            return False
            
    except Exception as e:
        print(f"💥 训练过程中出现错误: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🎯 红外图像微调项目 - 独立训练启动器")
    print("=" * 60)
    
    # 验证环境
    if not verify_setup():
        print("\n❌ 环境验证失败，请检查配置")
        sys.exit(1)
    
    print("\n" + "=" * 60)
    print("🚀 开始训练")
    print("=" * 60)
    
    # 启动训练
    success = start_training()
    
    if success:
        print("\n🎉 训练成功完成！")
        print("\n📋 后续步骤:")
        print("1. 检查输出目录: /home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/output/checkpoints")
        print("2. 运行评估: python evaluate_model.py --lora-path ./output/checkpoints")
        sys.exit(0)
    else:
        print("\n💥 训练失败！")
        sys.exit(1)

if __name__ == "__main__":
    main()
