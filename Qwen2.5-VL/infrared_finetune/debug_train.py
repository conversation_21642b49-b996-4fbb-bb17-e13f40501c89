#!/usr/bin/env python3
"""
调试训练脚本
"""

import os
import sys
import logging
import traceback

# 添加LLaMA-Factory路径
sys.path.insert(0, '/home/<USER>/Qwen/Qwen2.5-VL/LLaMA-Factory/src')

# 设置环境变量
os.environ['CUDA_VISIBLE_DEVICES'] = '0'

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_imports():
    """测试导入"""
    try:
        logger.info("测试基础导入...")
        import torch
        logger.info(f"PyTorch版本: {torch.__version__}")
        logger.info(f"CUDA可用: {torch.cuda.is_available()}")
        
        import transformers
        logger.info(f"Transformers版本: {transformers.__version__}")
        
        import accelerate
        logger.info(f"Accelerate版本: {accelerate.__version__}")
        
        logger.info("测试LLaMA-Factory导入...")
        from llamafactory.train.tuner import run_exp
        logger.info("LLaMA-Factory导入成功")
        
        return True
    except Exception as e:
        logger.error(f"导入失败: {e}")
        traceback.print_exc()
        return False

def test_dataset():
    """测试数据集"""
    try:
        logger.info("测试数据集...")
        
        # 检查数据文件
        data_file = "/home/<USER>/Qwen/Qwen2.5-VL/LLaMA-Factory/data/infrared_detection_train.json"
        if os.path.exists(data_file):
            logger.info(f"训练数据文件存在: {data_file}")
            
            import json
            with open(data_file, 'r') as f:
                data = json.load(f)
            logger.info(f"数据样本数: {len(data)}")
            
            # 检查第一个样本
            if len(data) > 0:
                sample = data[0]
                logger.info(f"样本结构: {list(sample.keys())}")
                if 'videos' in sample:
                    video_path = sample['videos'][0]
                    full_video_path = f"/home/<USER>/Qwen/Qwen2.5-VL/LLaMA-Factory/data/{video_path}"
                    logger.info(f"视频文件: {full_video_path}")
                    logger.info(f"视频文件存在: {os.path.exists(full_video_path)}")
        else:
            logger.error(f"训练数据文件不存在: {data_file}")
            return False
        
        # 检查dataset_info.json
        dataset_info_file = "/home/<USER>/Qwen/Qwen2.5-VL/LLaMA-Factory/data/dataset_info.json"
        if os.path.exists(dataset_info_file):
            logger.info(f"数据集信息文件存在: {dataset_info_file}")
            
            with open(dataset_info_file, 'r') as f:
                dataset_info = json.load(f)
            
            if 'infrared_detection' in dataset_info:
                logger.info("infrared_detection数据集已配置")
                logger.info(f"数据集配置: {dataset_info['infrared_detection']}")
            else:
                logger.error("infrared_detection数据集未配置")
                return False
        else:
            logger.error(f"数据集信息文件不存在: {dataset_info_file}")
            return False
        
        return True
    except Exception as e:
        logger.error(f"数据集测试失败: {e}")
        traceback.print_exc()
        return False

def test_model():
    """测试模型"""
    try:
        logger.info("测试模型...")
        
        model_path = "/home/<USER>/Qwen/Qwen2.5-VL/Qwen2.5-VL-7B-Instruct"
        if os.path.exists(model_path):
            logger.info(f"模型路径存在: {model_path}")
            
            # 检查模型文件
            config_file = os.path.join(model_path, "config.json")
            if os.path.exists(config_file):
                logger.info("模型配置文件存在")
            else:
                logger.error("模型配置文件不存在")
                return False
        else:
            logger.error(f"模型路径不存在: {model_path}")
            return False
        
        return True
    except Exception as e:
        logger.error(f"模型测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    logger.info("开始调试测试...")

    # 切换到LLaMA-Factory目录
    llamafactory_dir = "/home/<USER>/Qwen/Qwen2.5-VL/LLaMA-Factory"
    logger.info(f"切换到LLaMA-Factory目录: {llamafactory_dir}")
    os.chdir(llamafactory_dir)

    # 测试导入
    if not test_imports():
        logger.error("导入测试失败")
        return

    # 测试数据集
    if not test_dataset():
        logger.error("数据集测试失败")
        return

    # 测试模型
    if not test_model():
        logger.error("模型测试失败")
        return

    logger.info("所有测试通过！")

    # 尝试运行训练
    try:
        logger.info("尝试运行训练...")

        # 设置训练参数
        sys.argv = [
            'train.py',
            '--model_name_or_path', '/home/<USER>/Qwen/Qwen2.5-VL/Qwen2.5-VL-7B-Instruct',
            '--stage', 'sft',
            '--do_train',
            '--finetuning_type', 'lora',
            '--dataset', 'infrared_detection',
            '--template', 'qwen2_vl',
            '--cutoff_len', '8192',
            '--learning_rate', '5e-05',
            '--num_train_epochs', '1.0',  # 减少到1个epoch用于测试
            '--per_device_train_batch_size', '1',
            '--gradient_accumulation_steps', '8',
            '--lr_scheduler_type', 'cosine',
            '--warmup_ratio', '0.1',
            '--bf16',
            '--logging_steps', '5',
            '--save_steps', '100',
            '--output_dir', '/home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/output/checkpoints',
            '--lora_rank', '64',
            '--lora_alpha', '16',
            '--lora_dropout', '0.05',
            '--lora_target', 'q_proj,k_proj,v_proj,o_proj,gate_proj,up_proj,down_proj',
            '--max_new_tokens', '2048',
            '--plot_loss',
            '--overwrite_output_dir'
        ]

        from llamafactory.train.tuner import run_exp
        run_exp()

        logger.info("训练完成！")

    except Exception as e:
        logger.error(f"训练失败: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    main()
