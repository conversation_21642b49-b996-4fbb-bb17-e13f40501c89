#!/usr/bin/env python3
"""
红外图像微小目标检测微调配置文件
基于LLaMA-Factory框架的Qwen2.5-VL微调配置
"""

import os
from pathlib import Path

class FineTuneConfig:
    """微调配置类"""
    
    def __init__(self):
        # 基础路径配置
        self.project_root = Path("/home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune")
        self.llamafactory_root = Path("/home/<USER>/Qwen/Qwen2.5-VL/LLaMA-Factory")
        self.model_path = "/home/<USER>/Qwen/Qwen2.5-VL/Qwen2.5-VL-7B-Instruct"
        
        # 数据配置
        self.data_dir = self.project_root / "data"
        self.training_data_file = "infrared_detection_train.json"
        self.dataset_name = "infrared_detection"
        
        # 输出配置
        self.output_dir = self.project_root / "output"
        self.checkpoint_dir = self.output_dir / "checkpoints"
        self.logs_dir = self.output_dir / "logs"
        
        # 创建必要目录
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.checkpoint_dir.mkdir(parents=True, exist_ok=True)
        self.logs_dir.mkdir(parents=True, exist_ok=True)
        
        # LoRA配置
        self.lora_config = {
            "lora_rank": 64,
            "lora_alpha": 16,
            "lora_dropout": 0.05,
            "lora_target": "q_proj,k_proj,v_proj,o_proj,gate_proj,up_proj,down_proj",
            "additional_target": "",
            "pissa_init": False,
            "pissa_iter": 16,
            "pissa_convert": False,
            "use_rslora": False,
            "use_dora": False,
            "lora_plus_scale": 1.0,
            "loraplus_lr_ratio": 16.0,
        }
        
        # 训练配置
        self.training_config = {
            "stage": "sft",
            "do_train": True,
            "finetuning_type": "lora",
            "deepspeed": "",
            "use_fast_tokenizer": True,
            "flash_attn": "auto",
            "shift_attn": False,
            "rope_scaling": "",
            "mask_history": False,
            
            # 数据配置
            "dataset": self.dataset_name,
            "template": "qwen2_vl",
            "cutoff_len": 8192,
            "train_on_prompt": False,
            "mask_history": False,
            "streaming": False,
            "buffer_size": 16384,
            "mix_strategy": "concat",
            "interleave_probs": "",
            "overwrite_cache": False,
            "preprocessing_num_workers": 16,
            
            # 训练参数
            "per_device_train_batch_size": 1,
            "gradient_accumulation_steps": 8,
            "learning_rate": 5e-5,
            "num_train_epochs": 3.0,
            "max_steps": -1,
            "lr_scheduler_type": "cosine",
            "warmup_ratio": 0.1,
            "warmup_steps": 0,
            
            # 优化器配置
            "optim": "adamw_torch",
            "adam_beta1": 0.9,
            "adam_beta2": 0.999,
            "adam_epsilon": 1e-8,
            "max_grad_norm": 1.0,
            "weight_decay": 0.01,
            
            # 保存和日志配置
            "output_dir": str(self.checkpoint_dir),
            "logging_steps": 10,
            "save_steps": 500,
            "eval_steps": 500,
            "save_total_limit": 3,
            "save_strategy": "steps",
            "evaluation_strategy": "no",
            "load_best_model_at_end": False,
            "greater_is_better": False,
            "metric_for_best_model": "",
            
            # 其他配置
            "fp16": False,
            "bf16": True,
            "ddp_timeout": 180000000,
            "include_num_input_tokens_seen": False,
            "packing": False,
            "neat_packing": False,
            "tool_format": "",
            "resize_vocab": False,
            "freeze_trainable_layers": 0,
            "freeze_trainable_modules": "",
            "freeze_extra_modules": "",
            "train_mm_proj_only": False,
            "compute_accuracy": False,
            "plot_loss": False,
            "do_sample": True,
            "temperature": 0.95,
            "top_p": 0.7,
            "top_k": 50,
            "num_beams": 1,
            "max_length": 4096,
            "max_new_tokens": 2048,
            "repetition_penalty": 1.0,
            "length_penalty": 1.0,
            "default_system": "",
        }
        
        # 更新LoRA配置到训练配置中
        self.training_config.update(self.lora_config)
    
    def generate_dataset_info(self) -> dict:
        """生成数据集信息配置"""
        dataset_info = {
            self.dataset_name: {
                "file_name": self.training_data_file,
                "formatting": "sharegpt",
                "columns": {
                    "messages": "messages",
                    "videos": "videos"
                },
                "tags": {
                    "role_tag": "role",
                    "content_tag": "content",
                    "user_tag": "user",
                    "assistant_tag": "assistant"
                }
            }
        }
        return dataset_info
    
    def save_dataset_info(self):
        """保存数据集信息到LLaMA-Factory的dataset_info.json"""
        dataset_info_file = self.llamafactory_root / "data" / "dataset_info.json"
        
        # 读取现有的dataset_info.json
        if dataset_info_file.exists():
            import json
            with open(dataset_info_file, 'r', encoding='utf-8') as f:
                existing_info = json.load(f)
        else:
            existing_info = {}
        
        # 添加我们的数据集信息
        new_dataset_info = self.generate_dataset_info()
        existing_info.update(new_dataset_info)
        
        # 保存更新后的dataset_info.json
        import json
        with open(dataset_info_file, 'w', encoding='utf-8') as f:
            json.dump(existing_info, f, ensure_ascii=False, indent=2)
        
        print(f"数据集信息已保存到: {dataset_info_file}")
    
    def copy_training_data(self):
        """复制训练数据到LLaMA-Factory的data目录"""
        import shutil
        
        source_file = self.data_dir / self.training_data_file
        target_file = self.llamafactory_root / "data" / self.training_data_file
        
        if source_file.exists():
            shutil.copy2(source_file, target_file)
            print(f"训练数据已复制到: {target_file}")
        else:
            print(f"警告: 训练数据文件不存在: {source_file}")
    
    def generate_training_script(self) -> str:
        """生成训练脚本"""
        script_content = f"""#!/bin/bash

# 红外图像微小目标检测微调训练脚本
# 基于LLaMA-Factory框架

export CUDA_VISIBLE_DEVICES=0
export PYTHONPATH="{self.llamafactory_root}/src:$PYTHONPATH"

cd {self.llamafactory_root}

python src/train.py \\
    --model_name_or_path {self.model_path} \\
    --stage {self.training_config['stage']} \\
    --do_train {self.training_config['do_train']} \\
    --finetuning_type {self.training_config['finetuning_type']} \\
    --dataset {self.training_config['dataset']} \\
    --template {self.training_config['template']} \\
    --cutoff_len {self.training_config['cutoff_len']} \\
    --learning_rate {self.training_config['learning_rate']} \\
    --num_train_epochs {self.training_config['num_train_epochs']} \\
    --per_device_train_batch_size {self.training_config['per_device_train_batch_size']} \\
    --gradient_accumulation_steps {self.training_config['gradient_accumulation_steps']} \\
    --lr_scheduler_type {self.training_config['lr_scheduler_type']} \\
    --warmup_ratio {self.training_config['warmup_ratio']} \\
    --bf16 {self.training_config['bf16']} \\
    --logging_steps {self.training_config['logging_steps']} \\
    --save_steps {self.training_config['save_steps']} \\
    --output_dir {self.training_config['output_dir']} \\
    --lora_rank {self.training_config['lora_rank']} \\
    --lora_alpha {self.training_config['lora_alpha']} \\
    --lora_dropout {self.training_config['lora_dropout']} \\
    --lora_target {self.training_config['lora_target']} \\
    --max_new_tokens {self.training_config['max_new_tokens']} \\
    --plot_loss \\
    --overwrite_output_dir

echo "微调训练完成！"
echo "检查点保存在: {self.checkpoint_dir}"
"""
        
        script_file = self.project_root / "train.sh"
        with open(script_file, 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        # 添加执行权限
        os.chmod(script_file, 0o755)
        
        print(f"训练脚本已生成: {script_file}")
        return str(script_file)
    
    def setup_environment(self):
        """设置训练环境"""
        print("设置微调训练环境...")
        
        # 保存数据集信息
        self.save_dataset_info()
        
        # 复制训练数据
        self.copy_training_data()
        
        # 生成训练脚本
        script_file = self.generate_training_script()
        
        print("环境设置完成！")
        print(f"训练脚本: {script_file}")
        print(f"输出目录: {self.output_dir}")
        
        return script_file

def main():
    """主函数"""
    config = FineTuneConfig()
    script_file = config.setup_environment()
    
    print("\n" + "="*50)
    print("微调环境设置完成！")
    print("="*50)
    print(f"项目根目录: {config.project_root}")
    print(f"训练脚本: {script_file}")
    print(f"输出目录: {config.output_dir}")
    print("\n运行训练:")
    print(f"cd {config.project_root}")
    print("./train.sh")

if __name__ == "__main__":
    main()
