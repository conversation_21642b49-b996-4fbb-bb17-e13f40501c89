#!/usr/bin/env python3
"""
Create comparison charts for infrared small target detection methods
"""

import matplotlib.pyplot as plt
import numpy as np
from matplotlib.patches import Rectangle

# Set font and style
plt.rcParams['font.family'] = 'DejaVu Sans'
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('default')

def create_overall_comparison():
    """Create overall performance comparison chart"""
    methods = ['Qwen Original', 'Qwen Fine-tuned', 'YOLO']
    recall = [3.0, 31.5, 95.0]
    fp_rate = [95.6, 59.7, 3.3]
    consistency = [0, 0, 100.0]

    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))

    # Recall comparison
    bars1 = ax1.bar(methods, recall, color=['#ff6b6b', '#4ecdc4', '#45b7d1'], alpha=0.8)
    ax1.set_title('Recall Comparison (%)', fontsize=14, fontweight='bold')
    ax1.set_ylabel('Recall (%)')
    ax1.set_ylim(0, 100)
    for i, v in enumerate(recall):
        ax1.text(i, v + 2, f'{v}%', ha='center', fontweight='bold')

    # False positive rate comparison
    bars2 = ax2.bar(methods, fp_rate, color=['#ff6b6b', '#4ecdc4', '#45b7d1'], alpha=0.8)
    ax2.set_title('False Positive Rate Comparison (%)', fontsize=14, fontweight='bold')
    ax2.set_ylabel('FP Rate (%)')
    ax2.set_ylim(0, 100)
    for i, v in enumerate(fp_rate):
        ax2.text(i, v + 2, f'{v}%', ha='center', fontweight='bold')

    # Temporal consistency comparison
    bars3 = ax3.bar(methods, consistency, color=['#ff6b6b', '#4ecdc4', '#45b7d1'], alpha=0.8)
    ax3.set_title('Temporal Consistency Comparison (%)', fontsize=14, fontweight='bold')
    ax3.set_ylabel('Consistency (%)')
    ax3.set_ylim(0, 100)
    for i, v in enumerate(consistency):
        ax3.text(i, v + 2, f'{v}%', ha='center', fontweight='bold')

    # Comprehensive performance radar chart
    categories = ['Recall', 'Low FP Rate', 'Temporal Consistency']
    # Convert FP rate to "Low FP rate" score (100 - FP rate)
    low_fp_rate = [100 - x for x in fp_rate]
    
    qwen_orig = [recall[0], low_fp_rate[0], consistency[0]]
    qwen_tuned = [recall[1], low_fp_rate[1], consistency[1]]
    yolo = [recall[2], low_fp_rate[2], consistency[2]]

    angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
    angles += angles[:1]  # Close the plot

    qwen_orig += qwen_orig[:1]
    qwen_tuned += qwen_tuned[:1]
    yolo += yolo[:1]

    ax4 = plt.subplot(2, 2, 4, projection='polar')
    ax4.plot(angles, qwen_orig, 'o-', linewidth=2, label='Qwen Original', color='#ff6b6b')
    ax4.fill(angles, qwen_orig, alpha=0.25, color='#ff6b6b')
    ax4.plot(angles, qwen_tuned, 'o-', linewidth=2, label='Qwen Fine-tuned', color='#4ecdc4')
    ax4.fill(angles, qwen_tuned, alpha=0.25, color='#4ecdc4')
    ax4.plot(angles, yolo, 'o-', linewidth=2, label='YOLO', color='#45b7d1')
    ax4.fill(angles, yolo, alpha=0.25, color='#45b7d1')

    ax4.set_xticks(angles[:-1])
    ax4.set_xticklabels(categories)
    ax4.set_ylim(0, 100)
    ax4.set_title('Comprehensive Performance Comparison', fontsize=14, fontweight='bold', pad=20)
    ax4.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
    
    plt.tight_layout()
    plt.savefig('overall_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_sequence_comparison():
    """Create detailed sequence comparison"""
    sequences = ['data01', 'data02', 'data04', 'data05', 'data06', 'data07', 'data23', 'data25', 'data26']

    # Qwen fine-tuned results
    qwen_recall = [33.3, 30.4, 32.8, 36.1, 29.5, 34.4, 32.8, 30.2, 28.9]
    qwen_fp = [60.0, 59.1, 57.4, 62.3, 59.0, 60.7, 58.6, 58.7, 61.3]

    # YOLO results
    yolo_recall = [97.3, 90.4, 100.0, 100.0, 93.4, 100.0, 96.9, 78.9, 91.8]
    yolo_fp = [6.4, 1.0, 0.0, 7.6, 1.7, 3.2, 7.5, 3.6, 3.2]

    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))

    x = np.arange(len(sequences))
    width = 0.35

    # Recall comparison
    bars1 = ax1.bar(x - width/2, qwen_recall, width, label='Qwen Fine-tuned', color='#4ecdc4', alpha=0.8)
    bars2 = ax1.bar(x + width/2, yolo_recall, width, label='YOLO', color='#45b7d1', alpha=0.8)

    ax1.set_title('Recall Comparison by Sequence', fontsize=14, fontweight='bold')
    ax1.set_ylabel('Recall (%)')
    ax1.set_xlabel('Sequence')
    ax1.set_xticks(x)
    ax1.set_xticklabels(sequences, rotation=45)
    ax1.legend()
    ax1.set_ylim(0, 105)
    
    # Add value labels
    for bar in bars1:
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{height:.1f}%', ha='center', va='bottom', fontsize=8)
    for bar in bars2:
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{height:.1f}%', ha='center', va='bottom', fontsize=8)

    # False positive rate comparison
    bars3 = ax2.bar(x - width/2, qwen_fp, width, label='Qwen Fine-tuned', color='#4ecdc4', alpha=0.8)
    bars4 = ax2.bar(x + width/2, yolo_fp, width, label='YOLO', color='#45b7d1', alpha=0.8)

    ax2.set_title('False Positive Rate Comparison by Sequence', fontsize=14, fontweight='bold')
    ax2.set_ylabel('FP Rate (%)')
    ax2.set_xlabel('Sequence')
    ax2.set_xticks(x)
    ax2.set_xticklabels(sequences, rotation=45)
    ax2.legend()
    ax2.set_ylim(0, 70)

    # Add value labels
    for bar in bars3:
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{height:.1f}%', ha='center', va='bottom', fontsize=8)
    for bar in bars4:
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{height:.1f}%', ha='center', va='bottom', fontsize=8)
    
    plt.tight_layout()
    plt.savefig('sequence_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_improvement_analysis():
    """Create improvement analysis chart"""
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))

    # Before and after fine-tuning comparison
    metrics = ['Recall', 'FP Rate']
    before = [3.0, 95.6]
    after = [31.5, 59.7]

    x = np.arange(len(metrics))
    width = 0.35

    bars1 = ax1.bar(x - width/2, before, width, label='Before Fine-tuning', color='#ff6b6b', alpha=0.8)
    bars2 = ax1.bar(x + width/2, after, width, label='After Fine-tuning', color='#4ecdc4', alpha=0.8)

    ax1.set_title('Qwen Model: Before vs After Fine-tuning', fontsize=14, fontweight='bold')
    ax1.set_ylabel('Percentage (%)')
    ax1.set_xticks(x)
    ax1.set_xticklabels(metrics)
    ax1.legend()
    ax1.set_ylim(0, 100)
    
    for bar in bars1:
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 2,
                f'{height}%', ha='center', va='bottom', fontweight='bold')
    for bar in bars2:
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 2,
                f'{height}%', ha='center', va='bottom', fontweight='bold')

    # Improvement magnitude
    improvements = ['Recall Improvement', 'FP Rate Reduction']
    values = [31.5 - 3.0, 95.6 - 59.7]
    colors = ['#2ecc71', '#e74c3c']

    bars = ax2.bar(improvements, values, color=colors, alpha=0.8)
    ax2.set_title('Fine-tuning Improvement Magnitude', fontsize=14, fontweight='bold')
    ax2.set_ylabel('Improvement (percentage points)')

    for i, bar in enumerate(bars):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'+{height:.1f}pp', ha='center', va='bottom', fontweight='bold')

    # Performance gap with YOLO
    gap_metrics = ['Recall Gap', 'FP Rate Gap']
    gaps = [95.0 - 31.5, 59.7 - 3.3]

    bars = ax3.bar(gap_metrics, gaps, color=['#e67e22', '#e67e22'], alpha=0.8)
    ax3.set_title('Performance Gap with YOLO', fontsize=14, fontweight='bold')
    ax3.set_ylabel('Gap (percentage points)')

    for bar in bars:
        height = bar.get_height()
        ax3.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{height:.1f}pp', ha='center', va='bottom', fontweight='bold')

    # Performance improvement multiplier
    multipliers = ['Recall Improvement\nMultiplier']
    mult_values = [31.5 / 3.0]

    bars = ax4.bar(multipliers, mult_values, color='#9b59b6', alpha=0.8)
    ax4.set_title('Fine-tuning Effect Multiplier', fontsize=14, fontweight='bold')
    ax4.set_ylabel('Improvement Multiplier')

    for bar in bars:
        height = bar.get_height()
        ax4.text(bar.get_x() + bar.get_width()/2., height + 0.2,
                f'{height:.1f}x', ha='center', va='bottom', fontweight='bold')
    
    plt.tight_layout()
    plt.savefig('improvement_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()

if __name__ == "__main__":
    print("🎨 Generating infrared small target detection method comparison charts...")

    print("📊 Creating overall performance comparison chart...")
    create_overall_comparison()

    print("📈 Creating detailed sequence comparison chart...")
    create_sequence_comparison()

    print("📉 Creating improvement analysis chart...")
    create_improvement_analysis()

    print("✅ All charts generated successfully!")
    print("Generated chart files:")
    print("- overall_comparison.png: Overall performance comparison")
    print("- sequence_comparison.png: Detailed sequence comparison")
    print("- improvement_analysis.png: Improvement analysis")
