"""
红外视频目标检测LoRA微调训练脚本
"""
import os
import json
import torch
import logging
from pathlib import Path
from transformers import (
    Qwen2_5_VLForConditionalGeneration,
    AutoTokenizer,
    TrainingArguments,
    Trainer,
    DataCollatorForLanguageModeling
)
from peft import LoraConfig, TaskType, get_peft_model
from datasets import Dataset

# 设置环境
os.environ["CUDA_VISIBLE_DEVICES"] = "2"
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    """LoRA微调主函数"""
    logger.info("🚀 开始红外视频目标检测LoRA微调...")
    
    # 路径配置
    model_path = "/home/<USER>/Qwen/Qwen2.5-VL/Qwen2.5-VL-7B-Instruct"
    data_path = "data/infrared_video_train.json"
    output_path = "output/lora_checkpoints"
    
    # 创建输出目录
    Path(output_path).mkdir(parents=True, exist_ok=True)
    
    # 加载数据
    logger.info("📊 加载训练数据...")
    with open(data_path, 'r') as f:
        train_data = json.load(f)
    
    # 使用完整的训练数据集
    logger.info(f"✅ 使用完整数据集: {len(train_data)} 个样本进行训练")
    
    # 加载tokenizer
    logger.info("🔤 加载tokenizer...")
    tokenizer = AutoTokenizer.from_pretrained(model_path, trust_remote_code=True)
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    # 预处理数据
    logger.info("📝 预处理数据...")
    processed_data = []
    for item in train_data:
        conversations = item['conversations']
        user_text = conversations[0]['value'].replace('<video>\n', '')
        assistant_text = conversations[1]['value']
        
        # 构建训练文本
        full_text = f"User: {user_text}\nAssistant: {assistant_text}{tokenizer.eos_token}"
        processed_data.append({"text": full_text})
    
    # 转换为Dataset并tokenize
    dataset = Dataset.from_list(processed_data)
    
    def tokenize_function(examples):
        return tokenizer(
            examples["text"],
            truncation=True,
            padding=False,
            max_length=1024,
            return_tensors=None
        )
    
    tokenized_dataset = dataset.map(tokenize_function, batched=True, remove_columns=["text"])
    tokenized_dataset = tokenized_dataset.map(
        lambda examples: {"labels": examples["input_ids"]}, 
        batched=True
    )
    
    # 加载模型
    logger.info("🤖 加载模型...")
    model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
        model_path,
        torch_dtype=torch.bfloat16,
        device_map={"": "cuda:0"},
        trust_remote_code=True,
        attn_implementation="flash_attention_2"
    )
    
    # 配置LoRA
    logger.info("⚙️ 配置LoRA...")
    lora_config = LoraConfig(
        task_type=TaskType.CAUSAL_LM,
        target_modules=["q_proj", "k_proj", "v_proj", "o_proj"],
        inference_mode=False,
        r=16,
        lora_alpha=32,
        lora_dropout=0.1,
        bias="none",
    )
    
    # 应用LoRA
    model = get_peft_model(model, lora_config)
    model.print_trainable_parameters()
    
    # 训练参数
    training_args = TrainingArguments(
        output_dir=output_path,
        per_device_train_batch_size=1,
        gradient_accumulation_steps=4,
        num_train_epochs=2,
        learning_rate=1e-4,
        logging_steps=5,
        save_steps=50,
        save_strategy="steps",
        save_total_limit=2,
        remove_unused_columns=False,
        bf16=True,
        dataloader_num_workers=0,
        report_to="none",
    )
    
    # 数据整理器
    data_collator = DataCollatorForLanguageModeling(
        tokenizer=tokenizer,
        mlm=False,
    )
    
    # 创建训练器
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=tokenized_dataset,
        tokenizer=tokenizer,
        data_collator=data_collator,
    )
    
    # 开始训练
    logger.info("🎯 开始LoRA训练...")
    trainer.train()
    
    # 保存模型
    logger.info("💾 保存LoRA权重...")
    trainer.save_model()
    tokenizer.save_pretrained(output_path)
    
    logger.info("🎉 LoRA微调完成!")
    logger.info(f"📁 模型保存在: {output_path}")
    
    # 验证保存的文件
    saved_files = list(Path(output_path).glob("*"))
    logger.info(f"📋 保存的文件: {[f.name for f in saved_files]}")

if __name__ == "__main__":
    main()
