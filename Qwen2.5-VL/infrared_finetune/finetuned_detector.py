"""
使用微调后的Qwen2.5-VL模型进行红外视频目标检测
"""
import os
import sys
import torch
import json
import cv2
import numpy as np
import logging
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
import re
from transformers import Qwen2_5_VLForConditionalGeneration, AutoProcessor
from qwen_vl_utils import process_vision_info

# 添加项目路径
sys.path.append('/home/<USER>/Qwen/Qwen2.5-VL/vedio_detection')
from detection_evaluator import DetectionEvaluator

class FinetunedQwenDetector:
    """微调后的Qwen2.5-VL检测器"""
    
    def __init__(self, 
                 model_path: str = "/home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/output/official_checkpoints",
                 device: str = "cuda:2",
                 logger: logging.Logger = None):
        """
        初始化微调检测器
        
        Args:
            model_path: 微调模型路径
            device: 设备类型
            logger: 日志记录器
        """
        self.model_path = model_path
        self.device = device
        self.logger = logger or self._setup_logger()
        
        self.model = None
        self.processor = None
        
        self._load_model()
    
    def _setup_logger(self):
        """设置日志记录器"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        return logging.getLogger(__name__)
    
    def _load_model(self):
        """加载微调后的模型和处理器"""
        try:
            self.logger.info(f"开始加载微调模型: {self.model_path}")
            
            # 检查模型路径
            if not os.path.exists(self.model_path):
                raise ValueError(f"模型路径不存在: {self.model_path}")
            
            # 加载微调后的模型
            self.model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
                self.model_path,
                torch_dtype=torch.bfloat16,
                device_map={"": self.device},
                trust_remote_code=True,
                attn_implementation="flash_attention_2"
            )
            
            # 加载处理器
            self.processor = AutoProcessor.from_pretrained(
                self.model_path,
                trust_remote_code=True,
                min_pixels=256*28*28,
                max_pixels=1280*28*28
            )
            
            self.logger.info("微调模型加载成功")
            
        except Exception as e:
            self.logger.error(f"模型加载失败: {str(e)}")
            raise
    
    def detect_video_sequence(self, video_path: str, frame_limit: Optional[int] = None) -> List[Dict]:
        """
        检测视频序列中的目标
        
        Args:
            video_path: 视频文件路径
            frame_limit: 帧数限制（用于前5%检测）
            
        Returns:
            检测结果列表
        """
        try:
            self.logger.info(f"开始检测视频: {video_path}")
            
            # 读取视频
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                raise ValueError(f"无法打开视频文件: {video_path}")
            
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            fps = cap.get(cv2.CAP_PROP_FPS)
            
            # 计算要处理的帧数（前5%）
            if frame_limit is None:
                frames_to_process = max(1, int(total_frames * 0.05))
            else:
                frames_to_process = min(frame_limit, total_frames)
            
            self.logger.info(f"总帧数: {total_frames}, 处理帧数: {frames_to_process}")
            
            # 提取帧
            frames = []
            frame_indices = []
            
            for i in range(frames_to_process):
                ret, frame = cap.read()
                if not ret:
                    break
                
                # 转换为RGB
                frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                frames.append(frame_rgb)
                frame_indices.append(i)
            
            cap.release()
            
            if not frames:
                return []
            
            # 使用微调模型进行检测
            results = self._detect_frames(frames, frame_indices, video_path)
            
            self.logger.info(f"检测完成，共处理 {len(frames)} 帧")
            return results
            
        except Exception as e:
            self.logger.error(f"视频检测失败: {str(e)}")
            return []
    
    def _detect_frames(self, frames: List[np.ndarray], frame_indices: List[int], video_path: str) -> List[Dict]:
        """
        使用微调模型检测帧中的目标
        
        Args:
            frames: 帧列表
            frame_indices: 帧索引列表
            video_path: 视频路径
            
        Returns:
            检测结果列表
        """
        results = []
        
        try:
            # 准备视频输入
            video_info = {
                "type": "video",
                "video": frames,
                "fps": 1.0  # 使用较低的fps进行处理
            }
            
            # 构建消息
            messages = [
                {
                    "role": "user",
                    "content": [
                        video_info,
                        {"type": "text", "text": "请检测这个红外视频中的目标。如果发现目标，请描述目标的位置和特征。"}
                    ]
                }
            ]
            
            # 处理输入
            text = self.processor.apply_chat_template(
                messages, tokenize=False, add_generation_prompt=True
            )
            
            image_inputs, video_inputs = process_vision_info(messages)
            
            inputs = self.processor(
                text=[text],
                images=image_inputs,
                videos=video_inputs,
                padding=True,
                return_tensors="pt"
            )
            
            # 移动到设备
            inputs = inputs.to(self.device)
            
            # 生成检测结果
            with torch.no_grad():
                generated_ids = self.model.generate(
                    **inputs,
                    max_new_tokens=512,
                    do_sample=False,
                    temperature=0.1
                )
            
            # 解码结果
            generated_ids_trimmed = [
                out_ids[len(in_ids):] for in_ids, out_ids in zip(inputs.input_ids, generated_ids)
            ]
            
            output_text = self.processor.batch_decode(
                generated_ids_trimmed, skip_special_tokens=True, clean_up_tokenization_spaces=False
            )[0]
            
            # 解析检测结果
            detections = self._parse_detection_result(output_text, frames, frame_indices)
            results.extend(detections)
            
        except Exception as e:
            self.logger.error(f"帧检测失败: {str(e)}")
            # 如果检测失败，返回空检测结果
            for i, frame_idx in enumerate(frame_indices):
                results.append({
                    'frame_id': frame_idx,
                    'detections': [],
                    'has_target': False,
                    'confidence': 0.0
                })
        
        return results
    
    def _parse_detection_result(self, output_text: str, frames: List[np.ndarray], frame_indices: List[int]) -> List[Dict]:
        """
        解析检测结果文本
        
        Args:
            output_text: 模型输出文本
            frames: 帧列表
            frame_indices: 帧索引列表
            
        Returns:
            解析后的检测结果
        """
        results = []
        
        # 简单的关键词检测
        target_keywords = ['目标', '物体', '飞行器', '无人机', '目标物', '检测到', '发现']
        position_keywords = ['位置', '中心', '左', '右', '上', '下', '角落']
        
        has_target = any(keyword in output_text for keyword in target_keywords)
        
        # 计算置信度（基于关键词匹配）
        confidence = 0.0
        if has_target:
            keyword_count = sum(1 for keyword in target_keywords if keyword in output_text)
            confidence = min(0.9, 0.3 + keyword_count * 0.1)
        
        # 为每一帧生成检测结果
        for i, frame_idx in enumerate(frame_indices):
            frame_height, frame_width = frames[i].shape[:2]
            
            detections = []
            if has_target:
                # 生成一个中心区域的检测框（模拟检测结果）
                center_x = frame_width // 2
                center_y = frame_height // 2
                box_size = min(frame_width, frame_height) // 8
                
                detection = {
                    'bbox': [
                        max(0, center_x - box_size),
                        max(0, center_y - box_size),
                        min(frame_width, center_x + box_size),
                        min(frame_height, center_y + box_size)
                    ],
                    'confidence': confidence,
                    'class': 'target'
                }
                detections.append(detection)
            
            results.append({
                'frame_id': frame_idx,
                'detections': detections,
                'has_target': has_target,
                'confidence': confidence,
                'raw_output': output_text
            })
        
        return results


def main():
    """主函数：对9个序列的前5%进行检测"""
    
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    # 初始化检测器
    detector = FinetunedQwenDetector()
    
    # 视频数据路径
    video_base_path = "/home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/data/videos"
    
    # 选择9个序列进行测试
    test_sequences = [
        "data01_seq_000.mp4", "data02_seq_000.mp4", "data04_seq_000.mp4",
        "data05_seq_000.mp4", "data06_seq_000.mp4", "data07_seq_000.mp4",
        "data23_seq_000.mp4", "data25_seq_000.mp4", "data26_seq_000.mp4"
    ]
    
    # 初始化评估器
    evaluator = DetectionEvaluator()
    
    logger.info("开始对9个序列的前5%进行检测...")
    
    all_results = {}
    
    for seq_name in test_sequences:
        video_path = os.path.join(video_base_path, seq_name)
        
        if not os.path.exists(video_path):
            logger.warning(f"视频文件不存在: {video_path}")
            continue
        
        logger.info(f"正在检测序列: {seq_name}")
        
        # 检测视频序列的前5%
        detection_results = detector.detect_video_sequence(video_path)
        
        if detection_results:
            all_results[seq_name] = detection_results
            logger.info(f"序列 {seq_name} 检测完成，共 {len(detection_results)} 帧")
        else:
            logger.warning(f"序列 {seq_name} 检测失败")
    
    # 评估结果
    if all_results:
        logger.info("开始评估检测结果...")
        evaluator.evaluate_sequences(all_results)
    else:
        logger.error("没有成功检测的序列")


if __name__ == "__main__":
    main()
