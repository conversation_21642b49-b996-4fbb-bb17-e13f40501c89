#!/usr/bin/env python3
"""
快速修复视频路径脚本
"""

import json
import os

def main():
    print("🔧 开始修复视频路径...")
    
    # 文件路径
    train_file = "/home/<USER>/Qwen/Qwen2.5-VL/LLaMA-Factory/data/infrared_detection_train.json"
    video_base_path = "/home/<USER>/Qwen/Qwen2.5-VL/LLaMA-Factory/data/videos"
    
    # 检查文件是否存在
    if not os.path.exists(train_file):
        print(f"❌ 训练数据文件不存在: {train_file}")
        return
    
    # 读取训练数据
    with open(train_file, 'r', encoding='utf-8') as f:
        train_data = json.load(f)
    
    # 修复路径
    fixed_count = 0
    for item in train_data:
        if 'videos' in item:
            new_videos = []
            for video_path in item['videos']:
                if video_path.startswith('videos/'):
                    video_name = video_path.replace('videos/', '')
                    absolute_path = os.path.join(video_base_path, video_name)
                    new_videos.append(absolute_path)
                    fixed_count += 1
                else:
                    new_videos.append(video_path)
            item['videos'] = new_videos
    
    # 保存修复后的数据
    with open(train_file, 'w', encoding='utf-8') as f:
        json.dump(train_data, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 路径修复完成，修复了 {fixed_count} 个路径")
    
    # 验证修复结果
    sample_video = train_data[0]['videos'][0]
    exists = os.path.exists(sample_video)
    print(f"📁 示例视频路径: {sample_video}")
    print(f"✅ 文件存在: {exists}")
    
    if exists:
        print("\n🚀 现在可以运行训练了:")
        print("   ./train.sh")
    else:
        print("\n⚠️ 视频文件不存在，请检查视频目录")

if __name__ == "__main__":
    main()
