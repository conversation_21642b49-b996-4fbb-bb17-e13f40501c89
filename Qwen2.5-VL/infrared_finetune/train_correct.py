"""
红外视频LoRA微调 - 正确的实现
解决张量形状问题，确保grid_thw计算正确
"""

import os
import json
import logging
import torch
import math
from pathlib import Path
from transformers import (
    AutoProcessor,
    Qwen2_5_VLForConditionalGeneration,
    TrainingArguments,
    Trainer
)
from peft import LoraConfig, get_peft_model, TaskType
from torch.utils.data import Dataset
from decord import VideoReader
from PIL import Image

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class VideoDataset(Dataset):
    """视频数据集 - 确保正确的张量形状"""
    
    def __init__(self, data_list, processor, fixed_frames=5):
        self.data_list = data_list
        self.processor = processor
        self.fixed_frames = fixed_frames
        
        # 设置tokenizer
        self.processor.tokenizer.padding_side = 'left'
        if not hasattr(self.processor.tokenizer, 'pad_token') or self.processor.tokenizer.pad_token is None:
            self.processor.tokenizer.pad_token = self.processor.tokenizer.eos_token
    
    def __len__(self):
        return len(self.data_list)
    
    def load_video_frames(self, video_path):
        """加载视频帧"""
        try:
            if not os.path.exists(video_path):
                logger.warning(f"视频不存在: {video_path}")
                return self._create_dummy_frames()
            
            vr = VideoReader(video_path, num_threads=1)
            total_frames = len(vr)
            
            if total_frames == 0:
                return self._create_dummy_frames()
            
            # 均匀采样固定数量的帧
            if total_frames <= self.fixed_frames:
                frame_indices = list(range(total_frames))
                # 填充到固定帧数
                while len(frame_indices) < self.fixed_frames:
                    frame_indices.append(frame_indices[-1])
            else:
                frame_indices = torch.linspace(0, total_frames - 1, self.fixed_frames).long()
            
            # 确保帧数正确
            frame_indices = frame_indices[:self.fixed_frames]
            
            # 加载帧
            frames = vr.get_batch(frame_indices).asnumpy()
            pil_frames = [Image.fromarray(frame) for frame in frames]
            
            # 确保帧数完全一致
            while len(pil_frames) < self.fixed_frames:
                pil_frames.append(pil_frames[-1])
            pil_frames = pil_frames[:self.fixed_frames]
            
            return pil_frames
            
        except Exception as e:
            logger.warning(f"视频加载失败 {video_path}: {e}")
            return self._create_dummy_frames()
    
    def _create_dummy_frames(self):
        """创建虚拟帧 - 确保生成有效的特征"""
        # 使用标准尺寸，确保processor输出有效的tokens
        frame = Image.new('RGB', (224, 224), 'black')
        return [frame] * self.fixed_frames
    
    def __getitem__(self, idx):
        """获取数据项"""
        item = self.data_list[idx]
        video_path = item['video']
        conversations = item['conversations']
        
        # 加载视频帧
        frames = self.load_video_frames(video_path)
        
        # 构建对话
        user_text = conversations[0]['value'].replace('<video>\n', '').strip()
        assistant_text = conversations[1]['value'].strip()
        
        messages = [
            {
                "role": "user",
                "content": [
                    {"type": "video", "video": video_path},
                    {"type": "text", "text": user_text}
                ]
            },
            {
                "role": "assistant",
                "content": assistant_text
            }
        ]
        
        try:
            # 使用processor处理
            text = self.processor.apply_chat_template(
                messages,
                tokenize=False,
                add_generation_prompt=False
            )
            
            inputs = self.processor(
                videos=frames,
                text=text,
                padding=False,
                truncation=True,
                max_length=2048,
                return_tensors="pt"
            )
            
            input_ids = inputs['input_ids'].squeeze(0)
            attention_mask = inputs['attention_mask'].squeeze(0)
            
            # 获取视频特征
            pixel_values = inputs.get('pixel_values_videos', 
                                    inputs.get('pixel_values', torch.zeros(1, 3, 224, 224)))
            
            # 处理不同维度的pixel_values
            if pixel_values.dim() == 5:  # [1, frames, channels, height, width]
                pixel_values = pixel_values.squeeze(0)
            elif pixel_values.dim() == 3:  # [channels, height, width]
                pixel_values = pixel_values.unsqueeze(0)
            
            # 计算grid_thw - 确保维度有效
            grid_thw = self._calculate_grid_thw(pixel_values)
            
            return {
                'input_ids': input_ids,
                'attention_mask': attention_mask,
                'labels': input_ids.clone(),
                'pixel_values_videos': pixel_values,
                'video_grid_thw': grid_thw
            }
            
        except Exception as e:
            logger.error(f"处理失败 {video_path}: {e}")
            return self._create_dummy_sample()
    
    def _calculate_grid_thw(self, pixel_values):
        """计算grid_thw - 确保所有维度都有效（>0）"""
        frames = self.fixed_frames
        
        if pixel_values.dim() == 2:
            # 2D tensor [num_tokens, features]
            num_tokens = pixel_values.shape[0]
            
            # 确保num_tokens有效
            if num_tokens <= 0:
                num_tokens = frames  # 最小值：每帧1个token
            
            # 确保能被frames整除
            tokens_per_frame = max(1, num_tokens // frames)
            
            # 计算h和w，确保都>=1
            h = max(1, int(math.sqrt(tokens_per_frame)))
            while h > 1 and tokens_per_frame % h != 0:
                h -= 1
            w = max(1, tokens_per_frame // h)
            
            # 最终验证
            calculated_tokens = frames * h * w
            if calculated_tokens != num_tokens:
                logger.warning(f"Grid计算调整: {num_tokens} -> {calculated_tokens}")
            
        elif pixel_values.dim() == 4:
            # 4D tensor [frames, channels, height, width]
            actual_frames = pixel_values.shape[0]
            height = pixel_values.shape[2]
            width = pixel_values.shape[3]
            
            # 使用实际的空间维度
            h = max(1, height // 16)  # 假设patch size为16
            w = max(1, width // 16)
            frames = max(1, actual_frames)
            
        else:
            # 其他情况使用默认值
            h, w = 14, 14  # 标准的14x14 patches
        
        # 确保所有维度都>0
        frames = max(1, frames)
        h = max(1, h)
        w = max(1, w)
        
        grid_thw = torch.tensor([[frames, h, w]], dtype=torch.long)
        logger.debug(f"Grid_thw: frames={frames}, h={h}, w={w}")
        
        return grid_thw
    
    def _create_dummy_sample(self):
        """创建虚拟样本"""
        dummy_ids = torch.tensor([151643] * 50)  # 使用合理的长度
        dummy_frames = torch.zeros(self.fixed_frames, 3, 224, 224)
        dummy_grid = torch.tensor([[self.fixed_frames, 14, 14]], dtype=torch.long)
        
        return {
            'input_ids': dummy_ids,
            'attention_mask': torch.ones_like(dummy_ids),
            'labels': dummy_ids.clone(),
            'pixel_values_videos': dummy_frames,
            'video_grid_thw': dummy_grid
        }

def collate_fn(batch):
    """数据整理函数 - 确保批次一致性"""
    batch_size = len(batch)
    
    if batch_size == 0:
        raise ValueError("Empty batch")
    
    # 提取数据
    input_ids = [item['input_ids'] for item in batch]
    attention_masks = [item['attention_mask'] for item in batch]
    labels = [item['labels'] for item in batch]
    pixel_values_videos = [item['pixel_values_videos'] for item in batch]
    video_grid_thws = [item['video_grid_thw'] for item in batch]
    
    # 处理文本数据填充
    max_length = max(len(ids) for ids in input_ids)
    pad_token_id = 151643
    
    padded_input_ids = []
    padded_attention_masks = []
    padded_labels = []
    
    for i in range(batch_size):
        pad_length = max_length - len(input_ids[i])
        
        # 左填充
        padded_input_ids.append(torch.cat([
            torch.full((pad_length,), pad_token_id, dtype=input_ids[i].dtype),
            input_ids[i]
        ]))
        
        padded_attention_masks.append(torch.cat([
            torch.zeros(pad_length, dtype=attention_masks[i].dtype),
            attention_masks[i]
        ]))
        
        padded_labels.append(torch.cat([
            torch.full((pad_length,), -100, dtype=labels[i].dtype),
            labels[i]
        ]))
    
    # 处理视频数据 - 确保形状一致
    video_shapes = [pv.shape for pv in pixel_values_videos]
    if len(set(video_shapes)) > 1:
        logger.warning(f"检测到不同的视频形状: {video_shapes}")
        # 统一到最小的形状
        min_shape = min(video_shapes, key=lambda x: x[0] if len(x) > 0 else 0)
        
        unified_videos = []
        unified_grids = []
        
        for pv, grid in zip(pixel_values_videos, video_grid_thws):
            if pv.shape != min_shape:
                if pv.dim() == 2 and len(min_shape) == 2:
                    # 截断到最小形状
                    pv = pv[:min_shape[0], :min_shape[1]]
                elif pv.dim() == 4 and len(min_shape) == 4:
                    pv = pv[:min_shape[0], :min_shape[1], :min_shape[2], :min_shape[3]]
            
            unified_videos.append(pv)
            unified_grids.append(grid)
        
        pixel_values_videos = unified_videos
        video_grid_thws = unified_grids
    
    return {
        'input_ids': torch.stack(padded_input_ids),
        'attention_mask': torch.stack(padded_attention_masks),
        'labels': torch.stack(padded_labels),
        'pixel_values_videos': torch.stack(pixel_values_videos),
        'video_grid_thw': torch.cat(video_grid_thws, dim=0)
    }

def main():
    """主函数"""
    logger.info("🚀 开始红外视频LoRA微调...")
    
    # 配置
    model_path = "/home/<USER>/Qwen/Qwen2.5-VL/Qwen2.5-VL-7B-Instruct"
    data_path = "data/infrared_video_train.json"
    output_path = "output/video_lora"
    
    Path(output_path).mkdir(parents=True, exist_ok=True)
    
    # 加载数据
    logger.info("📊 加载数据...")
    with open(data_path, 'r') as f:
        train_data = json.load(f)
    
    # 限制数据量用于测试
    train_data = train_data[:50]
    logger.info(f"✅ 数据: {len(train_data)} 个样本")
    
    # 加载processor和模型
    logger.info("🔤 加载processor...")
    processor = AutoProcessor.from_pretrained(model_path, trust_remote_code=True)
    
    logger.info("🤖 加载模型...")
    model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
        model_path,
        torch_dtype=torch.bfloat16,
        device_map={"": "cuda:0"},
        trust_remote_code=True,
        attn_implementation="flash_attention_2"
    )
    
    # LoRA配置
    logger.info("⚙️ 配置LoRA...")
    lora_config = LoraConfig(
        task_type=TaskType.CAUSAL_LM,
        target_modules=["q_proj", "k_proj", "v_proj", "o_proj"],
        inference_mode=False,
        r=32,
        lora_alpha=32,
        lora_dropout=0.1,
        bias="none",
    )
    
    model = get_peft_model(model, lora_config)
    model.print_trainable_parameters()
    
    # 创建数据集
    dataset = VideoDataset(train_data, processor, fixed_frames=5)
    
    # 训练参数
    training_args = TrainingArguments(
        output_dir=output_path,
        per_device_train_batch_size=1,  # 小批次避免内存问题
        gradient_accumulation_steps=16,
        num_train_epochs=1,
        learning_rate=1e-5,
        weight_decay=0.01,
        warmup_ratio=0.1,
        logging_steps=1,
        save_steps=25,
        save_strategy="steps",
        save_total_limit=2,
        remove_unused_columns=False,
        bf16=True,
        dataloader_num_workers=0,
        gradient_checkpointing=True,
        report_to="none",
        dataloader_drop_last=True,
    )
    
    # 创建训练器
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=dataset,
        data_collator=collate_fn,
    )
    
    # 开始训练
    logger.info("🎯 开始训练...")
    trainer.train()
    
    # 保存模型
    logger.info("💾 保存模型...")
    trainer.save_model()
    
    logger.info("✅ 训练完成!")

if __name__ == "__main__":
    main()
