"""
红外视频目标检测完整流水线
包含数据处理、微调、检测、评估四个模块
"""

import os
import sys
import logging
import argparse
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(str(Path(__file__).parent))

from data_processor_correct import InfraredVideoProcessor
from train_correct import main as train_main
from detect_correct import main as detect_main
from evaluate_correct import main as evaluate_main

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def run_data_processing():
    """运行数据处理模块"""
    logger.info("🔄 开始数据处理...")
    
    processor = InfraredVideoProcessor(
        base_images_dir="/home/<USER>/Qwen/Qwen2.5-VL/tiaozhanbei_datasets/images",
        base_labels_dir="/home/<USER>/Qwen/Qwen2.5-VL/tiaozhanbei_datasets/labels",
        output_dir="data"
    )
    
    # 处理所有序列
    all_data = processor.process_all_sequences()
    
    logger.info("✅ 数据处理完成!")
    logger.info(f"训练样本: {len(all_data['train'])}")
    logger.info(f"测试样本: {len(all_data['test'])}")
    
    return True

def run_training():
    """运行微调模块"""
    logger.info("🔄 开始模型微调...")
    
    # 检查数据文件是否存在
    data_file = Path("data/infrared_video_train.json")
    if not data_file.exists():
        logger.error(f"训练数据不存在: {data_file}")
        return False
    
    try:
        # 调用训练主函数
        train_main()
        logger.info("✅ 模型微调完成!")
        return True
    except Exception as e:
        logger.error(f"微调失败: {e}")
        return False

def run_detection():
    """运行检测模块"""
    logger.info("🔄 开始目标检测...")
    
    # 检查模型和测试数据是否存在
    model_dir = Path("output/video_lora")
    test_data = Path("data/infrared_video_test.json")
    
    if not model_dir.exists():
        logger.error(f"微调模型不存在: {model_dir}")
        return False
    
    if not test_data.exists():
        logger.error(f"测试数据不存在: {test_data}")
        return False
    
    try:
        # 调用检测主函数
        detect_main()
        logger.info("✅ 目标检测完成!")
        return True
    except Exception as e:
        logger.error(f"检测失败: {e}")
        return False

def run_evaluation():
    """运行评估模块"""
    logger.info("🔄 开始结果评估...")
    
    # 检查检测结果是否存在
    results_file = Path("results/detection_results.json")
    if not results_file.exists():
        logger.error(f"检测结果不存在: {results_file}")
        return False
    
    try:
        # 调用评估主函数
        evaluate_main()
        logger.info("✅ 结果评估完成!")
        return True
    except Exception as e:
        logger.error(f"评估失败: {e}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="红外视频目标检测流水线")
    parser.add_argument(
        "--steps",
        nargs="+",
        choices=["data", "train", "detect", "evaluate", "all"],
        default=["all"],
        help="要执行的步骤"
    )
    parser.add_argument(
        "--skip-existing",
        action="store_true",
        help="跳过已存在的结果"
    )
    
    args = parser.parse_args()
    
    # 确定要执行的步骤
    if "all" in args.steps:
        steps_to_run = ["data", "train", "detect", "evaluate"]
    else:
        steps_to_run = args.steps
    
    logger.info("🚀 开始红外视频目标检测流水线...")
    logger.info(f"执行步骤: {steps_to_run}")
    
    success_count = 0
    total_steps = len(steps_to_run)
    
    # 执行各个步骤
    for step in steps_to_run:
        logger.info(f"\n{'='*50}")
        logger.info(f"步骤 {success_count + 1}/{total_steps}: {step.upper()}")
        logger.info(f"{'='*50}")
        
        # 检查是否跳过已存在的结果
        if args.skip_existing:
            if step == "data" and Path("data/infrared_video_train.json").exists():
                logger.info("⏭️ 跳过数据处理（文件已存在）")
                success_count += 1
                continue
            elif step == "train" and Path("output/video_lora").exists():
                logger.info("⏭️ 跳过模型微调（模型已存在）")
                success_count += 1
                continue
            elif step == "detect" and Path("results/detection_results.json").exists():
                logger.info("⏭️ 跳过目标检测（结果已存在）")
                success_count += 1
                continue
            elif step == "evaluate" and Path("results/evaluation_report.txt").exists():
                logger.info("⏭️ 跳过结果评估（报告已存在）")
                success_count += 1
                continue
        
        # 执行步骤
        if step == "data":
            success = run_data_processing()
        elif step == "train":
            success = run_training()
        elif step == "detect":
            success = run_detection()
        elif step == "evaluate":
            success = run_evaluation()
        else:
            logger.error(f"未知步骤: {step}")
            success = False
        
        if success:
            success_count += 1
            logger.info(f"✅ 步骤 {step} 完成")
        else:
            logger.error(f"❌ 步骤 {step} 失败")
            break
    
    # 总结
    logger.info(f"\n{'='*50}")
    logger.info("流水线执行总结")
    logger.info(f"{'='*50}")
    logger.info(f"成功完成: {success_count}/{total_steps} 个步骤")
    
    if success_count == total_steps:
        logger.info("🎉 所有步骤执行成功!")
        
        # 显示最终结果
        if Path("results/evaluation_report.txt").exists():
            logger.info("\n📊 最终评估结果:")
            with open("results/evaluation_report.txt", 'r', encoding='utf-8') as f:
                lines = f.readlines()
                for line in lines[:15]:  # 显示前15行
                    print(line.rstrip())
    else:
        logger.error("❌ 流水线执行失败")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
