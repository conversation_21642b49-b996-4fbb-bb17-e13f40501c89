{"best_global_step": null, "best_metric": null, "best_model_checkpoint": null, "epoch": 1.8, "eval_steps": 500, "global_step": 5, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.4, "grad_norm": 0.40257182717323303, "learning_rate": 0.0001, "loss": 1.234, "step": 1}, {"epoch": 0.8, "grad_norm": 0.4034430682659149, "learning_rate": 8e-05, "loss": 1.2273, "step": 2}, {"epoch": 1.0, "grad_norm": 0.2189345806837082, "learning_rate": 6e-05, "loss": 0.6076, "step": 3}, {"epoch": 1.4, "grad_norm": 0.4270309507846832, "learning_rate": 4e-05, "loss": 1.1959, "step": 4}, {"epoch": 1.8, "grad_norm": 0.41257816553115845, "learning_rate": 2e-05, "loss": 1.1883, "step": 5}], "logging_steps": 1, "max_steps": 5, "num_input_tokens_seen": 0, "num_train_epochs": 3, "save_steps": 10, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": true}, "attributes": {}}}, "total_flos": 214333255581696.0, "train_batch_size": 1, "trial_name": null, "trial_params": null}