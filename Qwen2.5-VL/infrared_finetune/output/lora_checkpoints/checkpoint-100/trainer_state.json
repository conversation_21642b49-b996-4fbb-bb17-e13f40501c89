{"best_global_step": null, "best_metric": null, "best_model_checkpoint": null, "epoch": 1.8952380952380952, "eval_steps": 500, "global_step": 100, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.09523809523809523, "grad_norm": 0.20605787634849548, "learning_rate": 9.615384615384617e-05, "loss": 1.3298, "step": 5}, {"epoch": 0.19047619047619047, "grad_norm": 0.29210612177848816, "learning_rate": 9.134615384615385e-05, "loss": 1.0581, "step": 10}, {"epoch": 0.2857142857142857, "grad_norm": 0.29912257194519043, "learning_rate": 8.653846153846155e-05, "loss": 0.9235, "step": 15}, {"epoch": 0.38095238095238093, "grad_norm": 0.32693150639533997, "learning_rate": 8.173076923076923e-05, "loss": 0.6574, "step": 20}, {"epoch": 0.47619047619047616, "grad_norm": 0.36943602561950684, "learning_rate": 7.692307692307693e-05, "loss": 0.5001, "step": 25}, {"epoch": 0.5714285714285714, "grad_norm": 0.48518893122673035, "learning_rate": 7.211538461538462e-05, "loss": 0.269, "step": 30}, {"epoch": 0.6666666666666666, "grad_norm": 0.2737029194831848, "learning_rate": 6.730769230769232e-05, "loss": 0.1354, "step": 35}, {"epoch": 0.7619047619047619, "grad_norm": 0.20361585915088654, "learning_rate": 6.25e-05, "loss": 0.1073, "step": 40}, {"epoch": 0.8571428571428571, "grad_norm": 0.1384253054857254, "learning_rate": 5.769230769230769e-05, "loss": 0.0848, "step": 45}, {"epoch": 0.9523809523809523, "grad_norm": 0.09210879355669022, "learning_rate": 5.288461538461539e-05, "loss": 0.0776, "step": 50}, {"epoch": 1.0380952380952382, "grad_norm": 0.04862374812364578, "learning_rate": 4.8076923076923084e-05, "loss": 0.0554, "step": 55}, {"epoch": 1.1333333333333333, "grad_norm": 0.06638514995574951, "learning_rate": 4.326923076923077e-05, "loss": 0.0632, "step": 60}, {"epoch": 1.2285714285714286, "grad_norm": 0.05528070777654648, "learning_rate": 3.846153846153846e-05, "loss": 0.0597, "step": 65}, {"epoch": 1.3238095238095238, "grad_norm": 0.06786138564348221, "learning_rate": 3.365384615384616e-05, "loss": 0.072, "step": 70}, {"epoch": 1.4190476190476191, "grad_norm": 0.06363685429096222, "learning_rate": 2.8846153846153845e-05, "loss": 0.0599, "step": 75}, {"epoch": 1.5142857142857142, "grad_norm": 0.13693946599960327, "learning_rate": 2.4038461538461542e-05, "loss": 0.0415, "step": 80}, {"epoch": 1.6095238095238096, "grad_norm": 0.061040956526994705, "learning_rate": 1.923076923076923e-05, "loss": 0.0399, "step": 85}, {"epoch": 1.704761904761905, "grad_norm": 0.10097029060125351, "learning_rate": 1.4423076923076923e-05, "loss": 0.0759, "step": 90}, {"epoch": 1.8, "grad_norm": 0.11288197338581085, "learning_rate": 9.615384615384616e-06, "loss": 0.0535, "step": 95}, {"epoch": 1.8952380952380952, "grad_norm": 0.07155220955610275, "learning_rate": 4.807692307692308e-06, "loss": 0.0486, "step": 100}], "logging_steps": 5, "max_steps": 104, "num_input_tokens_seen": 0, "num_train_epochs": 2, "save_steps": 50, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 9000002436784128.0, "train_batch_size": 1, "trial_name": null, "trial_params": null}