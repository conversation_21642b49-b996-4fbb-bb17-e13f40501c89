{"best_global_step": null, "best_metric": null, "best_model_checkpoint": null, "epoch": 1.8952380952380952, "eval_steps": 500, "global_step": 100, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.09523809523809523, "grad_norm": 0.2856425344944, "learning_rate": 9.615384615384617e-05, "loss": 1.3294, "step": 5}, {"epoch": 0.19047619047619047, "grad_norm": 0.3929438889026642, "learning_rate": 9.134615384615385e-05, "loss": 1.0582, "step": 10}, {"epoch": 0.2857142857142857, "grad_norm": 0.42077305912971497, "learning_rate": 8.653846153846155e-05, "loss": 0.9246, "step": 15}, {"epoch": 0.38095238095238093, "grad_norm": 0.4582614004611969, "learning_rate": 8.173076923076923e-05, "loss": 0.6577, "step": 20}, {"epoch": 0.47619047619047616, "grad_norm": 0.5175191164016724, "learning_rate": 7.692307692307693e-05, "loss": 0.502, "step": 25}, {"epoch": 0.5714285714285714, "grad_norm": 0.6871837377548218, "learning_rate": 7.211538461538462e-05, "loss": 0.2697, "step": 30}, {"epoch": 0.6666666666666666, "grad_norm": 0.36327752470970154, "learning_rate": 6.730769230769232e-05, "loss": 0.1347, "step": 35}, {"epoch": 0.7619047619047619, "grad_norm": 0.25231438875198364, "learning_rate": 6.25e-05, "loss": 0.1066, "step": 40}, {"epoch": 0.8571428571428571, "grad_norm": 0.17332115769386292, "learning_rate": 5.769230769230769e-05, "loss": 0.0848, "step": 45}, {"epoch": 0.9523809523809523, "grad_norm": 0.15601646900177002, "learning_rate": 5.288461538461539e-05, "loss": 0.0775, "step": 50}, {"epoch": 1.0380952380952382, "grad_norm": 0.07156571745872498, "learning_rate": 4.8076923076923084e-05, "loss": 0.0548, "step": 55}, {"epoch": 1.1333333333333333, "grad_norm": 0.10356737673282623, "learning_rate": 4.326923076923077e-05, "loss": 0.0631, "step": 60}, {"epoch": 1.2285714285714286, "grad_norm": 0.08539614826440811, "learning_rate": 3.846153846153846e-05, "loss": 0.0592, "step": 65}, {"epoch": 1.3238095238095238, "grad_norm": 0.1700679063796997, "learning_rate": 3.365384615384616e-05, "loss": 0.0717, "step": 70}, {"epoch": 1.4190476190476191, "grad_norm": 0.093693308532238, "learning_rate": 2.8846153846153845e-05, "loss": 0.06, "step": 75}, {"epoch": 1.5142857142857142, "grad_norm": 0.16068731248378754, "learning_rate": 2.4038461538461542e-05, "loss": 0.041, "step": 80}, {"epoch": 1.6095238095238096, "grad_norm": 0.08652258664369583, "learning_rate": 1.923076923076923e-05, "loss": 0.0394, "step": 85}, {"epoch": 1.704761904761905, "grad_norm": 0.15005245804786682, "learning_rate": 1.4423076923076923e-05, "loss": 0.0756, "step": 90}, {"epoch": 1.8, "grad_norm": 0.15845853090286255, "learning_rate": 9.615384615384616e-06, "loss": 0.0536, "step": 95}, {"epoch": 1.8952380952380952, "grad_norm": 0.10584152489900589, "learning_rate": 4.807692307692308e-06, "loss": 0.0483, "step": 100}], "logging_steps": 5, "max_steps": 104, "num_input_tokens_seen": 0, "num_train_epochs": 2, "save_steps": 50, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 8988308246421504.0, "train_batch_size": 1, "trial_name": null, "trial_params": null}