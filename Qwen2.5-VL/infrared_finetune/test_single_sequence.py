"""
测试单个序列的微调模型检测
"""
import os
import sys
import json
import cv2
import numpy as np
import logging
import torch
from typing import List, Dict, Any, Tuple
from pathlib import Path
from transformers import Qwen2_5_VLForConditionalGeneration, AutoProcessor
from qwen_vl_utils import process_vision_info
from PIL import Image

class SingleSequenceTester:
    """单序列测试器"""
    
    def __init__(self):
        self.logger = self._setup_logger()

        # 设置CUDA环境
        os.environ["CUDA_VISIBLE_DEVICES"] = "2"
        self.device = "cuda:0"  # 因为只有GPU 2可见，所以使用cuda:0

        # 加载微调后的模型
        self.model = None
        self.processor = None
        self._load_model()
    
    def _setup_logger(self):
        """设置日志记录器"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        return logging.getLogger(__name__)
    
    def _load_model(self):
        """加载微调后的模型"""
        try:
            model_path = "/home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/output/official_checkpoints"
            
            self.logger.info(f"开始加载微调模型: {model_path}")
            
            # 检查模型路径是否存在
            if not os.path.exists(model_path):
                raise ValueError(f"模型路径不存在: {model_path}")
            
            # 加载微调后的模型
            self.model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
                model_path,
                torch_dtype=torch.bfloat16,
                device_map={"": self.device},
                trust_remote_code=True,
                attn_implementation="flash_attention_2"
            )
            
            # 加载处理器
            self.processor = AutoProcessor.from_pretrained(
                model_path,
                trust_remote_code=True,
                min_pixels=256*28*28,
                max_pixels=1280*28*28
            )

            # 如果微调模型的处理器没有chat_template，从原始模型复制
            if not hasattr(self.processor, 'chat_template') or self.processor.chat_template is None:
                self.logger.info("微调模型缺少chat_template，从原始模型复制...")
                base_processor = AutoProcessor.from_pretrained(
                    "/home/<USER>/Qwen/Qwen2.5-VL/Qwen2.5-VL-7B-Instruct",
                    trust_remote_code=True
                )
                if hasattr(base_processor, 'chat_template'):
                    self.processor.chat_template = base_processor.chat_template
                    self.logger.info("chat_template复制成功")

            self.logger.info("微调模型加载成功")
            
        except Exception as e:
            self.logger.error(f"微调模型加载失败: {str(e)}")
            self.logger.info("尝试加载原始模型...")
            self._load_base_model()
    
    def _load_base_model(self):
        """加载原始基础模型"""
        try:
            base_model_path = "/home/<USER>/Qwen/Qwen2.5-VL/Qwen2.5-VL-7B-Instruct"
            
            self.model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
                base_model_path,
                torch_dtype=torch.bfloat16,
                device_map={"": self.device},
                trust_remote_code=True,
                attn_implementation="flash_attention_2"
            )
            
            self.processor = AutoProcessor.from_pretrained(
                base_model_path,
                trust_remote_code=True,
                min_pixels=256*28*28,
                max_pixels=1280*28*28
            )
            
            self.logger.info("原始模型加载成功")
            
        except Exception as e:
            self.logger.error(f"原始模型加载也失败: {str(e)}")
            raise
    
    def test_single_frame(self, image_path: str) -> Dict:
        """测试单帧图像检测"""
        try:
            # 加载图像
            image = Image.open(image_path).convert('RGB')
            
            # 构建消息
            messages = [
                {
                    "role": "user",
                    "content": [
                        {"type": "image", "image": image},
                        {"type": "text", "text": "请仔细观察这张红外图像，检测是否有目标物体。如果发现目标，请描述目标的位置和特征。如果没有目标，请明确说明'无目标'。"}
                    ]
                }
            ]
            
            # 处理输入
            text = self.processor.apply_chat_template(
                messages, tokenize=False, add_generation_prompt=True
            )
            
            image_inputs, video_inputs = process_vision_info(messages)
            
            inputs = self.processor(
                text=[text],
                images=image_inputs,
                videos=video_inputs,
                padding=True,
                return_tensors="pt"
            )
            
            # 移动到设备
            inputs = inputs.to(self.device)
            
            # 生成检测结果
            with torch.no_grad():
                generated_ids = self.model.generate(
                    **inputs,
                    max_new_tokens=256,
                    do_sample=False,
                    temperature=0.1
                )
            
            # 解码结果
            generated_ids_trimmed = [
                out_ids[len(in_ids):] for in_ids, out_ids in zip(inputs.input_ids, generated_ids)
            ]
            
            output_text = self.processor.batch_decode(
                generated_ids_trimmed, skip_special_tokens=True, clean_up_tokenization_spaces=False
            )[0]
            
            # 解析检测结果
            has_target, confidence = self._parse_detection_output(output_text)
            
            return {
                'has_target': has_target,
                'confidence': confidence,
                'raw_output': output_text
            }
            
        except Exception as e:
            self.logger.error(f"单帧检测失败: {str(e)}")
            return {
                'has_target': False,
                'confidence': 0.0,
                'raw_output': f"检测失败: {str(e)}"
            }
    
    def _parse_detection_output(self, output_text: str) -> Tuple[bool, float]:
        """解析模型输出文本"""
        output_lower = output_text.lower()
        
        # 无目标的关键词
        no_target_keywords = ['无目标', '没有目标', '无', '没有', 'no target', 'no object', '空']
        
        # 有目标的关键词
        target_keywords = ['目标', '物体', '飞行器', '无人机', '检测到', '发现', '看到', 'target', 'object', 'detected']
        
        # 检查无目标关键词
        for keyword in no_target_keywords:
            if keyword in output_lower:
                return False, 0.0
        
        # 检查有目标关键词
        target_count = 0
        for keyword in target_keywords:
            if keyword in output_lower:
                target_count += 1
        
        if target_count > 0:
            confidence = min(0.9, 0.4 + target_count * 0.1)
            return True, confidence
        
        # 默认情况
        if len(output_text) > 20 and not any(kw in output_lower for kw in no_target_keywords):
            return True, 0.3
        
        return False, 0.0
    
    def test_video_frames(self, video_path: str, num_frames: int = 5) -> List[Dict]:
        """测试视频的前几帧"""
        try:
            self.logger.info(f"开始测试视频: {video_path}")
            
            # 读取视频
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                self.logger.error(f"无法打开视频文件: {video_path}")
                return []
            
            results = []
            
            for i in range(num_frames):
                ret, frame = cap.read()
                if not ret:
                    break
                
                # 转换为RGB
                frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                
                # 调整大小
                height, width = frame_rgb.shape[:2]
                if width > 640:
                    scale = 640 / width
                    new_width = 640
                    new_height = int(height * scale)
                    frame_rgb = cv2.resize(frame_rgb, (new_width, new_height))
                
                image = Image.fromarray(frame_rgb)
                
                # 检测单帧
                result = self._detect_single_image(image, i)
                results.append(result)
                
                self.logger.info(f"帧 {i}: {'有目标' if result['has_target'] else '无目标'} (置信度: {result['confidence']:.3f})")
            
            cap.release()
            return results
            
        except Exception as e:
            self.logger.error(f"视频测试失败: {str(e)}")
            return []
    
    def _detect_single_image(self, image: Image.Image, frame_idx: int) -> Dict:
        """检测单张图像"""
        try:
            # 构建消息
            messages = [
                {
                    "role": "user",
                    "content": [
                        {"type": "image", "image": image},
                        {"type": "text", "text": "请仔细观察这张红外图像，检测是否有目标物体。如果发现目标，请描述目标的位置和特征。如果没有目标，请明确说明'无目标'。"}
                    ]
                }
            ]
            
            # 处理输入
            text = self.processor.apply_chat_template(
                messages, tokenize=False, add_generation_prompt=True
            )
            
            image_inputs, video_inputs = process_vision_info(messages)
            
            inputs = self.processor(
                text=[text],
                images=image_inputs,
                videos=video_inputs,
                padding=True,
                return_tensors="pt"
            )
            
            # 移动到设备
            inputs = inputs.to(self.device)
            
            # 生成检测结果
            with torch.no_grad():
                generated_ids = self.model.generate(
                    **inputs,
                    max_new_tokens=256,
                    do_sample=False,
                    temperature=0.1
                )
            
            # 解码结果
            generated_ids_trimmed = [
                out_ids[len(in_ids):] for in_ids, out_ids in zip(inputs.input_ids, generated_ids)
            ]
            
            output_text = self.processor.batch_decode(
                generated_ids_trimmed, skip_special_tokens=True, clean_up_tokenization_spaces=False
            )[0]
            
            # 解析检测结果
            has_target, confidence = self._parse_detection_output(output_text)
            
            return {
                'frame_id': frame_idx,
                'has_target': has_target,
                'confidence': confidence,
                'raw_output': output_text
            }
            
        except Exception as e:
            self.logger.error(f"单图像检测失败 (帧{frame_idx}): {str(e)}")
            return {
                'frame_id': frame_idx,
                'has_target': False,
                'confidence': 0.0,
                'raw_output': f"检测失败: {str(e)}"
            }


def main():
    """主函数"""
    tester = SingleSequenceTester()
    
    # 测试单个视频的前5帧
    video_path = "/home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/data/videos/data01_seq_000.mp4"
    
    if os.path.exists(video_path):
        print(f"🚀 测试视频: {video_path}")
        results = tester.test_video_frames(video_path, num_frames=5)
        
        print("\n📊 检测结果:")
        for result in results:
            print(f"帧 {result['frame_id']}: {'✔ 有目标' if result['has_target'] else '✗ 无目标'} "
                  f"(置信度: {result['confidence']:.3f})")
            print(f"   输出: {result['raw_output'][:100]}...")
            print()
    else:
        print(f"❌ 视频文件不存在: {video_path}")


if __name__ == "__main__":
    main()
