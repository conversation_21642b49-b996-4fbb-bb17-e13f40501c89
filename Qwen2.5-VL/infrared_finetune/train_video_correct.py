"""
正确的视频微调训练脚本 - 真正处理视频数据
参考官方qwen-vl-finetune实现
"""

import os
import json
import logging
import torch
import numpy as np
from pathlib import Path
from datasets import Dataset
from transformers import (
    AutoProcessor,
    Qwen2_5_VLForConditionalGeneration,
    TrainingArguments,
    Trainer,
    DataCollatorForLanguageModeling
)
from peft import LoraConfig, get_peft_model, TaskType
from PIL import Image
import cv2

def setup_logger():
    """设置日志记录器"""
    logging.basicConfig(level=logging.INFO)
    return logging.getLogger(__name__)

def load_video_frames(video_path, max_frames=8):
    """加载视频帧"""
    try:
        cap = cv2.VideoCapture(video_path)
        frames = []
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        # 均匀采样帧
        if total_frames <= max_frames:
            frame_indices = list(range(total_frames))
        else:
            frame_indices = np.linspace(0, total_frames - 1, max_frames, dtype=int)
        
        for frame_idx in frame_indices:
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
            ret, frame = cap.read()
            if ret:
                # 转换BGR到RGB
                frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                frames.append(Image.fromarray(frame))
        
        cap.release()
        return frames
    except Exception as e:
        print(f"加载视频失败 {video_path}: {e}")
        return []

def main():
    """主函数"""
    logger = setup_logger()
    
    logger.info("🚀 开始红外视频目标检测LoRA微调（正确的视频处理版本）...")
    
    # 配置路径
    model_path = "/home/<USER>/Qwen/Qwen2.5-VL/Qwen2.5-VL-7B-Instruct"
    data_path = "data/infrared_video_train.json"
    output_path = "output/lora_checkpoints_video"
    
    # 创建输出目录
    Path(output_path).mkdir(parents=True, exist_ok=True)
    
    # 加载训练数据
    logger.info("📊 加载训练数据...")
    with open(data_path, 'r', encoding='utf-8') as f:
        train_data = json.load(f)
    
    logger.info(f"✅ 使用完整数据集: {len(train_data)} 个样本进行训练")
    
    # 加载processor
    logger.info("🔤 加载processor...")
    processor = AutoProcessor.from_pretrained(model_path, trust_remote_code=True)
    
    # 设置tokenizer的padding_side为left（Flash Attention要求）
    processor.tokenizer.padding_side = 'left'
    logger.info("✅ 设置tokenizer padding_side为left（Flash Attention要求）")
    
    # 预处理数据 - 真正处理视频
    logger.info("📝 预处理数据（包含视频处理）...")
    processed_data = []

    for i, item in enumerate(train_data):
        try:
            conversations = item['conversations']
            video_path = item['video']
            
            logger.info(f"处理视频 {i+1}/{len(train_data)}: {video_path}")
            
            # 加载视频帧
            video_frames = load_video_frames(video_path, max_frames=8)
            if not video_frames:
                logger.warning(f"跳过无效视频: {video_path}")
                continue
            
            # 构建消息格式（包含实际视频数据）
            messages = [
                {
                    "role": "user",
                    "content": [
                        {"type": "video", "video": video_frames},  # 传入实际的视频帧
                        {"type": "text", "text": conversations[0]['value'].replace('<video>\n', '')}
                    ]
                },
                {
                    "role": "assistant", 
                    "content": conversations[1]['value']
                }
            ]

            # 使用processor处理（包含视频数据）
            inputs = processor.apply_chat_template(
                messages, 
                tokenize=True, 
                add_generation_prompt=False,
                return_tensors="pt",
                return_dict=True
            )
            
            # 添加到处理后的数据
            processed_data.append({
                "input_ids": inputs["input_ids"].squeeze(),
                "attention_mask": inputs["attention_mask"].squeeze(),
                "pixel_values_videos": inputs.get("pixel_values_videos", None),
                "video_grid_thw": inputs.get("video_grid_thw", None),
                "labels": inputs["input_ids"].squeeze().clone()
            })

        except Exception as e:
            logger.warning(f"处理样本失败 {video_path}: {e}")
            continue
    
    logger.info(f"✅ 成功处理 {len(processed_data)} 个视频样本")
    
    # 创建数据集
    dataset = Dataset.from_list(processed_data)
    
    # 加载模型
    logger.info("🤖 加载模型...")
    model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
        model_path,
        torch_dtype=torch.bfloat16,
        device_map={"": "cuda:0"},
        trust_remote_code=True,
        attn_implementation="flash_attention_2"
    )
    
    # 配置LoRA
    logger.info("⚙️ 配置LoRA...")
    lora_config = LoraConfig(
        task_type=TaskType.CAUSAL_LM,
        target_modules=["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"],
        inference_mode=False,
        r=64,
        lora_alpha=64,
        lora_dropout=0.05,
        bias="none",
    )
    
    # 应用LoRA
    model = get_peft_model(model, lora_config)
    model.print_trainable_parameters()
    
    # 训练参数
    training_args = TrainingArguments(
        output_dir=output_path,
        per_device_train_batch_size=1,  # 视频数据较大，减小批次
        gradient_accumulation_steps=16,  # 增加梯度累积
        num_train_epochs=1,
        learning_rate=2e-5,
        weight_decay=0.01,
        warmup_ratio=0.03,
        max_grad_norm=1.0,
        lr_scheduler_type="cosine",
        logging_steps=5,
        save_steps=50,
        save_strategy="steps",
        save_total_limit=3,
        remove_unused_columns=False,
        bf16=True,
        dataloader_num_workers=2,  # 减少workers避免内存问题
        gradient_checkpointing=False,  # 关闭梯度检查点
        fp16_full_eval=False,
        optim="adamw_torch_fused",
        report_to="none",
    )
    
    # 自定义数据整理器
    def video_data_collator(features):
        """处理视频数据的数据整理器"""
        batch = {}
        
        # 处理文本数据
        input_ids = [f["input_ids"] for f in features]
        attention_mask = [f["attention_mask"] for f in features]
        labels = [f["labels"] for f in features]
        
        # Padding
        max_len = max(len(ids) for ids in input_ids)
        
        padded_input_ids = []
        padded_attention_mask = []
        padded_labels = []
        
        for ids, mask, label in zip(input_ids, attention_mask, labels):
            pad_len = max_len - len(ids)
            # 左填充
            padded_input_ids.append(torch.cat([torch.full((pad_len,), processor.tokenizer.pad_token_id), ids]))
            padded_attention_mask.append(torch.cat([torch.zeros(pad_len), mask]))
            padded_labels.append(torch.cat([torch.full((pad_len,), -100), label]))
        
        batch["input_ids"] = torch.stack(padded_input_ids)
        batch["attention_mask"] = torch.stack(padded_attention_mask)
        batch["labels"] = torch.stack(padded_labels)
        
        # 处理视频数据
        if "pixel_values_videos" in features[0] and features[0]["pixel_values_videos"] is not None:
            batch["pixel_values_videos"] = torch.stack([f["pixel_values_videos"] for f in features])
        
        if "video_grid_thw" in features[0] and features[0]["video_grid_thw"] is not None:
            batch["video_grid_thw"] = torch.stack([f["video_grid_thw"] for f in features])
        
        return batch
    
    # 创建训练器
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=dataset,
        data_collator=video_data_collator,
        processing_class=processor,
    )
    
    # 训练前最后一次确保padding_side设置正确
    trainer.processing_class.tokenizer.padding_side = 'left'
    logger.info("🔧 最终确认tokenizer padding_side设置为left")
    
    # 开始训练
    logger.info("🎯 开始LoRA训练（真正的视频微调）...")
    logger.info(f"📊 训练配置:")
    logger.info(f"   样本数: {len(dataset)}")
    logger.info(f"   批次大小: {training_args.per_device_train_batch_size}")
    logger.info(f"   梯度累积: {training_args.gradient_accumulation_steps}")
    logger.info(f"   有效批次大小: {training_args.per_device_train_batch_size * training_args.gradient_accumulation_steps}")
    logger.info(f"   学习率: {training_args.learning_rate}")
    logger.info(f"   训练轮数: {training_args.num_train_epochs}")
    
    trainer.train()
    
    # 保存模型
    logger.info("💾 保存LoRA权重...")
    trainer.save_model()
    processor.save_pretrained(output_path)
    
    logger.info("🎉 LoRA微调完成!")
    logger.info(f"📁 模型保存在: {output_path}")
    
    # 列出保存的文件
    saved_files = os.listdir(output_path)
    logger.info(f"📋 保存的文件: {saved_files}")

if __name__ == "__main__":
    main()
