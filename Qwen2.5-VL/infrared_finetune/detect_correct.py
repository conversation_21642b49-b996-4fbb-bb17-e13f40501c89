"""
红外视频目标检测 - 使用微调后的模型
"""

import os
import json
import logging
import torch
from pathlib import Path
from transformers import AutoProcessor, Qwen2_5_VLForConditionalGeneration
from peft import PeftModel
from decord import VideoReader
from PIL import Image
import re

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class InfraredVideoDetector:
    """红外视频目标检测器"""
    
    def __init__(self, base_model_path, lora_model_path):
        self.base_model_path = base_model_path
        self.lora_model_path = lora_model_path
        self.fixed_frames = 5
        
        # 加载模型和processor
        self.load_model()
    
    def load_model(self):
        """加载模型"""
        logger.info("🔤 加载processor...")
        self.processor = AutoProcessor.from_pretrained(
            self.base_model_path, 
            trust_remote_code=True
        )
        
        logger.info("🤖 加载基础模型...")
        self.model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
            self.base_model_path,
            torch_dtype=torch.bfloat16,
            device_map={"": "cuda:0"},
            trust_remote_code=True,
            attn_implementation="flash_attention_2"
        )
        
        # 加载LoRA权重
        if os.path.exists(self.lora_model_path):
            logger.info("⚙️ 加载LoRA权重...")
            self.model = PeftModel.from_pretrained(self.model, self.lora_model_path)
            self.model = self.model.merge_and_unload()
        else:
            logger.warning(f"LoRA模型不存在: {self.lora_model_path}")
        
        self.model.eval()
        logger.info("✅ 模型加载完成")
    
    def load_video_frames(self, video_path):
        """加载视频帧"""
        try:
            if not os.path.exists(video_path):
                logger.error(f"视频不存在: {video_path}")
                return None
            
            vr = VideoReader(video_path, num_threads=1)
            total_frames = len(vr)
            
            if total_frames == 0:
                logger.error(f"视频为空: {video_path}")
                return None
            
            # 均匀采样固定数量的帧
            if total_frames <= self.fixed_frames:
                frame_indices = list(range(total_frames))
                while len(frame_indices) < self.fixed_frames:
                    frame_indices.append(frame_indices[-1])
            else:
                frame_indices = torch.linspace(0, total_frames - 1, self.fixed_frames).long()
            
            frame_indices = frame_indices[:self.fixed_frames]
            
            # 加载帧
            frames = vr.get_batch(frame_indices).asnumpy()
            pil_frames = [Image.fromarray(frame) for frame in frames]
            
            # 确保帧数正确
            while len(pil_frames) < self.fixed_frames:
                pil_frames.append(pil_frames[-1])
            pil_frames = pil_frames[:self.fixed_frames]
            
            return pil_frames
            
        except Exception as e:
            logger.error(f"视频加载失败 {video_path}: {e}")
            return None
    
    def detect_video(self, video_path):
        """检测视频中的目标"""
        # 加载视频帧
        frames = self.load_video_frames(video_path)
        if frames is None:
            return None
        
        # 构建检测提示
        messages = [
            {
                "role": "user",
                "content": [
                    {"type": "video", "video": video_path},
                    {"type": "text", "text": "请检测视频中每一帧的红外目标，返回边界框坐标。"}
                ]
            }
        ]
        
        try:
            # 处理输入
            text = self.processor.apply_chat_template(
                messages,
                tokenize=False,
                add_generation_prompt=True
            )
            
            inputs = self.processor(
                videos=frames,
                text=text,
                padding=False,
                truncation=True,
                max_length=2048,
                return_tensors="pt"
            ).to("cuda:0")
            
            # 生成检测结果
            with torch.no_grad():
                outputs = self.model.generate(
                    **inputs,
                    max_new_tokens=512,
                    do_sample=False,
                    temperature=0.1,
                    pad_token_id=self.processor.tokenizer.eos_token_id
                )
            
            # 解码结果
            generated_text = self.processor.decode(
                outputs[0][inputs['input_ids'].shape[1]:],
                skip_special_tokens=True
            )
            
            # 解析检测结果
            detections = self.parse_detection_results(generated_text)
            
            return {
                'video_path': video_path,
                'raw_output': generated_text,
                'detections': detections
            }
            
        except Exception as e:
            logger.error(f"检测失败 {video_path}: {e}")
            return None
    
    def parse_detection_results(self, text):
        """解析检测结果文本"""
        detections = []
        
        # 查找帧级别的检测结果
        frame_pattern = r'帧(\d+):\s*\[(.*?)\]'
        frame_matches = re.findall(frame_pattern, text, re.DOTALL)
        
        for frame_idx, frame_content in frame_matches:
            frame_detections = []
            
            if frame_content.strip():
                # 解析边界框
                bbox_pattern = r'\{"class":\s*(\d+),\s*"bbox":\s*\[([\d\.,\s]+)\]\}'
                bbox_matches = re.findall(bbox_pattern, frame_content)
                
                for class_id, bbox_str in bbox_matches:
                    try:
                        bbox_coords = [float(x.strip()) for x in bbox_str.split(',')]
                        if len(bbox_coords) == 4:
                            frame_detections.append({
                                'class': int(class_id),
                                'bbox': bbox_coords,
                                'confidence': 1.0  # 默认置信度
                            })
                    except ValueError:
                        continue
            
            detections.append({
                'frame': int(frame_idx),
                'objects': frame_detections
            })
        
        return detections
    
    def detect_batch(self, video_list):
        """批量检测视频"""
        results = []
        
        for i, video_path in enumerate(video_list):
            logger.info(f"检测视频 {i+1}/{len(video_list)}: {video_path}")
            
            result = self.detect_video(video_path)
            if result:
                results.append(result)
            else:
                logger.warning(f"检测失败: {video_path}")
        
        return results
    
    def save_results(self, results, output_path):
        """保存检测结果"""
        output_file = Path(output_path)
        output_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        logger.info(f"检测结果保存到: {output_file}")

def main():
    """主函数"""
    logger.info("🎯 开始红外视频目标检测...")
    
    # 配置
    base_model_path = "/home/<USER>/Qwen/Qwen2.5-VL/Qwen2.5-VL-7B-Instruct"
    lora_model_path = "output/video_lora"
    test_data_path = "data/infrared_video_test.json"
    output_path = "results/detection_results.json"
    
    # 创建检测器
    detector = InfraredVideoDetector(base_model_path, lora_model_path)
    
    # 加载测试数据
    logger.info("📊 加载测试数据...")
    with open(test_data_path, 'r') as f:
        test_data = json.load(f)
    
    # 提取视频路径
    video_list = [item['video'] for item in test_data[:10]]  # 限制测试数量
    logger.info(f"测试视频数量: {len(video_list)}")
    
    # 批量检测
    results = detector.detect_batch(video_list)
    
    # 保存结果
    detector.save_results(results, output_path)
    
    logger.info(f"✅ 检测完成! 成功检测 {len(results)} 个视频")

if __name__ == "__main__":
    main()
