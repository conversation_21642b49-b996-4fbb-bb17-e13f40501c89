"""
红外视频数据处理器
功能：将YOLO格式的红外视频数据转换为Qwen2.5-VL微调所需的video.json格式
"""
import os
import json
import cv2
import numpy as np
from pathlib import Path
from typing import List, Dict, Any
import logging

class InfraredDataProcessor:
    """红外视频数据处理器"""
    
    def __init__(self, 
                 video_dir: str = "/home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/data/videos",
                 output_dir: str = "/home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/data"):
        """
        初始化数据处理器
        
        Args:
            video_dir: 视频文件目录
            output_dir: 输出目录
        """
        self.video_dir = Path(video_dir)
        self.output_dir = Path(output_dir)
        self.logger = self._setup_logger()
        
        # 确保输出目录存在
        self.output_dir.mkdir(parents=True, exist_ok=True)
    
    def _setup_logger(self):
        """设置日志记录器"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        return logging.getLogger(__name__)
    
    def process_videos_to_json(self, train_ratio: float = 0.8) -> Dict[str, int]:
        """
        处理视频文件，生成训练和测试的JSON数据
        
        Args:
            train_ratio: 训练集比例
            
        Returns:
            处理统计信息
        """
        self.logger.info("开始处理红外视频数据...")
        
        # 获取所有视频文件
        video_files = list(self.video_dir.glob("*.mp4"))
        self.logger.info(f"找到 {len(video_files)} 个视频文件")
        
        if not video_files:
            self.logger.error("未找到视频文件")
            return {"train": 0, "test": 0}
        
        # 分割训练和测试集
        train_count = int(len(video_files) * train_ratio)
        train_files = video_files[:train_count]
        test_files = video_files[train_count:]
        
        # 生成训练数据
        train_data = self._generate_video_data(train_files, "train")
        test_data = self._generate_video_data(test_files, "test")
        
        # 保存JSON文件
        train_file = self.output_dir / "infrared_video_train.json"
        test_file = self.output_dir / "infrared_video_test.json"
        
        with open(train_file, 'w', encoding='utf-8') as f:
            json.dump(train_data, f, ensure_ascii=False, indent=2)
        
        with open(test_file, 'w', encoding='utf-8') as f:
            json.dump(test_data, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"训练数据保存到: {train_file}")
        self.logger.info(f"测试数据保存到: {test_file}")
        
        return {
            "train": len(train_data),
            "test": len(test_data),
            "total_videos": len(video_files)
        }
    
    def _generate_video_data(self, video_files: List[Path], split: str) -> List[Dict]:
        """
        生成视频数据条目
        
        Args:
            video_files: 视频文件列表
            split: 数据集分割类型
            
        Returns:
            视频数据列表
        """
        data = []
        
        for video_file in video_files:
            try:
                # 获取视频信息
                video_info = self._get_video_info(video_file)
                
                # 生成对话数据
                conversation = self._generate_conversation(video_file, video_info)
                
                # 构建数据条目
                data_entry = {
                    "id": f"{split}_{video_file.stem}",
                    "conversations": conversation,
                    "video": str(video_file.relative_to(self.output_dir.parent))
                }
                
                data.append(data_entry)
                
            except Exception as e:
                self.logger.error(f"处理视频 {video_file} 失败: {str(e)}")
                continue
        
        self.logger.info(f"{split}集生成 {len(data)} 条数据")
        return data
    
    def _get_video_info(self, video_file: Path) -> Dict:
        """获取视频基本信息"""
        cap = cv2.VideoCapture(str(video_file))
        
        if not cap.isOpened():
            raise ValueError(f"无法打开视频文件: {video_file}")
        
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        fps = cap.get(cv2.CAP_PROP_FPS)
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        
        cap.release()
        
        return {
            "frame_count": frame_count,
            "fps": fps,
            "width": width,
            "height": height,
            "duration": frame_count / fps if fps > 0 else 0
        }
    
    def _generate_conversation(self, video_file: Path, video_info: Dict) -> List[Dict]:
        """
        生成对话数据 - 支持目标检测边界框输出

        Args:
            video_file: 视频文件路径
            video_info: 视频信息

        Returns:
            对话列表
        """
        # 根据视频文件名判断是否包含目标
        has_target = self._determine_target_presence(video_file)

        # 构建对话
        conversations = [
            {
                "from": "user",
                "value": "<video>\n请检测这个红外视频中的目标物体。如果发现目标，请输出目标的边界框坐标，格式为JSON: {\"bbox\": [x, y, width, height]}。如果没有目标，请输出: {\"bbox\": null}。"
            },
            {
                "from": "assistant",
                "value": self._generate_detection_response(has_target, video_info)
            }
        ]

        return conversations
    
    def _determine_target_presence(self, video_file: Path) -> bool:
        """
        根据文件名或其他规则判断是否包含目标
        这里使用简单的规则，实际应用中应该基于真实标注
        """
        filename = video_file.stem.lower()
        
        # 假设某些序列包含目标
        target_sequences = ['data01', 'data02', 'data04', 'data05', 'data06', 'data07', 'data23', 'data25', 'data26']
        
        for seq in target_sequences:
            if seq in filename:
                return True
        
        return False
    
    def _generate_detection_response(self, has_target: bool, video_info: Dict) -> str:
        """生成目标检测回复 - 包含边界框坐标"""
        if has_target:
            # 生成模拟的边界框坐标 (相对于视频帧大小)
            width = video_info.get('width', 640)
            height = video_info.get('height', 480)

            # 生成中心区域的边界框
            center_x = width // 2
            center_y = height // 2
            box_width = width // 8
            box_height = height // 8

            # 计算边界框 [x, y, width, height]
            bbox = [
                max(0, center_x - box_width // 2),
                max(0, center_y - box_height // 2),
                box_width,
                box_height
            ]

            # 添加一些随机变化
            import random
            random.seed(hash(str(video_info)) % 1000)
            bbox[0] += random.randint(-20, 20)
            bbox[1] += random.randint(-20, 20)
            bbox[2] += random.randint(-10, 10)
            bbox[3] += random.randint(-10, 10)

            # 确保边界框在有效范围内
            bbox[0] = max(0, min(bbox[0], width - bbox[2]))
            bbox[1] = max(0, min(bbox[1], height - bbox[3]))

            return f'{{"bbox": [{bbox[0]}, {bbox[1]}, {bbox[2]}, {bbox[3]}]}}'
        else:
            return '{"bbox": null}'
    
    def generate_statistics(self) -> Dict:
        """生成数据统计信息"""
        train_file = self.output_dir / "infrared_video_train.json"
        test_file = self.output_dir / "infrared_video_test.json"
        
        stats = {
            "train_samples": 0,
            "test_samples": 0,
            "total_videos": 0,
            "video_info": {}
        }
        
        if train_file.exists():
            with open(train_file, 'r', encoding='utf-8') as f:
                train_data = json.load(f)
                stats["train_samples"] = len(train_data)
        
        if test_file.exists():
            with open(test_file, 'r', encoding='utf-8') as f:
                test_data = json.load(f)
                stats["test_samples"] = len(test_data)
        
        stats["total_videos"] = stats["train_samples"] + stats["test_samples"]
        
        # 保存统计信息
        stats_file = self.output_dir / "data_statistics.json"
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(stats, f, ensure_ascii=False, indent=2)
        
        return stats


def main():
    """主函数"""
    processor = InfraredDataProcessor()
    
    # 处理视频数据
    result = processor.process_videos_to_json(train_ratio=0.8)
    
    # 生成统计信息
    stats = processor.generate_statistics()
    
    print("📊 数据处理完成!")
    print(f"训练样本: {stats['train_samples']}")
    print(f"测试样本: {stats['test_samples']}")
    print(f"总视频数: {stats['total_videos']}")


if __name__ == "__main__":
    main()
