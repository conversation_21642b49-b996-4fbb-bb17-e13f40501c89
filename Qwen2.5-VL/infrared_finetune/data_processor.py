"""
红外视频数据处理器
功能：将YOLO格式的红外视频数据转换为Qwen2.5-VL微调所需的video.json格式
"""
import os
import json
import cv2
import numpy as np
from pathlib import Path
from typing import List, Dict, Any
import logging

class InfraredDataProcessor:
    """红外视频数据处理器"""
    
    def __init__(self,
                 video_dir: str = "/home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/data/videos",
                 output_dir: str = "/home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/data",
                 labels_dir: str = "/home/<USER>/Qwen/Qwen2.5-VL/tiaozhanbei_datasets/labels"):
        """
        初始化数据处理器

        Args:
            video_dir: 视频文件目录
            output_dir: 输出目录
            labels_dir: YOLO标注文件目录
        """
        self.video_dir = Path(video_dir)
        self.output_dir = Path(output_dir)
        self.labels_dir = Path(labels_dir)
        self.logger = self._setup_logger()

        # 确保输出目录存在
        self.output_dir.mkdir(parents=True, exist_ok=True)
    
    def _setup_logger(self):
        """设置日志记录器"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        return logging.getLogger(__name__)

    def _extract_sequence_name(self, filename: str) -> str:
        """
        从文件名中提取序列名

        Args:
            filename: 视频文件名（不含扩展名）

        Returns:
            序列名，如 'data01', 'data23' 等
        """
        # 假设文件名格式为 data01_seq_000, data23_seq_001 等
        if '_seq_' in filename:
            return filename.split('_seq_')[0]
        elif filename.startswith('data'):
            # 提取 data 后面的数字部分
            import re
            match = re.match(r'(data\d+)', filename)
            if match:
                return match.group(1)

        # 如果无法识别，返回原文件名
        return filename

    def _get_image_size(self, sequence_name: str) -> tuple:
        """
        获取图像尺寸

        Args:
            sequence_name: 序列名，如 'data01', 'data23' 等

        Returns:
            (width, height) 图像尺寸
        """
        size_map = {
            'data01': (256, 256), 'data02': (256, 256), 'data04': (256, 256),
            'data05': (256, 256), 'data06': (256, 256), 'data07': (256, 256),
            'data23': (640, 512), 'data25': (640, 512), 'data26': (640, 512)
        }
        return size_map.get(sequence_name, (256, 256))

    def _yolo_to_absolute(self, yolo_bbox: list, img_width: int, img_height: int) -> list:
        """
        将YOLO格式的归一化坐标转换为绝对坐标

        Args:
            yolo_bbox: [x_center, y_center, width, height] 归一化坐标
            img_width: 图像宽度
            img_height: 图像高度

        Returns:
            [x1, y1, x2, y2] 绝对坐标
        """
        x_center, y_center, width, height = yolo_bbox

        # 转换为绝对坐标
        x_center_abs = x_center * img_width
        y_center_abs = y_center * img_height
        width_abs = width * img_width
        height_abs = height * img_height

        # 计算边界框坐标
        x1 = int(x_center_abs - width_abs / 2)
        y1 = int(y_center_abs - height_abs / 2)
        x2 = int(x_center_abs + width_abs / 2)
        y2 = int(y_center_abs + height_abs / 2)

        return [x1, y1, x2, y2]

    def _get_label_file_pattern(self, sequence_name: str) -> str:
        """
        获取标注文件的命名模式

        Args:
            sequence_name: 序列名，如 'data25', 'data01' 等

        Returns:
            标注文件的前缀模式
        """
        # 根据序列名确定标注文件的命名模式
        pattern_map = {
            'data01': '',  # data01使用简单的数字命名
            'data02': '',  # data02使用简单的数字命名
            'data04': '',  # data04使用简单的数字命名
            'data05': '',  # data05使用简单的数字命名
            'data06': '',  # data06使用简单的数字命名
            'data07': '',  # data07使用简单的数字命名
            'data23': 'merged_dataset_data_transform_1_new2_train_',
            'data25': 'merged_dataset_data_transform_1_wg2022_ir_041_split_02_',
            'data26': 'merged_dataset_data_transform_1_wg2022_ir_042_split_02_',
        }
        return pattern_map.get(sequence_name, '')

    def _video_seq_to_frame_range(self, sequence_name: str, seq_number: int) -> tuple:
        """
        将视频序列号转换为对应的标注帧范围

        Args:
            sequence_name: 序列名
            seq_number: 视频序列号（从文件名提取）

        Returns:
            (start_frame, end_frame) 对应的标注帧范围
        """
        # 每个视频对应5帧，从1开始编号
        start_frame = seq_number * 5 + 1
        end_frame = start_frame + 4
        return start_frame, end_frame

    def _load_frame_annotations(self, sequence_name: str, frame_id: int) -> list:
        """
        加载指定帧的标注数据

        Args:
            sequence_name: 序列名
            frame_id: 帧ID（标注文件中的实际帧号）

        Returns:
            标注列表，每个标注包含类别和边界框
        """
        # 获取标注文件的命名模式
        file_pattern = self._get_label_file_pattern(sequence_name)

        # 构建标注文件名
        if file_pattern:
            # 使用复杂命名模式
            label_filename = f"{file_pattern}{frame_id:06d}.txt"
        else:
            # 使用简单数字命名
            label_filename = f"{frame_id}.txt"

        label_file = self.labels_dir / sequence_name / label_filename

        annotations = []

        if label_file.exists():
            img_width, img_height = self._get_image_size(sequence_name)

            with open(label_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line:
                        parts = line.split()
                        if len(parts) >= 5:
                            class_id = int(parts[0])
                            x_center = float(parts[1])
                            y_center = float(parts[2])
                            width = float(parts[3])
                            height = float(parts[4])

                            # 转换为绝对坐标
                            bbox = self._yolo_to_absolute(
                                [x_center, y_center, width, height],
                                img_width, img_height
                            )

                            annotations.append({
                                'class_id': class_id,
                                'class_name': 'target',  # 假设只有一个类别
                                'bbox': bbox,
                                'confidence': 1.0
                            })
        else:
            # 如果标注文件不存在，记录日志但不报错
            self.logger.debug(f"标注文件不存在: {label_file}")

        return annotations
    
    def process_videos_to_json(self, test_sequences: List[str] = None) -> Dict[str, int]:
        """
        处理视频文件，按序列划分训练和测试集

        Args:
            test_sequences: 测试序列列表，默认为['data01', 'data23']

        Returns:
            处理统计信息
        """
        if test_sequences is None:
            test_sequences = ['data01', 'data23']

        self.logger.info("开始处理红外视频数据...")
        self.logger.info(f"测试序列: {test_sequences}")

        # 获取所有视频文件
        video_files = list(self.video_dir.glob("*.mp4"))
        self.logger.info(f"找到 {len(video_files)} 个视频文件")

        if not video_files:
            self.logger.error("未找到视频文件")
            return {"train": 0, "test": 0}

        # 按序列划分训练和测试集
        train_files = []
        test_files = []

        for video_file in video_files:
            # 从文件名中提取序列名
            filename = video_file.stem
            sequence_name = self._extract_sequence_name(filename)

            if sequence_name in test_sequences:
                test_files.append(video_file)
                self.logger.info(f"测试集: {filename} (序列: {sequence_name})")
            else:
                train_files.append(video_file)
                self.logger.info(f"训练集: {filename} (序列: {sequence_name})")

        self.logger.info(f"训练集视频数: {len(train_files)}")
        self.logger.info(f"测试集视频数: {len(test_files)}")

        # 生成训练数据
        train_data = self._generate_video_data(train_files, "train")
        test_data = self._generate_video_data(test_files, "test")

        # 保存JSON文件
        train_file = self.output_dir / "infrared_video_train.json"
        test_file = self.output_dir / "infrared_video_test.json"

        with open(train_file, 'w', encoding='utf-8') as f:
            json.dump(train_data, f, ensure_ascii=False, indent=2)

        with open(test_file, 'w', encoding='utf-8') as f:
            json.dump(test_data, f, ensure_ascii=False, indent=2)

        self.logger.info(f"训练数据保存到: {train_file}")
        self.logger.info(f"测试数据保存到: {test_file}")

        return {
            "train": len(train_data),
            "test": len(test_data),
            "total_videos": len(video_files),
            "train_sequences": [self._extract_sequence_name(f.stem) for f in train_files],
            "test_sequences": [self._extract_sequence_name(f.stem) for f in test_files]
        }
    
    def _generate_video_data(self, video_files: List[Path], split: str) -> List[Dict]:
        """
        生成视频数据条目
        
        Args:
            video_files: 视频文件列表
            split: 数据集分割类型
            
        Returns:
            视频数据列表
        """
        data = []
        
        for video_file in video_files:
            try:
                # 获取视频信息
                video_info = self._get_video_info(video_file)
                
                # 生成对话数据
                conversation = self._generate_conversation(video_file, video_info)
                
                # 构建数据条目
                data_entry = {
                    "id": f"{split}_{video_file.stem}",
                    "conversations": conversation,
                    "video": str(video_file.relative_to(self.output_dir.parent))
                }
                
                data.append(data_entry)
                
            except Exception as e:
                self.logger.error(f"处理视频 {video_file} 失败: {str(e)}")
                continue
        
        self.logger.info(f"{split}集生成 {len(data)} 条数据")
        return data
    
    def _get_video_info(self, video_file: Path) -> Dict:
        """获取视频基本信息"""
        cap = cv2.VideoCapture(str(video_file))
        
        if not cap.isOpened():
            raise ValueError(f"无法打开视频文件: {video_file}")
        
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        fps = cap.get(cv2.CAP_PROP_FPS)
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        
        cap.release()
        
        return {
            "frame_count": frame_count,
            "fps": fps,
            "width": width,
            "height": height,
            "duration": frame_count / fps if fps > 0 else 0
        }
    
    def _generate_conversation(self, video_file: Path, video_info: Dict) -> List[Dict]:
        """
        生成对话数据 - 参考vedio_detection项目格式

        Args:
            video_file: 视频文件路径
            video_info: 视频信息

        Returns:
            对话列表
        """
        # 根据视频文件名判断是否包含目标
        has_target = self._determine_target_presence(video_file)

        # 构建对话 - 参考vedio_detection项目的prompt格式
        conversations = [
            {
                "from": "user",
                "value": "<video>\n这是一个红外视频序列，包含连续的5帧图像。请分析这个视频中的微小移动目标。\n\n视频分析任务：\n1. 这是一个视频序列，不是静态图片，请利用帧间时序信息\n2. 检测每帧中的微小目标，特别是移动的小白点或小亮点\n3. 通过观察目标在不同帧中的位置变化来确认真实目标\n4. 对检测到的目标进行分类和定位\n\n检测重点：\n- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）\n- 利用视频时序性识别运动模式\n- 排除静态背景噪声\n- 精确的空间定位\n\n请以JSON格式输出每一帧的检测结果，格式如下：\n```json\n{\n  \"frame_001\": [\n    {\n      \"bbox\": [x1, y1, x2, y2],\n      \"class_name\": \"drone\",\n      \"confidence\": 0.95\n    }\n  ]\n}\n```\n\n目标类别：\n- drone: 无人机\n- car: 汽车\n- ship: 船只\n- bus: 公交车\n- pedestrian: 行人\n- cyclist: 骑行者"
            },
            {
                "from": "assistant",
                "value": self._generate_detection_response(has_target, video_info, video_file)
            }
        ]

        return conversations
    
    def _determine_target_presence(self, video_file: Path) -> bool:
        """
        根据文件名或其他规则判断是否包含目标
        这里使用简单的规则，实际应用中应该基于真实标注
        """
        filename = video_file.stem.lower()
        
        # 假设某些序列包含目标
        target_sequences = ['data01', 'data02', 'data04', 'data05', 'data06', 'data07', 'data23', 'data25', 'data26']
        
        for seq in target_sequences:
            if seq in filename:
                return True
        
        return False
    
    def _generate_detection_response(self, has_target: bool, video_info: Dict, video_file: Path) -> str:
        """生成目标检测回复 - 使用真实标注数据"""
        # 从视频文件名提取序列名和序列号
        filename = video_file.stem
        sequence_name = self._extract_sequence_name(filename)

        # 提取序列号（假设文件名格式为 data25_seq_008）
        import re
        match = re.search(r'seq_(\d+)', filename)
        if match:
            seq_number = int(match.group(1))
        else:
            seq_number = 0

        # 将视频序列号转换为标注帧范围
        start_frame, end_frame = self._video_seq_to_frame_range(sequence_name, seq_number)

        self.logger.info(f"视频 {filename}: 序列号={seq_number}, 标注帧范围={start_frame}-{end_frame}")

        # 生成5帧的检测结果，使用真实标注
        detection_result = "{\n"

        for i in range(5):
            frame_name = f"frame_{i+1:03d}"
            frame_id = start_frame + i

            # 加载真实标注数据
            annotations = self._load_frame_annotations(sequence_name, frame_id)

            detection_result += f'  "{frame_name}": [\n'

            if annotations:
                for j, ann in enumerate(annotations):
                    detection_result += '    {\n'
                    detection_result += f'      "bbox": {ann["bbox"]},\n'
                    detection_result += f'      "class_name": "{ann["class_name"]}",\n'
                    detection_result += f'      "confidence": {ann["confidence"]}\n'
                    detection_result += '    }'

                    if j < len(annotations) - 1:
                        detection_result += ','
                    detection_result += '\n'

            if i < 4:
                detection_result += '  ],\n'
            else:
                detection_result += '  ]\n'

        detection_result += "}"
        return detection_result

    def _smart_resize(self, height: int, width: int, factor: int = 28,
                     min_pixels: int = 56 * 56, max_pixels: int = 14 * 14 * 4 * 1280):
        """Qwen2.5-VL的智能resize函数"""
        if height < factor or width < factor:
            raise ValueError(f"Height and width must be at least {factor}")
        if height * width > max_pixels:
            ratio = (max_pixels / (height * width)) ** 0.5
            height = int(height * ratio)
            width = int(width * ratio)
        elif height * width < min_pixels:
            ratio = (min_pixels / (height * width)) ** 0.5
            height = int(height * ratio)
            width = int(width * ratio)

        height = (height // factor) * factor
        width = (width // factor) * factor

        return width, height
    
    def generate_statistics(self) -> Dict:
        """生成数据统计信息"""
        train_file = self.output_dir / "infrared_video_train.json"
        test_file = self.output_dir / "infrared_video_test.json"
        
        stats = {
            "train_samples": 0,
            "test_samples": 0,
            "total_videos": 0,
            "video_info": {}
        }
        
        if train_file.exists():
            with open(train_file, 'r', encoding='utf-8') as f:
                train_data = json.load(f)
                stats["train_samples"] = len(train_data)
        
        if test_file.exists():
            with open(test_file, 'r', encoding='utf-8') as f:
                test_data = json.load(f)
                stats["test_samples"] = len(test_data)
        
        stats["total_videos"] = stats["train_samples"] + stats["test_samples"]
        
        # 保存统计信息
        stats_file = self.output_dir / "data_statistics.json"
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(stats, f, ensure_ascii=False, indent=2)
        
        return stats


def main():
    """主函数"""
    processor = InfraredDataProcessor()

    # 按序列划分数据集：data01和data23作为测试集
    test_sequences = ['data01', 'data23']
    result = processor.process_videos_to_json(test_sequences=test_sequences)

    # 生成统计信息
    stats = processor.generate_statistics()

    print("📊 数据处理完成!")
    print(f"训练样本: {stats['train_samples']}")
    print(f"测试样本: {stats['test_samples']}")
    print(f"总视频数: {stats['total_videos']}")
    print(f"训练序列: {set(result['train_sequences'])}")
    print(f"测试序列: {set(result['test_sequences'])}")


if __name__ == "__main__":
    main()
