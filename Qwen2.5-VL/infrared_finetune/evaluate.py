#!/usr/bin/env python3
"""
独立的模型评估脚本
评估微调后的Qwen2.5-VL模型性能
"""

import os
import sys
import json
import torch
import logging
import argparse
from pathlib import Path
from typing import List, Dict, Any
from tqdm import tqdm

from transformers import Qwen2VLForConditionalGeneration, Qwen2VLProcessor
from peft import PeftModel

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ModelEvaluator:
    """模型评估器"""
    
    def __init__(self, model_path: str, lora_path: str = None):
        self.model_path = model_path
        self.lora_path = lora_path
        self.model = None
        self.processor = None
        
    def load_model(self):
        """加载模型"""
        logger.info("加载模型和处理器...")
        
        # 加载处理器
        self.processor = Qwen2VLProcessor.from_pretrained(self.model_path)
        
        # 加载基础模型
        self.model = Qwen2VLForConditionalGeneration.from_pretrained(
            self.model_path,
            torch_dtype=torch.bfloat16,
            device_map="auto"
        )
        
        # 如果有LoRA权重，加载它们
        if self.lora_path and os.path.exists(self.lora_path):
            logger.info(f"加载LoRA权重: {self.lora_path}")
            self.model = PeftModel.from_pretrained(self.model, self.lora_path)
        
        self.model.eval()
        logger.info("模型加载完成")
    
    def predict_single(self, video_path: str, prompt: str) -> str:
        """单个样本预测"""
        try:
            # 构建输入
            messages = [
                {
                    "role": "user",
                    "content": [
                        {"type": "video", "video": video_path},
                        {"type": "text", "text": prompt}
                    ]
                }
            ]
            
            # 处理输入
            text = self.processor.apply_chat_template(
                messages, tokenize=False, add_generation_prompt=True
            )
            
            inputs = self.processor(
                text=[text],
                videos=[video_path],
                return_tensors="pt",
                padding=True
            )
            
            # 移动到GPU
            inputs = {k: v.to(self.model.device) for k, v in inputs.items()}
            
            # 生成
            with torch.no_grad():
                outputs = self.model.generate(
                    **inputs,
                    max_new_tokens=512,
                    do_sample=False,
                    temperature=0.1
                )
            
            # 解码输出
            generated_ids = outputs[0][inputs["input_ids"].shape[1]:]
            response = self.processor.decode(generated_ids, skip_special_tokens=True)
            
            return response.strip()
            
        except Exception as e:
            logger.error(f"预测失败: {e}")
            return ""
    
    def evaluate_dataset(self, test_data_path: str) -> Dict[str, Any]:
        """评估整个数据集"""
        logger.info(f"评估数据集: {test_data_path}")
        
        # 加载测试数据
        with open(test_data_path, 'r', encoding='utf-8') as f:
            test_data = json.load(f)
        
        results = []
        correct_predictions = 0
        total_predictions = 0
        
        for i, item in enumerate(tqdm(test_data, desc="评估进度")):
            video_path = item['videos'][0]
            user_prompt = item['messages'][0]['content']
            ground_truth = item['messages'][1]['content']
            
            # 预测
            prediction = self.predict_single(video_path, user_prompt)
            
            # 简单的准确性检查（检查是否包含关键信息）
            is_correct = self._check_prediction_accuracy(prediction, ground_truth)
            
            if is_correct:
                correct_predictions += 1
            total_predictions += 1
            
            results.append({
                "sample_id": i,
                "video_path": video_path,
                "prompt": user_prompt,
                "ground_truth": ground_truth,
                "prediction": prediction,
                "is_correct": is_correct
            })
            
            # 每10个样本输出一次进度
            if (i + 1) % 10 == 0:
                accuracy = correct_predictions / total_predictions
                logger.info(f"已评估 {i+1}/{len(test_data)} 样本，当前准确率: {accuracy:.3f}")
        
        # 计算最终指标
        final_accuracy = correct_predictions / total_predictions if total_predictions > 0 else 0
        
        evaluation_results = {
            "total_samples": total_predictions,
            "correct_predictions": correct_predictions,
            "accuracy": final_accuracy,
            "detailed_results": results
        }
        
        return evaluation_results
    
    def _check_prediction_accuracy(self, prediction: str, ground_truth: str) -> bool:
        """检查预测准确性"""
        try:
            # 尝试解析JSON格式的预测和真实值
            pred_data = json.loads(prediction) if prediction.strip().startswith('{') else {}
            gt_data = json.loads(ground_truth) if ground_truth.strip().startswith('{') else {}
            
            # 如果都是JSON格式，比较关键字段
            if pred_data and gt_data:
                # 检查是否检测到目标
                pred_has_detections = any(len(detections) > 0 for detections in pred_data.values())
                gt_has_detections = any(len(detections) > 0 for detections in gt_data.values())
                
                return pred_has_detections == gt_has_detections
            
            # 简单的字符串匹配
            return "drone" in prediction.lower() if "drone" in ground_truth.lower() else True
            
        except:
            # 如果解析失败，使用简单的字符串匹配
            return "drone" in prediction.lower() if "drone" in ground_truth.lower() else True

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="评估微调后的Qwen2.5-VL模型")
    parser.add_argument("--model_path", type=str, 
                       default="/home/<USER>/Qwen/Qwen2.5-VL/Qwen2.5-VL-7B-Instruct",
                       help="基础模型路径")
    parser.add_argument("--lora_path", type=str,
                       default="/home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/output/checkpoints",
                       help="LoRA权重路径")
    parser.add_argument("--test_data", type=str,
                       default="/home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/data/infrared_detection_test.json",
                       help="测试数据路径")
    parser.add_argument("--output_dir", type=str,
                       default="/home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/output",
                       help="输出目录")
    
    args = parser.parse_args()
    
    # 检查文件是否存在
    if not os.path.exists(args.test_data):
        logger.error(f"测试数据文件不存在: {args.test_data}")
        return
    
    # 创建评估器
    evaluator = ModelEvaluator(args.model_path, args.lora_path)
    
    # 加载模型
    evaluator.load_model()
    
    # 评估
    logger.info("开始评估...")
    results = evaluator.evaluate_dataset(args.test_data)
    
    # 保存结果
    os.makedirs(args.output_dir, exist_ok=True)
    results_file = os.path.join(args.output_dir, "evaluation_results.json")
    
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    # 输出结果
    logger.info("=" * 60)
    logger.info("评估结果")
    logger.info("=" * 60)
    logger.info(f"总样本数: {results['total_samples']}")
    logger.info(f"正确预测: {results['correct_predictions']}")
    logger.info(f"准确率: {results['accuracy']:.3f}")
    logger.info(f"详细结果保存在: {results_file}")
    logger.info("=" * 60)

if __name__ == "__main__":
    main()
