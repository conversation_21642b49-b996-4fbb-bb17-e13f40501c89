"""
批量检测脚本 - 对完整测试集进行检测
"""
import os
import json
import logging
from pathlib import Path
from typing import List, Dict, Any
from detect import InfraredDetector

class BatchDetector:
    """批量检测器"""
    
    def __init__(self):
        """初始化批量检测器"""
        self.logger = self._setup_logger()
        self.detector = None
        self._load_detector()
    
    def _setup_logger(self):
        """设置日志记录器"""
        logging.basicConfig(level=logging.INFO)
        return logging.getLogger(__name__)
    
    def _load_detector(self):
        """加载检测器"""
        try:
            self.detector = InfraredDetector()
            self.logger.info("✅ 检测器初始化成功")
        except Exception as e:
            self.logger.error(f"❌ 检测器初始化失败: {str(e)}")
            raise
    
    def detect_test_dataset(self, output_file: str = "output/detection_results.json"):
        """检测完整测试数据集"""
        
        # 加载测试数据
        test_data_path = "data/infrared_video_test.json"
        if not os.path.exists(test_data_path):
            self.logger.error(f"测试数据文件不存在: {test_data_path}")
            return
        
        with open(test_data_path, 'r') as f:
            test_data = json.load(f)
        
        self.logger.info(f"🚀 开始批量检测: {len(test_data)} 个测试样本")
        
        # 创建输出目录
        Path(output_file).parent.mkdir(parents=True, exist_ok=True)
        
        detection_results = []
        
        for i, sample in enumerate(test_data):
            video_path = sample['video']
            sample_id = sample['id']
            
            self.logger.info(f"检测进度: {i+1}/{len(test_data)} - {sample_id}")
            
            if not os.path.exists(video_path):
                self.logger.warning(f"视频文件不存在: {video_path}")
                continue
            
            try:
                # 检测视频
                frame_results = self.detector.detect_video_sequence(video_path)
                
                # 保存结果
                result = {
                    'sample_id': sample_id,
                    'video_path': video_path,
                    'detection_results': frame_results,
                    'ground_truth': sample['conversations'][1]['value']  # 真实标注
                }
                
                detection_results.append(result)
                
                # 输出简要统计
                if frame_results:
                    target_frames = sum(1 for r in frame_results if r['has_target'])
                    avg_confidence = sum(r['confidence'] for r in frame_results) / len(frame_results)
                    self.logger.info(f"  结果: {target_frames}/{len(frame_results)} 帧有目标, "
                                   f"平均置信度: {avg_confidence:.3f}")
                
            except Exception as e:
                self.logger.error(f"检测失败 {video_path}: {str(e)}")
                continue
        
        # 保存检测结果
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(detection_results, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"✅ 批量检测完成! 结果保存到: {output_file}")
        self.logger.info(f"📊 成功检测: {len(detection_results)}/{len(test_data)} 个样本")
        
        return detection_results
    
    def analyze_results(self, results_file: str = "output/detection_results.json"):
        """分析检测结果"""
        
        if not os.path.exists(results_file):
            self.logger.error(f"结果文件不存在: {results_file}")
            return
        
        with open(results_file, 'r') as f:
            results = json.load(f)
        
        self.logger.info(f"📊 分析检测结果: {len(results)} 个样本")
        
        # 按序列分组统计
        sequence_stats = {}
        
        for result in results:
            sample_id = result['sample_id']
            sequence_name = sample_id.split('_')[1]  # 提取序列名如data01, data23
            
            if sequence_name not in sequence_stats:
                sequence_stats[sequence_name] = {
                    'total_videos': 0,
                    'total_frames': 0,
                    'target_frames': 0,
                    'avg_confidence': 0.0
                }
            
            detection_results = result['detection_results']
            if detection_results:
                sequence_stats[sequence_name]['total_videos'] += 1
                sequence_stats[sequence_name]['total_frames'] += len(detection_results)
                sequence_stats[sequence_name]['target_frames'] += sum(1 for r in detection_results if r['has_target'])
                sequence_stats[sequence_name]['avg_confidence'] += sum(r['confidence'] for r in detection_results) / len(detection_results)
        
        # 输出统计结果
        print("\n📈 检测结果统计:")
        print("=" * 80)
        
        for seq_name, stats in sequence_stats.items():
            if stats['total_videos'] > 0:
                avg_conf = stats['avg_confidence'] / stats['total_videos']
                target_rate = stats['target_frames'] / stats['total_frames'] if stats['total_frames'] > 0 else 0
                
                print(f"序列 {seq_name}:")
                print(f"  视频数: {stats['total_videos']}")
                print(f"  总帧数: {stats['total_frames']}")
                print(f"  目标帧数: {stats['target_frames']}")
                print(f"  目标检出率: {target_rate:.3f}")
                print(f"  平均置信度: {avg_conf:.3f}")
                print()

def main():
    """主函数"""
    detector = BatchDetector()
    
    # 检测完整测试集
    results = detector.detect_test_dataset()
    
    if results:
        # 分析结果
        detector.analyze_results()
    
    print("🎉 批量检测完成!")

if __name__ == "__main__":
    main()
