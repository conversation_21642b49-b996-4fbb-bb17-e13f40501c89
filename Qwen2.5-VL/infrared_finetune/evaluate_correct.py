"""
红外视频目标检测评估
计算mAP、精确率、召回率等指标
"""

import json
import logging
import numpy as np
from pathlib import Path
from collections import defaultdict
import matplotlib.pyplot as plt

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DetectionEvaluator:
    """目标检测评估器"""
    
    def __init__(self, iou_threshold=0.5):
        self.iou_threshold = iou_threshold
    
    def calculate_iou(self, box1, box2):
        """计算两个边界框的IoU"""
        # box格式: [x_center, y_center, width, height] (归一化坐标)
        
        # 转换为 [x1, y1, x2, y2] 格式
        x1_1 = box1[0] - box1[2] / 2
        y1_1 = box1[1] - box1[3] / 2
        x2_1 = box1[0] + box1[2] / 2
        y2_1 = box1[1] + box1[3] / 2
        
        x1_2 = box2[0] - box2[2] / 2
        y1_2 = box2[1] - box2[3] / 2
        x2_2 = box2[0] + box2[2] / 2
        y2_2 = box2[1] + box2[3] / 2
        
        # 计算交集
        x1_inter = max(x1_1, x1_2)
        y1_inter = max(y1_1, y1_2)
        x2_inter = min(x2_1, x2_2)
        y2_inter = min(y2_1, y2_2)
        
        if x2_inter <= x1_inter or y2_inter <= y1_inter:
            return 0.0
        
        inter_area = (x2_inter - x1_inter) * (y2_inter - y1_inter)
        
        # 计算并集
        area1 = box1[2] * box1[3]
        area2 = box2[2] * box2[3]
        union_area = area1 + area2 - inter_area
        
        if union_area <= 0:
            return 0.0
        
        return inter_area / union_area
    
    def match_detections(self, pred_boxes, gt_boxes):
        """匹配预测框和真实框"""
        if not pred_boxes or not gt_boxes:
            return [], list(range(len(gt_boxes)))
        
        # 计算所有预测框和真实框之间的IoU
        iou_matrix = np.zeros((len(pred_boxes), len(gt_boxes)))
        for i, pred_box in enumerate(pred_boxes):
            for j, gt_box in enumerate(gt_boxes):
                iou_matrix[i, j] = self.calculate_iou(pred_box['bbox'], gt_box['bbox'])
        
        # 贪心匹配
        matched_pairs = []
        used_gt = set()
        used_pred = set()
        
        # 按IoU从大到小排序
        indices = np.unravel_index(np.argsort(-iou_matrix.ravel()), iou_matrix.shape)
        
        for pred_idx, gt_idx in zip(indices[0], indices[1]):
            if pred_idx in used_pred or gt_idx in used_gt:
                continue
            
            iou = iou_matrix[pred_idx, gt_idx]
            if iou >= self.iou_threshold:
                # 检查类别是否匹配
                if pred_boxes[pred_idx]['class'] == gt_boxes[gt_idx]['class']:
                    matched_pairs.append((pred_idx, gt_idx, iou))
                    used_pred.add(pred_idx)
                    used_gt.add(gt_idx)
        
        # 未匹配的真实框
        unmatched_gt = [i for i in range(len(gt_boxes)) if i not in used_gt]
        
        return matched_pairs, unmatched_gt
    
    def evaluate_frame(self, pred_frame, gt_frame):
        """评估单帧检测结果"""
        pred_objects = pred_frame.get('objects', [])
        gt_objects = gt_frame.get('objects', [])
        
        # 匹配检测结果
        matched_pairs, unmatched_gt = self.match_detections(pred_objects, gt_objects)
        
        # 计算指标
        tp = len(matched_pairs)  # 真正例
        fp = len(pred_objects) - tp  # 假正例
        fn = len(unmatched_gt)  # 假负例
        
        return {
            'tp': tp,
            'fp': fp,
            'fn': fn,
            'matched_pairs': matched_pairs,
            'num_pred': len(pred_objects),
            'num_gt': len(gt_objects)
        }
    
    def evaluate_video(self, pred_video, gt_video):
        """评估单个视频的检测结果"""
        # 按帧号对齐预测和真实结果
        pred_frames = {frame['frame']: frame for frame in pred_video.get('detections', [])}
        gt_frames = {frame['frame']: frame for frame in gt_video.get('annotations', [])}
        
        video_metrics = {
            'total_tp': 0,
            'total_fp': 0,
            'total_fn': 0,
            'frame_results': []
        }
        
        # 评估每一帧
        all_frames = set(pred_frames.keys()) | set(gt_frames.keys())
        
        for frame_idx in sorted(all_frames):
            pred_frame = pred_frames.get(frame_idx, {'objects': []})
            gt_frame = gt_frames.get(frame_idx, {'objects': []})
            
            frame_result = self.evaluate_frame(pred_frame, gt_frame)
            frame_result['frame'] = frame_idx
            
            video_metrics['total_tp'] += frame_result['tp']
            video_metrics['total_fp'] += frame_result['fp']
            video_metrics['total_fn'] += frame_result['fn']
            video_metrics['frame_results'].append(frame_result)
        
        return video_metrics
    
    def calculate_metrics(self, tp, fp, fn):
        """计算精确率、召回率和F1分数"""
        precision = tp / (tp + fp) if (tp + fp) > 0 else 0.0
        recall = tp / (tp + fn) if (tp + fn) > 0 else 0.0
        f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0.0
        
        return {
            'precision': precision,
            'recall': recall,
            'f1': f1
        }
    
    def evaluate_dataset(self, predictions, ground_truth):
        """评估整个数据集"""
        # 按视频路径对齐预测和真实结果
        pred_dict = {pred['video_path']: pred for pred in predictions}
        gt_dict = {gt['video']: gt for gt in ground_truth}
        
        dataset_metrics = {
            'total_tp': 0,
            'total_fp': 0,
            'total_fn': 0,
            'video_results': [],
            'class_metrics': defaultdict(lambda: {'tp': 0, 'fp': 0, 'fn': 0})
        }
        
        # 评估每个视频
        common_videos = set(pred_dict.keys()) & set(gt_dict.keys())
        
        for video_path in common_videos:
            pred_video = pred_dict[video_path]
            gt_video = gt_dict[video_path]
            
            video_result = self.evaluate_video(pred_video, gt_video)
            video_result['video_path'] = video_path
            
            dataset_metrics['total_tp'] += video_result['total_tp']
            dataset_metrics['total_fp'] += video_result['total_fp']
            dataset_metrics['total_fn'] += video_result['total_fn']
            dataset_metrics['video_results'].append(video_result)
        
        # 计算总体指标
        overall_metrics = self.calculate_metrics(
            dataset_metrics['total_tp'],
            dataset_metrics['total_fp'],
            dataset_metrics['total_fn']
        )
        
        dataset_metrics['overall'] = overall_metrics
        
        return dataset_metrics
    
    def generate_report(self, metrics, output_path):
        """生成评估报告"""
        report = []
        
        # 总体指标
        overall = metrics['overall']
        report.append("=== 红外视频目标检测评估报告 ===\n")
        report.append(f"IoU阈值: {self.iou_threshold}")
        report.append(f"评估视频数量: {len(metrics['video_results'])}")
        report.append("")
        
        report.append("=== 总体指标 ===")
        report.append(f"精确率 (Precision): {overall['precision']:.4f}")
        report.append(f"召回率 (Recall): {overall['recall']:.4f}")
        report.append(f"F1分数: {overall['f1']:.4f}")
        report.append(f"真正例 (TP): {metrics['total_tp']}")
        report.append(f"假正例 (FP): {metrics['total_fp']}")
        report.append(f"假负例 (FN): {metrics['total_fn']}")
        report.append("")
        
        # 视频级别指标
        report.append("=== 视频级别指标 ===")
        for video_result in metrics['video_results'][:10]:  # 显示前10个视频
            video_metrics = self.calculate_metrics(
                video_result['total_tp'],
                video_result['total_fp'],
                video_result['total_fn']
            )
            
            video_name = Path(video_result['video_path']).name
            report.append(f"{video_name}:")
            report.append(f"  精确率: {video_metrics['precision']:.4f}")
            report.append(f"  召回率: {video_metrics['recall']:.4f}")
            report.append(f"  F1分数: {video_metrics['f1']:.4f}")
        
        # 保存报告
        output_file = Path(output_path)
        output_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report))
        
        logger.info(f"评估报告保存到: {output_file}")
        
        # 打印关键指标
        logger.info("=== 评估结果 ===")
        logger.info(f"精确率: {overall['precision']:.4f}")
        logger.info(f"召回率: {overall['recall']:.4f}")
        logger.info(f"F1分数: {overall['f1']:.4f}")

def load_ground_truth_from_test_data(test_data_path):
    """从测试数据中提取真实标注"""
    with open(test_data_path, 'r') as f:
        test_data = json.load(f)
    
    ground_truth = []
    
    for item in test_data:
        video_path = item['video']
        
        # 解析助手回复中的检测结果作为真实标注
        assistant_text = item['conversations'][1]['value']
        
        # 简单解析（实际应用中需要更复杂的解析逻辑）
        annotations = []
        lines = assistant_text.split('\n')
        
        for line in lines:
            if line.startswith('帧') and ':' in line:
                try:
                    frame_part, content_part = line.split(':', 1)
                    frame_idx = int(frame_part.replace('帧', '').strip())
                    
                    # 这里简化处理，实际需要解析具体的边界框
                    objects = []
                    if '[' in content_part and ']' in content_part:
                        # 假设有目标
                        objects.append({
                            'class': 0,
                            'bbox': [0.5, 0.5, 0.1, 0.1]  # 示例边界框
                        })
                    
                    annotations.append({
                        'frame': frame_idx,
                        'objects': objects
                    })
                except:
                    continue
        
        ground_truth.append({
            'video': video_path,
            'annotations': annotations
        })
    
    return ground_truth

def main():
    """主函数"""
    logger.info("📊 开始评估检测结果...")
    
    # 配置
    predictions_path = "results/detection_results.json"
    test_data_path = "data/infrared_video_test.json"
    report_path = "results/evaluation_report.txt"
    
    # 加载预测结果
    logger.info("加载预测结果...")
    with open(predictions_path, 'r') as f:
        predictions = json.load(f)
    
    # 加载真实标注
    logger.info("加载真实标注...")
    ground_truth = load_ground_truth_from_test_data(test_data_path)
    
    # 创建评估器
    evaluator = DetectionEvaluator(iou_threshold=0.5)
    
    # 评估
    logger.info("开始评估...")
    metrics = evaluator.evaluate_dataset(predictions, ground_truth)
    
    # 生成报告
    evaluator.generate_report(metrics, report_path)
    
    logger.info("✅ 评估完成!")

if __name__ == "__main__":
    main()
