#!/usr/bin/env python3
"""
微调模型评估脚本
基于现有的detection_evaluator.py进行适配
"""

import os
import sys
import json
import torch
import logging
from pathlib import Path
from typing import List, Dict, Any
import argparse

# 添加检测模块路径
sys.path.append("/home/<USER>/Qwen/Qwen2.5-VL/vedio_detection")

from transformers import Qwen2_5_VLForConditionalGeneration, AutoProcessor
from peft import PeftModel
from qwen_vl_utils import process_vision_info
from detection_evaluator import DetectionEvaluator

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FineTunedInfraredDetector:
    """微调后的红外图像检测器"""
    
    def __init__(self, base_model_path: str, lora_path: str = None):
        """
        初始化检测器
        
        Args:
            base_model_path: 基础模型路径
            lora_path: LoRA权重路径（可选）
        """
        self.base_model_path = base_model_path
        self.lora_path = lora_path
        self.model = None
        self.processor = None
        
        # 目标类别映射
        self.class_mapping = {
            "0": "drone",
            "1": "car",
            "2": "ship", 
            "3": "bus",
            "4": "pedestrian",
            "5": "cyclist"
        }
        
        self.load_model()
    
    def load_model(self):
        """加载模型"""
        try:
            logger.info(f"正在加载基础模型: {self.base_model_path}")
            
            # 加载基础模型
            self.model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
                self.base_model_path,
                torch_dtype="auto",
                device_map="auto"
            )
            
            # 如果有LoRA权重，则加载
            if self.lora_path and os.path.exists(self.lora_path):
                logger.info(f"正在加载LoRA权重: {self.lora_path}")
                self.model = PeftModel.from_pretrained(self.model, self.lora_path)
                self.model = self.model.merge_and_unload()  # 合并权重
            
            # 加载处理器
            self.processor = AutoProcessor.from_pretrained(self.base_model_path)
            
            logger.info("模型加载完成")
            
        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            raise
    
    def create_detection_prompt(self, frame_paths: List[str]) -> str:
        """创建检测提示词"""
        frame_ids = [Path(path).stem for path in frame_paths]
        
        # 构建示例输出格式
        example_output = "{\n"
        for i, frame_id in enumerate(frame_ids):
            example_output += f'  "{frame_id}": [\n'
            example_output += '    {\n'
            example_output += '      "bbox": [x1, y1, x2, y2],\n'
            example_output += '      "class_name": "drone",\n'
            example_output += '      "confidence": 0.95\n'
            example_output += '    }\n'
            if i < len(frame_ids) - 1:
                example_output += '  ],\n'
            else:
                example_output += '  ]\n'
        example_output += "}"
        
        prompt = f"""这是一个红外视频序列，包含连续的{len(frame_paths)}帧图像。请分析这个视频中的微小移动目标。

视频分析任务：
1. 这是一个视频序列，不是静态图片，请利用帧间时序信息
2. 检测每帧中的微小目标，特别是移动的小白点或小亮点
3. 通过观察目标在不同帧中的位置变化来确认真实目标
4. 对检测到的目标进行分类和定位

检测重点：
- 微小移动目标（无人机、汽车、船只、公交车、行人、骑行者）
- 利用视频时序性识别运动模式
- 排除静态背景噪声
- 精确的空间定位

请以JSON格式输出每一帧的检测结果：
```json
{example_output}
```

目标类别：
- drone: 无人机
- car: 汽车
- ship: 船只
- bus: 公交车
- pedestrian: 行人
- cyclist: 骑行者

注意：这是视频序列分析，请仔细观察每帧（{', '.join(frame_ids)}）中的微小目标，利用时序信息提高检测准确性。"""

        return prompt
    
    def detect_video(self, video_path: str, frame_paths: List[str]) -> Dict[str, Any]:
        """
        对视频进行目标检测
        
        Args:
            video_path: 视频文件路径
            frame_paths: 帧文件路径列表
            
        Returns:
            检测结果字典
        """
        try:
            # 构建消息
            messages = [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "video",
                            "video": f"file://{os.path.abspath(video_path)}",
                            "max_pixels": 256 * 256,
                            "fps": 5.0,
                        },
                        {
                            "type": "text",
                            "text": self.create_detection_prompt(frame_paths)
                        }
                    ]
                }
            ]
            
            # 处理输入
            text = self.processor.apply_chat_template(
                messages, tokenize=False, add_generation_prompt=True
            )
            
            image_inputs, video_inputs, video_kwargs = process_vision_info(
                messages, return_video_kwargs=True
            )
            
            inputs = self.processor(
                text=[text],
                images=image_inputs,
                videos=video_inputs,
                padding=True,
                return_tensors="pt",
                **video_kwargs,
            )
            inputs = inputs.to(self.model.device)
            
            # 推理
            logger.info("开始推理...")
            generated_ids = self.model.generate(
                **inputs,
                max_new_tokens=2048,
                temperature=0.1,
                do_sample=True
            )
            
            generated_ids_trimmed = [
                out_ids[len(in_ids):] for in_ids, out_ids in zip(inputs.input_ids, generated_ids)
            ]
            
            output_text = self.processor.batch_decode(
                generated_ids_trimmed, skip_special_tokens=True, clean_up_tokenization_spaces=False
            )
            
            logger.info("推理完成")
            
            # 解析结果
            result = self.parse_detection_result(output_text[0], frame_paths)
            return result
            
        except Exception as e:
            logger.error(f"检测失败: {e}")
            return {}
    
    def parse_detection_result(self, output_text: str, frame_paths: List[str]) -> Dict[str, Any]:
        """解析检测结果"""
        try:
            import re
            import ast
            
            logger.info("解析检测结果...")
            
            # 提取JSON部分
            json_match = re.search(r'```json\s*(.*?)\s*```', output_text, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
            else:
                json_str = output_text.strip()
            
            # 解析JSON
            try:
                result = json.loads(json_str)
            except json.JSONDecodeError:
                result = ast.literal_eval(json_str)
            
            # 验证结果格式
            frame_ids = [Path(path).stem for path in frame_paths]
            validated_result = {}
            
            for frame_id in frame_ids:
                if frame_id in result:
                    validated_result[frame_id] = result[frame_id]
                else:
                    validated_result[frame_id] = []
            
            parsed_result = {
                "frame_paths": frame_paths,
                "detections": validated_result
            }
            
            logger.info(f"成功解析检测结果，包含 {len(validated_result)} 帧")
            return parsed_result
            
        except Exception as e:
            logger.error(f"解析检测结果失败: {e}")
            frame_ids = [Path(path).stem for path in frame_paths]
            empty_detections = {frame_id: [] for frame_id in frame_ids}
            return {
                "frame_paths": frame_paths,
                "detections": empty_detections
            }

class FineTunedModelEvaluator:
    """微调模型评估器"""
    
    def __init__(self, base_model_path: str, lora_path: str = None, data_root: str = None):
        """
        初始化评估器
        
        Args:
            base_model_path: 基础模型路径
            lora_path: LoRA权重路径
            data_root: 数据根目录
        """
        self.base_model_path = base_model_path
        self.lora_path = lora_path
        self.data_root = Path(data_root) if data_root else Path("/home/<USER>/Qwen/Qwen2.5-VL/tiaozhanbei_datasets")
        
        # 初始化检测器
        self.detector = FineTunedInfraredDetector(base_model_path, lora_path)
        
        # 测试序列
        self.test_sequences = ['data01', 'data02', 'data04', 'data05', 'data06', 'data07', 'data23', 'data25', 'data26']
    
    def evaluate_model(self, output_dir: str = "./evaluation_output"):
        """
        评估模型性能
        
        Args:
            output_dir: 输出目录
        """
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info("开始模型评估...")
        
        # 这里可以添加具体的评估逻辑
        # 由于原始的detection_evaluator.py比较复杂，这里提供一个简化版本
        
        logger.info("模型评估完成")
        logger.info(f"评估结果保存在: {output_dir}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="微调模型评估")
    
    parser.add_argument(
        "--base-model",
        type=str,
        default="/home/<USER>/Qwen/Qwen2.5-VL/Qwen2.5-VL-7B-Instruct",
        help="基础模型路径"
    )
    
    parser.add_argument(
        "--lora-path",
        type=str,
        default=None,
        help="LoRA权重路径"
    )
    
    parser.add_argument(
        "--data-root",
        type=str,
        default="/home/<USER>/Qwen/Qwen2.5-VL/tiaozhanbei_datasets",
        help="数据根目录"
    )
    
    parser.add_argument(
        "--output-dir",
        type=str,
        default="./evaluation_output",
        help="评估输出目录"
    )
    
    args = parser.parse_args()
    
    try:
        # 创建评估器
        evaluator = FineTunedModelEvaluator(
            base_model_path=args.base_model,
            lora_path=args.lora_path,
            data_root=args.data_root
        )
        
        # 运行评估
        evaluator.evaluate_model(args.output_dir)
        
    except Exception as e:
        logger.error(f"评估失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
