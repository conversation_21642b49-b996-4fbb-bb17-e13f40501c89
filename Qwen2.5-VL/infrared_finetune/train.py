#!/usr/bin/env python3
"""
独立的红外图像微小目标检测微调训练脚本
不依赖LLaMA-Factory，直接使用transformers和peft
"""

import os
import sys
import json
import torch
import logging
from pathlib import Path
from typing import List, Dict, Any
from dataclasses import dataclass, field
from transformers import (
    Qwen2VLForConditionalGeneration,
    Qwen2VLProcessor,
    TrainingArguments,
    Trainer,
    DataCollatorForSeq2Seq
)
from peft import LoraConfig, get_peft_model, TaskType
from torch.utils.data import Dataset
import cv2
import numpy as np

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('training.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class ModelArguments:
    """模型参数"""
    model_name_or_path: str = "/home/<USER>/Qwen/Qwen2.5-VL/Qwen2.5-VL-7B-Instruct"
    cache_dir: str = None
    use_flash_attention_2: bool = True

@dataclass
class DataArguments:
    """数据参数"""
    train_data_path: str = "/home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/data/infrared_detection_train.json"
    test_data_path: str = "/home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/data/infrared_detection_test.json"
    max_seq_length: int = 8192

@dataclass
class LoraArguments:
    """LoRA参数"""
    lora_r: int = 64
    lora_alpha: int = 16
    lora_dropout: float = 0.05
    lora_target_modules: List[str] = field(default_factory=lambda: [
        "q_proj", "k_proj", "v_proj", "o_proj",
        "gate_proj", "up_proj", "down_proj"
    ])

class InfraredDataset(Dataset):
    """红外图像数据集"""
    
    def __init__(self, data_path: str, processor, max_length: int = 8192):
        self.processor = processor
        self.max_length = max_length
        
        # 加载数据
        with open(data_path, 'r', encoding='utf-8') as f:
            self.data = json.load(f)
        
        logger.info(f"加载了 {len(self.data)} 个训练样本")
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        item = self.data[idx]
        
        # 获取视频路径
        video_path = item['videos'][0]
        
        # 获取对话内容
        messages = item['messages']
        user_content = messages[0]['content']
        assistant_content = messages[1]['content']
        
        # 构建输入文本
        input_text = f"<|im_start|>user\n{user_content}<|im_end|>\n<|im_start|>assistant\n"
        target_text = f"{assistant_content}<|im_end|>"
        full_text = input_text + target_text
        
        try:
            # 处理视频
            inputs = self.processor(
                text=input_text,
                videos=[video_path],
                return_tensors="pt",
                max_length=self.max_length,
                truncation=True,
                padding=True
            )
            
            # 处理目标文本
            target_inputs = self.processor(
                text=full_text,
                return_tensors="pt",
                max_length=self.max_length,
                truncation=True,
                padding=True
            )
            
            # 创建标签
            labels = target_inputs["input_ids"].clone()
            input_length = inputs["input_ids"].shape[1]
            labels[:, :input_length] = -100  # 忽略输入部分的损失
            
            return {
                "input_ids": inputs["input_ids"].squeeze(0),
                "attention_mask": inputs["attention_mask"].squeeze(0),
                "pixel_values": inputs.get("pixel_values", torch.tensor([])),
                "labels": labels.squeeze(0)
            }
            
        except Exception as e:
            logger.warning(f"处理样本 {idx} 时出错: {e}")
            # 返回一个空样本
            return {
                "input_ids": torch.tensor([]),
                "attention_mask": torch.tensor([]),
                "pixel_values": torch.tensor([]),
                "labels": torch.tensor([])
            }

def setup_model_and_processor(model_args: ModelArguments, lora_args: LoraArguments):
    """设置模型和处理器"""
    
    logger.info("加载模型和处理器...")
    
    # 加载处理器
    processor = Qwen2VLProcessor.from_pretrained(
        model_args.model_name_or_path,
        cache_dir=model_args.cache_dir
    )
    
    # 加载模型
    model = Qwen2VLForConditionalGeneration.from_pretrained(
        model_args.model_name_or_path,
        cache_dir=model_args.cache_dir,
        torch_dtype=torch.bfloat16,
        device_map="auto",
        attn_implementation="flash_attention_2" if model_args.use_flash_attention_2 else None
    )
    
    # 配置LoRA
    lora_config = LoraConfig(
        task_type=TaskType.CAUSAL_LM,
        r=lora_args.lora_r,
        lora_alpha=lora_args.lora_alpha,
        lora_dropout=lora_args.lora_dropout,
        target_modules=lora_args.lora_target_modules,
        bias="none"
    )
    
    # 应用LoRA
    model = get_peft_model(model, lora_config)
    model.print_trainable_parameters()
    
    return model, processor

def main():
    """主函数"""
    
    logger.info("🚀 开始红外图像微小目标检测微调训练")
    
    # 参数设置
    model_args = ModelArguments()
    data_args = DataArguments()
    lora_args = LoraArguments()
    
    # 检查数据文件
    if not os.path.exists(data_args.train_data_path):
        logger.error(f"训练数据文件不存在: {data_args.train_data_path}")
        return
    
    # 设置模型和处理器
    model, processor = setup_model_and_processor(model_args, lora_args)
    
    # 创建数据集
    logger.info("创建数据集...")
    train_dataset = InfraredDataset(
        data_args.train_data_path,
        processor,
        data_args.max_seq_length
    )
    
    # 训练参数
    training_args = TrainingArguments(
        output_dir="/home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/output/checkpoints",
        num_train_epochs=2,
        per_device_train_batch_size=1,
        gradient_accumulation_steps=8,
        learning_rate=5e-5,
        lr_scheduler_type="cosine",
        warmup_ratio=0.1,
        bf16=True,
        logging_steps=10,
        save_steps=100,
        save_total_limit=3,
        remove_unused_columns=False,
        dataloader_pin_memory=False,
        report_to=None,
        overwrite_output_dir=True
    )
    
    # 数据整理器
    data_collator = DataCollatorForSeq2Seq(
        tokenizer=processor.tokenizer,
        model=model,
        padding=True,
        return_tensors="pt"
    )
    
    # 创建训练器
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=train_dataset,
        data_collator=data_collator,
        tokenizer=processor.tokenizer
    )
    
    # 开始训练
    logger.info("开始训练...")
    trainer.train()
    
    # 保存模型
    logger.info("保存模型...")
    trainer.save_model()
    processor.save_pretrained(training_args.output_dir)
    
    logger.info("✅ 训练完成！")
    logger.info(f"模型保存在: {training_args.output_dir}")

if __name__ == "__main__":
    main()
