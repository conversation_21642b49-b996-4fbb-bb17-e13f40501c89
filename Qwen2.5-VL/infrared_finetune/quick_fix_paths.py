#!/usr/bin/env python3
"""
快速修复视频路径
"""

import json
import os

def main():
    # 修复训练数据路径
    train_file = '/home/<USER>/Qwen/Qwen2.5-VL/LLaMA-Factory/data/infrared_detection_train.json'
    video_base_path = '/home/<USER>/Qwen/Qwen2.5-VL/LLaMA-Factory/data/videos'

    print("开始修复训练数据路径...")
    
    with open(train_file, 'r', encoding='utf-8') as f:
        train_data = json.load(f)

    fixed_count = 0
    for item in train_data:
        if 'videos' in item:
            new_videos = []
            for video_path in item['videos']:
                if video_path.startswith('videos/'):
                    video_name = video_path.replace('videos/', '')
                    absolute_path = os.path.join(video_base_path, video_name)
                    new_videos.append(absolute_path)
                    fixed_count += 1
                else:
                    new_videos.append(video_path)
            item['videos'] = new_videos

    with open(train_file, 'w', encoding='utf-8') as f:
        json.dump(train_data, f, ensure_ascii=False, indent=2)

    print(f'训练数据路径修复完成，修复了 {fixed_count} 个路径')
    
    # 验证修复结果
    print("验证修复结果...")
    sample_video = train_data[0]['videos'][0]
    exists = os.path.exists(sample_video)
    print(f"示例视频路径: {sample_video}")
    print(f"文件存在: {exists}")

if __name__ == "__main__":
    main()
