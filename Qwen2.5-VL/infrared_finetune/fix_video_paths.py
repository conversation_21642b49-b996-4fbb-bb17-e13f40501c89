#!/usr/bin/env python3
"""
修复训练数据中的视频路径
将相对路径转换为绝对路径
"""

import json
import os
from pathlib import Path

def fix_video_paths():
    """修复视频路径"""
    
    # 文件路径
    train_file = "/home/<USER>/Qwen/Qwen2.5-VL/LLaMA-Factory/data/infrared_detection_train.json"
    test_file = "/home/<USER>/Qwen/Qwen2.5-VL/LLaMA-Factory/data/infrared_detection_test.json"
    
    # 视频目录的绝对路径
    video_base_path = "/home/<USER>/Qwen/Qwen2.5-VL/LLaMA-Factory/data/videos"
    
    # 修复训练数据
    if os.path.exists(train_file):
        print(f"修复训练数据: {train_file}")
        
        with open(train_file, 'r', encoding='utf-8') as f:
            train_data = json.load(f)
        
        for item in train_data:
            if 'videos' in item:
                new_videos = []
                for video_path in item['videos']:
                    if video_path.startswith('videos/'):
                        # 转换为绝对路径
                        video_name = video_path.replace('videos/', '')
                        absolute_path = os.path.join(video_base_path, video_name)
                        new_videos.append(absolute_path)
                    else:
                        new_videos.append(video_path)
                item['videos'] = new_videos
        
        # 保存修复后的数据
        with open(train_file, 'w', encoding='utf-8') as f:
            json.dump(train_data, f, ensure_ascii=False, indent=2)
        
        print(f"训练数据修复完成，共处理 {len(train_data)} 个样本")
    
    # 修复测试数据
    if os.path.exists(test_file):
        print(f"修复测试数据: {test_file}")
        
        with open(test_file, 'r', encoding='utf-8') as f:
            test_data = json.load(f)
        
        for item in test_data:
            if 'videos' in item:
                new_videos = []
                for video_path in item['videos']:
                    if video_path.startswith('videos/'):
                        # 转换为绝对路径
                        video_name = video_path.replace('videos/', '')
                        absolute_path = os.path.join(video_base_path, video_name)
                        new_videos.append(absolute_path)
                    else:
                        new_videos.append(video_path)
                item['videos'] = new_videos
        
        # 保存修复后的数据
        with open(test_file, 'w', encoding='utf-8') as f:
            json.dump(test_data, f, ensure_ascii=False, indent=2)
        
        print(f"测试数据修复完成，共处理 {len(test_data)} 个样本")
    
    # 验证修复结果
    print("\n验证修复结果...")
    
    with open(train_file, 'r', encoding='utf-8') as f:
        train_data = json.load(f)
    
    # 检查前几个样本的视频路径
    for i, item in enumerate(train_data[:3]):
        if 'videos' in item:
            for video_path in item['videos']:
                exists = os.path.exists(video_path)
                print(f"样本 {i+1}: {video_path} - 存在: {exists}")
    
    print("路径修复完成！")

if __name__ == "__main__":
    fix_video_paths()
