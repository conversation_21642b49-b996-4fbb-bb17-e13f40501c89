"""
红外视频目标检测LoRA微调训练脚本
"""
import os
import json
import torch
import logging
from pathlib import Path
from transformers import (
    Qwen2_5_VLForConditionalGeneration,
    AutoProcessor,
    TrainingArguments,
    Trainer,
    DataCollatorWithPadding
)
from peft import LoraConfig, TaskType, get_peft_model
from datasets import Dataset
from qwen_vl_utils import process_vision_info

# 设置环境
os.environ["CUDA_VISIBLE_DEVICES"] = "2"
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    """LoRA微调主函数"""
    logger.info("🚀 开始红外视频目标检测LoRA微调...")
    
    # 路径配置
    model_path = "/home/<USER>/Qwen/Qwen2.5-VL/Qwen2.5-VL-7B-Instruct"
    data_path = "data/infrared_video_train.json"
    output_path = "output/lora_checkpoints"
    
    # 创建输出目录
    Path(output_path).mkdir(parents=True, exist_ok=True)
    
    # 加载数据
    logger.info("📊 加载训练数据...")
    with open(data_path, 'r') as f:
        train_data = json.load(f)
    
    # 使用完整的训练数据集
    logger.info(f"✅ 使用完整数据集: {len(train_data)} 个样本进行训练")
    
    # 加载processor（包含tokenizer和图像处理器）
    logger.info("🔤 加载processor...")
    processor = AutoProcessor.from_pretrained(model_path, trust_remote_code=True)

    # 预处理数据 - 正确处理视频数据
    logger.info("📝 预处理数据...")
    processed_data = []

    for item in train_data:
        try:
            conversations = item['conversations']
            video_path = item['video']

            # 构建消息格式（Qwen2.5-VL的标准格式）
            messages = [
                {
                    "role": "user",
                    "content": [
                        {"type": "video", "video": video_path},
                        {"type": "text", "text": conversations[0]['value'].replace('<video>\n', '')}
                    ]
                },
                {
                    "role": "assistant",
                    "content": conversations[1]['value']
                }
            ]

            # 使用processor处理消息
            text = processor.apply_chat_template(messages, tokenize=False, add_generation_prompt=False)

            # 处理视频信息
            image_inputs, video_inputs = process_vision_info(messages)

            processed_data.append({
                "text": text,
                "video_path": video_path,
                "messages": messages
            })

        except Exception as e:
            logger.warning(f"处理样本失败: {str(e)}")
            continue

    logger.info(f"成功处理 {len(processed_data)} 个样本")

    # 转换为Dataset
    dataset = Dataset.from_list(processed_data)

    def tokenize_function(examples):
        # 只对文本进行tokenize，视频处理在训练时进行
        tokenized = processor.tokenizer(
            examples["text"],
            truncation=True,
            padding=False,
            max_length=4096,  # 增加长度以容纳视频tokens
            return_tensors=None
        )
        tokenized["labels"] = tokenized["input_ids"].copy()
        return tokenized

    tokenized_dataset = dataset.map(tokenize_function, batched=True, remove_columns=["text"])
    
    # 加载模型
    logger.info("🤖 加载模型...")
    model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
        model_path,
        torch_dtype=torch.bfloat16,
        device_map={"": "cuda:0"},
        trust_remote_code=True,
        attn_implementation="flash_attention_2"
    )
    
    # 配置LoRA（修复版 - 参考官方配置）
    logger.info("⚙️ 配置LoRA...")
    lora_config = LoraConfig(
        task_type=TaskType.CAUSAL_LM,
        target_modules=["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"],
        inference_mode=False,
        r=64,  # 增加rank
        lora_alpha=64,
        lora_dropout=0.05,  # 降低dropout
        bias="none",
    )
    
    # 应用LoRA
    model = get_peft_model(model, lora_config)
    model.print_trainable_parameters()
    
    # 训练参数（修复版 - 参考官方配置）
    training_args = TrainingArguments(
        output_dir=output_path,
        per_device_train_batch_size=2,  # 增加批次大小
        gradient_accumulation_steps=8,  # 增加梯度累积
        num_train_epochs=1,  # 减少训练轮数
        learning_rate=2e-5,  # 降低学习率
        weight_decay=0.01,  # 添加权重衰减
        warmup_ratio=0.03,  # 添加warmup
        max_grad_norm=1.0,  # 添加梯度裁剪
        lr_scheduler_type="cosine",  # 使用cosine学习率调度
        logging_steps=10,
        save_steps=100,
        save_strategy="steps",
        save_total_limit=3,
        remove_unused_columns=False,
        bf16=True,
        dataloader_num_workers=4,  # 增加数据加载workers
        gradient_checkpointing=True,  # 启用梯度检查点
        report_to="none",
    )
    
    # 数据整理器
    data_collator = DataCollatorForLanguageModeling(
        tokenizer=tokenizer,
        mlm=False,
    )
    
    # 创建训练器
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=tokenized_dataset,
        tokenizer=tokenizer,
        data_collator=data_collator,
    )
    
    # 开始训练
    logger.info("🎯 开始LoRA训练...")
    trainer.train()
    
    # 保存模型
    logger.info("💾 保存LoRA权重...")
    trainer.save_model()
    tokenizer.save_pretrained(output_path)
    
    logger.info("🎉 LoRA微调完成!")
    logger.info(f"📁 模型保存在: {output_path}")
    
    # 验证保存的文件
    saved_files = list(Path(output_path).glob("*"))
    logger.info(f"📋 保存的文件: {[f.name for f in saved_files]}")

if __name__ == "__main__":
    main()
