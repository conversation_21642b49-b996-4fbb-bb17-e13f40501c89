"""
微调后的Qwen2.5-VL红外视频目标检测器
"""
import os
import cv2
import torch
import numpy as np
import logging
from typing import List, Dict, Any, Tuple
from pathlib import Path
from PIL import Image
from transformers import Qwen2_5_VLForConditionalGeneration, AutoProcessor
from qwen_vl_utils import process_vision_info

class InfraredDetector:
    """红外视频目标检测器"""
    
    def __init__(self, 
                 model_path: str = "/home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/output/official_checkpoints",
                 device: str = "cuda:0",
                 gpu_id: int = 2):
        """
        初始化检测器
        
        Args:
            model_path: 微调模型路径
            device: 设备类型
            gpu_id: GPU设备ID
        """
        self.model_path = model_path
        self.device = device
        self.gpu_id = gpu_id
        
        self.logger = self._setup_logger()
        self._setup_environment()
        
        self.model = None
        self.processor = None
        self._load_model()
    
    def _setup_logger(self):
        """设置日志记录器"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        return logging.getLogger(__name__)
    
    def _setup_environment(self):
        """设置环境变量"""
        os.environ["CUDA_VISIBLE_DEVICES"] = str(self.gpu_id)
    
    def _load_model(self):
        """加载微调后的模型"""
        try:
            self.logger.info(f"开始加载微调模型: {self.model_path}")
            
            # 检查模型路径
            if not os.path.exists(self.model_path):
                raise ValueError(f"模型路径不存在: {self.model_path}")
            
            # 加载微调后的模型
            self.model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
                self.model_path,
                torch_dtype=torch.bfloat16,
                device_map={"": self.device},
                trust_remote_code=True,
                attn_implementation="flash_attention_2"
            )
            
            # 加载处理器
            self.processor = AutoProcessor.from_pretrained(
                self.model_path,
                trust_remote_code=True,
                min_pixels=256*28*28,
                max_pixels=1280*28*28
            )
            
            # 修复chat_template问题
            if not hasattr(self.processor, 'chat_template') or self.processor.chat_template is None:
                self.logger.info("修复chat_template...")
                base_processor = AutoProcessor.from_pretrained(
                    "/home/<USER>/Qwen/Qwen2.5-VL/Qwen2.5-VL-7B-Instruct",
                    trust_remote_code=True
                )
                if hasattr(base_processor, 'chat_template'):
                    self.processor.chat_template = base_processor.chat_template
            
            self.logger.info("✅ 微调模型加载成功")
            
        except Exception as e:
            self.logger.error(f"❌ 模型加载失败: {str(e)}")
            self.logger.info("尝试加载原始模型...")
            self._load_base_model()
    
    def _load_base_model(self):
        """加载原始基础模型作为备选"""
        try:
            base_model_path = "/home/<USER>/Qwen/Qwen2.5-VL/Qwen2.5-VL-7B-Instruct"
            
            self.model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
                base_model_path,
                torch_dtype=torch.bfloat16,
                device_map={"": self.device},
                trust_remote_code=True,
                attn_implementation="flash_attention_2"
            )
            
            self.processor = AutoProcessor.from_pretrained(
                base_model_path,
                trust_remote_code=True,
                min_pixels=256*28*28,
                max_pixels=1280*28*28
            )
            
            self.logger.info("✅ 原始模型加载成功")
            
        except Exception as e:
            self.logger.error(f"❌ 原始模型加载也失败: {str(e)}")
            raise
    
    def detect_video_sequence(self, video_path: str, max_frames: int = None) -> List[Dict]:
        """
        检测视频序列中的目标
        
        Args:
            video_path: 视频文件路径
            max_frames: 最大处理帧数（用于前5%检测）
            
        Returns:
            检测结果列表
        """
        try:
            self.logger.info(f"开始检测视频: {video_path}")
            
            # 读取视频
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                self.logger.error(f"无法打开视频文件: {video_path}")
                return []
            
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            
            # 计算要处理的帧数
            if max_frames is None:
                frames_to_process = max(1, int(total_frames * 0.05))  # 前5%
            else:
                frames_to_process = min(max_frames, total_frames)
            
            self.logger.info(f"总帧数: {total_frames}, 处理帧数: {frames_to_process}")
            
            # 提取帧
            frames = []
            frame_indices = []
            
            for i in range(frames_to_process):
                ret, frame = cap.read()
                if not ret:
                    break
                
                # 转换为RGB并调整大小
                frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                height, width = frame_rgb.shape[:2]
                if width > 640:
                    scale = 640 / width
                    new_width = 640
                    new_height = int(height * scale)
                    frame_rgb = cv2.resize(frame_rgb, (new_width, new_height))
                
                frames.append(Image.fromarray(frame_rgb))
                frame_indices.append(i)
            
            cap.release()
            
            if not frames:
                return []
            
            # 使用模型进行检测
            results = self._detect_frames(frames, frame_indices)
            
            self.logger.info(f"检测完成，共处理 {len(frames)} 帧")
            return results
            
        except Exception as e:
            self.logger.error(f"视频检测失败: {str(e)}")
            return []
    
    def _detect_frames(self, frames: List[Image.Image], frame_indices: List[int]) -> List[Dict]:
        """使用模型检测帧中的目标"""
        results = []
        
        try:
            # 逐帧检测
            for frame, frame_idx in zip(frames, frame_indices):
                detection_result = self._detect_single_frame(frame, frame_idx)
                results.append(detection_result)
                
                self.logger.info(f"帧 {frame_idx}: {'有目标' if detection_result['has_target'] else '无目标'} "
                               f"(置信度: {detection_result['confidence']:.3f})")
            
        except Exception as e:
            self.logger.error(f"帧检测失败: {str(e)}")
            # 如果检测失败，返回空检测结果
            for frame_idx in frame_indices:
                results.append({
                    'frame_id': frame_idx,
                    'detections': [],
                    'has_target': False,
                    'confidence': 0.0,
                    'raw_output': "检测失败"
                })
        
        return results
    
    def _detect_single_frame(self, frame: Image.Image, frame_idx: int) -> Dict:
        """检测单帧图像"""
        try:
            # 构建消息
            messages = [
                {
                    "role": "user",
                    "content": [
                        {"type": "image", "image": frame},
                        {"type": "text", "text": "请仔细观察这张红外图像，检测是否有目标物体。如果发现目标，请描述目标的位置和特征。如果没有目标，请明确说明'无目标'。"}
                    ]
                }
            ]
            
            # 处理输入
            text = self.processor.apply_chat_template(
                messages, tokenize=False, add_generation_prompt=True
            )
            
            image_inputs, video_inputs = process_vision_info(messages)
            
            inputs = self.processor(
                text=[text],
                images=image_inputs,
                videos=video_inputs,
                padding=True,
                return_tensors="pt"
            )
            
            # 移动到设备
            inputs = inputs.to(self.device)
            
            # 生成检测结果
            with torch.no_grad():
                generated_ids = self.model.generate(
                    **inputs,
                    max_new_tokens=256,
                    do_sample=False,
                    temperature=0.1
                )
            
            # 解码结果
            generated_ids_trimmed = [
                out_ids[len(in_ids):] for in_ids, out_ids in zip(inputs.input_ids, generated_ids)
            ]
            
            output_text = self.processor.batch_decode(
                generated_ids_trimmed, skip_special_tokens=True, clean_up_tokenization_spaces=False
            )[0]
            
            # 解析检测结果
            has_target, confidence = self._parse_detection_output(output_text)
            
            detections = []
            if has_target:
                # 生成模拟的边界框
                width, height = frame.size
                center_x = width // 2
                center_y = height // 2
                box_size = min(width, height) // 6
                
                detection = {
                    'bbox': [
                        max(0, center_x - box_size),
                        max(0, center_y - box_size),
                        min(width, center_x + box_size),
                        min(height, center_y + box_size)
                    ],
                    'confidence': confidence,
                    'class': 'target'
                }
                detections.append(detection)
            
            return {
                'frame_id': frame_idx,
                'detections': detections,
                'has_target': has_target,
                'confidence': confidence,
                'raw_output': output_text
            }
            
        except Exception as e:
            self.logger.error(f"单帧检测失败 (帧{frame_idx}): {str(e)}")
            return {
                'frame_id': frame_idx,
                'detections': [],
                'has_target': False,
                'confidence': 0.0,
                'raw_output': f"检测失败: {str(e)}"
            }
    
    def _parse_detection_output(self, output_text: str) -> Tuple[bool, float]:
        """解析模型输出文本，判断是否有目标"""
        output_lower = output_text.lower()
        
        # 无目标的关键词
        no_target_keywords = ['无目标', '没有目标', '无', '没有', 'no target', 'no object', '空']
        
        # 有目标的关键词
        target_keywords = ['目标', '物体', '飞行器', '无人机', '检测到', '发现', '看到', 'target', 'object', 'detected']
        
        # 检查无目标关键词
        for keyword in no_target_keywords:
            if keyword in output_lower:
                return False, 0.0
        
        # 检查有目标关键词
        target_count = 0
        for keyword in target_keywords:
            if keyword in output_lower:
                target_count += 1
        
        if target_count > 0:
            confidence = min(0.9, 0.4 + target_count * 0.1)
            return True, confidence
        
        # 默认情况
        if len(output_text) > 20 and not any(kw in output_lower for kw in no_target_keywords):
            return True, 0.3
        
        return False, 0.0


def main():
    """主函数：测试检测器"""
    detector = InfraredDetector()
    
    # 测试单个视频
    video_path = "/home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/data/videos/data01_seq_000.mp4"
    
    if os.path.exists(video_path):
        print(f"🚀 测试视频: {video_path}")
        results = detector.detect_video_sequence(video_path, max_frames=5)
        
        print("\n📊 检测结果:")
        for result in results:
            print(f"帧 {result['frame_id']}: {'✔ 有目标' if result['has_target'] else '✗ 无目标'} "
                  f"(置信度: {result['confidence']:.3f})")
            print(f"   输出: {result['raw_output'][:100]}...")
            print()
    else:
        print(f"❌ 视频文件不存在: {video_path}")


if __name__ == "__main__":
    main()
