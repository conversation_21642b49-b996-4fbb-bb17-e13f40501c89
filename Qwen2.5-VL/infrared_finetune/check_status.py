#!/usr/bin/env python3
"""
检查训练状态
"""

import os
import time
import psutil

def check_training_status():
    """检查训练状态"""
    print("🔍 检查训练状态...")
    
    # 检查GPU使用情况
    try:
        import torch
        if torch.cuda.is_available():
            print(f"🔥 GPU可用: {torch.cuda.device_count()}个")
            for i in range(torch.cuda.device_count()):
                memory_allocated = torch.cuda.memory_allocated(i) / 1024**3
                memory_reserved = torch.cuda.memory_reserved(i) / 1024**3
                print(f"   GPU {i}: 已分配 {memory_allocated:.2f}GB, 已保留 {memory_reserved:.2f}GB")
        else:
            print("❌ GPU不可用")
    except:
        print("❌ 无法检查GPU状态")
    
    # 检查Python进程
    python_processes = []
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if 'python' in proc.info['name'].lower():
                cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                if 'minimal_train.py' in cmdline or 'train.py' in cmdline:
                    python_processes.append({
                        'pid': proc.info['pid'],
                        'cmdline': cmdline
                    })
        except:
            continue
    
    if python_processes:
        print(f"🐍 发现 {len(python_processes)} 个训练进程:")
        for proc in python_processes:
            print(f"   PID {proc['pid']}: {proc['cmdline'][:100]}...")
    else:
        print("❌ 没有发现训练进程")
    
    # 检查输出目录
    output_dirs = [
        "/home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/output/minimal_checkpoints",
        "/home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/output/checkpoints",
        "/home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/output/simple_checkpoints"
    ]
    
    for output_dir in output_dirs:
        if os.path.exists(output_dir):
            files = os.listdir(output_dir)
            if files:
                print(f"📁 {output_dir}: {len(files)} 个文件")
                for f in files[:5]:  # 只显示前5个文件
                    print(f"   - {f}")
            else:
                print(f"📁 {output_dir}: 空目录")
        else:
            print(f"📁 {output_dir}: 不存在")

if __name__ == "__main__":
    check_training_status()
