#!/bin/bash

# 红外图像微调训练脚本
# 基于官方qwen-vl-finetune框架

# 设置环境变量
export CUDA_VISIBLE_DEVICES=0
export TOKENIZERS_PARALLELISM=false

# 激活conda环境
source /home/<USER>/miniconda3/etc/profile.d/conda.sh
conda activate Qwen

# 训练配置
MODEL_PATH="/home/<USER>/Qwen/Qwen2.5-VL/Qwen2.5-VL-7B-Instruct"
DATA_PATH="/home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/data"
OUTPUT_DIR="/home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/output/official_checkpoints"

# 创建输出目录
mkdir -p ${OUTPUT_DIR}

# 训练参数
LEARNING_RATE=1e-5
BATCH_SIZE=1
GRAD_ACCUM_STEPS=16  # 增加梯度累积以减少内存使用
NUM_EPOCHS=1
MAX_LENGTH=1024  # 减少序列长度

echo "🚀 开始红外图像微调训练"
echo "📁 模型路径: ${MODEL_PATH}"
echo "📁 数据路径: ${DATA_PATH}"
echo "📁 输出路径: ${OUTPUT_DIR}"

# 运行训练
python qwenvl/train/train_qwen.py \
    --model_name_or_path ${MODEL_PATH} \
    --dataset_use infrared_video_train \
    --data_flatten True \
    --tune_mm_vision False \
    --tune_mm_mlp True \
    --tune_mm_llm True \
    --bf16 True \
    --output_dir ${OUTPUT_DIR} \
    --num_train_epochs ${NUM_EPOCHS} \
    --per_device_train_batch_size ${BATCH_SIZE} \
    --gradient_accumulation_steps ${GRAD_ACCUM_STEPS} \
    --save_strategy "steps" \
    --save_steps 50 \
    --save_total_limit 2 \
    --learning_rate ${LEARNING_RATE} \
    --weight_decay 0.0 \
    --warmup_ratio 0.03 \
    --lr_scheduler_type "cosine" \
    --logging_steps 5 \
    --model_max_length ${MAX_LENGTH} \
    --gradient_checkpointing True \
    --dataloader_num_workers 0

echo "✅ 训练完成！"
echo "📁 模型保存在: ${OUTPUT_DIR}"
