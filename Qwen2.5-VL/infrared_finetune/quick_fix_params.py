"""
快速修复训练参数的脚本
只修改关键参数，不改变模型加载方式
"""

import os
import re

def fix_training_parameters():
    """修复训练参数"""
    
    script_path = "train_lora.py"
    
    print("🔧 修复训练参数...")
    
    with open(script_path, 'r') as f:
        content = f.read()
    
    # 修复学习率
    content = re.sub(
        r'learning_rate=1e-4',
        'learning_rate=2e-5',
        content
    )
    
    # 修复训练轮数
    content = re.sub(
        r'num_train_epochs=2',
        'num_train_epochs=1',
        content
    )
    
    # 修复批次大小和梯度累积
    content = re.sub(
        r'per_device_train_batch_size=1',
        'per_device_train_batch_size=1',
        content
    )
    
    content = re.sub(
        r'gradient_accumulation_steps=4',
        'gradient_accumulation_steps=16',
        content
    )
    
    # 添加权重衰减
    content = re.sub(
        r'remove_unused_columns=False,',
        '''remove_unused_columns=False,
        weight_decay=0.01,
        warmup_ratio=0.03,
        max_grad_norm=1.0,
        lr_scheduler_type="cosine",
        gradient_checkpointing=True,''',
        content
    )
    
    # 修复LoRA配置
    content = re.sub(
        r'r=32,',
        'r=64,',
        content
    )
    
    content = re.sub(
        r'lora_alpha=32,',
        'lora_alpha=64,',
        content
    )
    
    content = re.sub(
        r'lora_dropout=0.1,',
        'lora_dropout=0.05,',
        content
    )
    
    # 扩展target_modules
    content = re.sub(
        r'target_modules=\["q_proj", "k_proj", "v_proj", "o_proj"\],',
        'target_modules=["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"],',
        content
    )
    
    # 保存修复后的文件
    backup_path = script_path.replace('.py', '_backup.py')
    if not os.path.exists(backup_path):
        os.rename(script_path, backup_path)
        print(f"📁 原文件备份: {backup_path}")
    
    with open(script_path, 'w') as f:
        f.write(content)
    
    print(f"✅ 训练参数修复完成: {script_path}")
    print("🎯 主要修改:")
    print("   - 学习率: 1e-4 → 2e-5")
    print("   - 训练轮数: 2 → 1")
    print("   - 梯度累积: 4 → 16")
    print("   - LoRA rank: 32 → 64")
    print("   - 添加权重衰减、warmup、梯度裁剪")
    print("   - 扩展LoRA target_modules")

if __name__ == "__main__":
    fix_training_parameters()
    print("\n🚀 现在可以运行修复后的训练:")
    print("   python train_lora.py")
