#!/usr/bin/env python3
"""
红外图像微小目标检测微调主程序
独立的训练流程，不依赖LLaMA-Factory
"""

import os
import sys
import logging
import argparse
import subprocess
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(str(Path(__file__).parent))

from data_preprocessor import InfraredDataPreprocessor

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('main.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class InfraredFineTuner:
    """红外图像微调器"""
    
    def __init__(self):
        self.data_root = "/home/<USER>/Qwen/Qwen2.5-VL/tiaozhanbei_datasets"
        self.project_root = Path(__file__).parent
        self.preprocessor = InfraredDataPreprocessor(self.data_root, self.project_root)
        
        logger.info("微调器初始化完成")
        logger.info(f"数据根目录: {self.data_root}")
        logger.info(f"项目根目录: {self.project_root}")
    
    def preprocess_data(self, force_regenerate: bool = False):
        """数据预处理"""
        logger.info("=" * 60)
        logger.info("开始数据预处理...")
        logger.info("=" * 60)
        
        # 检查是否已有处理好的数据
        train_file = self.project_root / "data" / "infrared_detection_train.json"
        test_file = self.project_root / "data" / "infrared_detection_test.json"
        
        if not force_regenerate and train_file.exists() and test_file.exists():
            logger.info(f"训练数据文件已存在: {train_file}")
            logger.info(f"测试数据文件已存在: {test_file}")
            logger.info("如需重新生成，请使用 --force-regenerate 参数")
            return True
        
        try:
            # 执行数据预处理
            success = self.preprocessor.process_all_data()
            
            if success:
                logger.info("✅ 数据预处理完成")
                return True
            else:
                logger.error("❌ 数据预处理失败")
                return False
                
        except Exception as e:
            logger.error(f"数据预处理过程中出现错误: {e}")
            return False
    
    def start_training(self):
        """开始训练"""
        logger.info("=" * 60)
        logger.info("开始微调训练...")
        logger.info("=" * 60)
        
        # 检查训练数据是否存在
        train_file = self.project_root / "data" / "infrared_detection_train.json"
        if not train_file.exists():
            logger.error(f"训练数据文件不存在: {train_file}")
            logger.error("请先运行数据预处理")
            return False
        
        # 运行训练脚本
        train_script = self.project_root / "train.py"
        if not train_script.exists():
            logger.error(f"训练脚本不存在: {train_script}")
            return False
        
        try:
            logger.info(f"执行训练脚本: {train_script}")
            
            # 运行训练
            result = subprocess.run(
                [sys.executable, str(train_script)],
                cwd=str(self.project_root),
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                logger.info("✅ 训练完成")
                logger.info("输出:")
                logger.info(result.stdout)
                return True
            else:
                logger.error("❌ 训练失败")
                logger.error("错误信息:")
                logger.error(result.stderr)
                return False
                
        except Exception as e:
            logger.error(f"训练过程中出现错误: {e}")
            return False
    
    def run_evaluation(self):
        """运行评估"""
        logger.info("=" * 60)
        logger.info("开始模型评估...")
        logger.info("=" * 60)
        
        # 检查模型是否存在
        model_dir = self.project_root / "output" / "checkpoints"
        if not model_dir.exists():
            logger.error(f"模型目录不存在: {model_dir}")
            logger.error("请先完成训练")
            return False
        
        # 运行评估脚本
        eval_script = self.project_root / "evaluate.py"
        if not eval_script.exists():
            logger.error(f"评估脚本不存在: {eval_script}")
            return False
        
        try:
            logger.info(f"执行评估脚本: {eval_script}")
            
            # 运行评估
            result = subprocess.run(
                [sys.executable, str(eval_script), "--lora_path", str(model_dir)],
                cwd=str(self.project_root),
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                logger.info("✅ 评估完成")
                logger.info("输出:")
                logger.info(result.stdout)
                return True
            else:
                logger.error("❌ 评估失败")
                logger.error("错误信息:")
                logger.error(result.stderr)
                return False
                
        except Exception as e:
            logger.error(f"评估过程中出现错误: {e}")
            return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="红外图像微小目标检测微调")
    parser.add_argument("--preprocess-only", action="store_true", help="仅执行数据预处理")
    parser.add_argument("--train-only", action="store_true", help="仅执行训练")
    parser.add_argument("--eval-only", action="store_true", help="仅执行评估")
    parser.add_argument("--force-regenerate", action="store_true", help="强制重新生成数据")
    
    args = parser.parse_args()
    
    # 创建微调器
    finetuner = InfraredFineTuner()
    
    logger.info("=" * 80)
    logger.info("开始红外图像微小目标检测微调流程")
    logger.info("=" * 80)
    
    success = True
    
    # 数据预处理
    if not args.train_only and not args.eval_only:
        success = finetuner.preprocess_data(args.force_regenerate)
        if not success:
            logger.error("数据预处理失败，终止流程")
            return
    
    if args.preprocess_only:
        logger.info("仅执行数据预处理，流程结束")
        return
    
    # 训练
    if not args.eval_only and success:
        success = finetuner.start_training()
        if not success:
            logger.error("训练失败，终止流程")
            return
    
    if args.train_only:
        logger.info("仅执行训练，流程结束")
        return
    
    # 评估
    if success:
        success = finetuner.run_evaluation()
    
    # 总结
    logger.info("=" * 80)
    if success:
        logger.info("✅ 微调流程完成！")
    else:
        logger.info("❌ 微调流程失败！")
    logger.info("=" * 80)
    
    # 输出项目信息
    project_root = Path(__file__).parent
    logger.info(f"项目目录: {project_root}")
    logger.info(f"训练数据: {project_root}/data/infrared_detection_train.json")
    logger.info(f"测试数据: {project_root}/data/infrared_detection_test.json")
    logger.info(f"模型输出: {project_root}/output/checkpoints")
    logger.info(f"评估结果: {project_root}/output/evaluation_results.json")

if __name__ == "__main__":
    main()
