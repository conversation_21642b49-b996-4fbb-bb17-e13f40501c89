#!/usr/bin/env python3
"""
红外图像微小目标检测微调主程序
整合数据预处理和微调训练流程
"""

import os
import sys
import logging
import argparse
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(str(Path(__file__).parent))

from data_preprocessor import InfraredDataPreprocessor
from finetune_config import FineTuneConfig

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('infrared_finetune.log')
    ]
)
logger = logging.getLogger(__name__)

class InfraredFineTuner:
    """红外图像微小目标检测微调器"""
    
    def __init__(self, data_root: str, project_root: str = None):
        """
        初始化微调器
        
        Args:
            data_root: 原始数据根目录
            project_root: 项目根目录，默认为当前脚本所在目录
        """
        if project_root is None:
            project_root = Path(__file__).parent
        
        self.data_root = Path(data_root)
        self.project_root = Path(project_root)
        
        # 确保项目目录存在
        self.project_root.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"微调器初始化完成")
        logger.info(f"数据根目录: {self.data_root}")
        logger.info(f"项目根目录: {self.project_root}")
    
    def preprocess_data(self, force_regenerate: bool = False):
        """
        数据预处理步骤
        
        Args:
            force_regenerate: 是否强制重新生成数据
        """
        logger.info("=" * 60)
        logger.info("开始数据预处理...")
        logger.info("=" * 60)
        
        output_dir = self.project_root / "data"
        training_data_file = output_dir / "infrared_detection_train.json"
        test_data_file = output_dir / "infrared_detection_test.json"

        # 检查是否已存在训练数据
        if training_data_file.exists() and test_data_file.exists() and not force_regenerate:
            logger.info(f"训练数据文件已存在: {training_data_file}")
            logger.info(f"测试数据文件已存在: {test_data_file}")
            logger.info("如需重新生成，请使用 --force-regenerate 参数")
            return str(training_data_file), str(test_data_file)
        
        try:
            # 创建数据预处理器
            preprocessor = InfraredDataPreprocessor(
                data_root=str(self.data_root),
                output_dir=str(output_dir)
            )
            
            # 生成训练数据和测试数据
            training_data_file, test_data_file = preprocessor.generate_training_data()

            logger.info("数据预处理完成！")
            return training_data_file, test_data_file
            
        except Exception as e:
            logger.error(f"数据预处理失败: {e}")
            raise
    
    def setup_training_environment(self):
        """设置训练环境"""
        logger.info("=" * 60)
        logger.info("设置训练环境...")
        logger.info("=" * 60)
        
        try:
            # 创建微调配置
            config = FineTuneConfig()
            config.project_root = self.project_root
            config.data_dir = self.project_root / "data"
            config.output_dir = self.project_root / "output"
            config.checkpoint_dir = config.output_dir / "checkpoints"
            config.logs_dir = config.output_dir / "logs"
            
            # 设置环境
            script_file = config.setup_environment()
            
            logger.info("训练环境设置完成！")
            return script_file, config
            
        except Exception as e:
            logger.error(f"训练环境设置失败: {e}")
            raise
    
    def run_training(self, script_file: str):
        """
        运行训练
        
        Args:
            script_file: 训练脚本文件路径
        """
        logger.info("=" * 60)
        logger.info("开始微调训练...")
        logger.info("=" * 60)
        
        try:
            import subprocess
            
            # 运行训练脚本
            logger.info(f"执行训练脚本: {script_file}")
            
            # 切换到项目目录
            os.chdir(self.project_root)
            
            # 执行训练脚本
            result = subprocess.run(
                ["bash", script_file],
                capture_output=True,
                text=True,
                check=True
            )
            
            logger.info("训练脚本执行完成")
            logger.info(f"输出: {result.stdout}")
            
            if result.stderr:
                logger.warning(f"警告信息: {result.stderr}")
            
        except subprocess.CalledProcessError as e:
            logger.error(f"训练脚本执行失败: {e}")
            logger.error(f"错误输出: {e.stderr}")
            raise
        except Exception as e:
            logger.error(f"训练过程出错: {e}")
            raise
    
    def run_full_pipeline(self, force_regenerate: bool = False, skip_training: bool = False):
        """
        运行完整的微调流程
        
        Args:
            force_regenerate: 是否强制重新生成数据
            skip_training: 是否跳过训练步骤
        """
        logger.info("=" * 80)
        logger.info("开始红外图像微小目标检测微调流程")
        logger.info("=" * 80)
        
        try:
            # 步骤1: 数据预处理
            training_data_file, test_data_file = self.preprocess_data(force_regenerate)
            
            # 步骤2: 设置训练环境
            script_file, config = self.setup_training_environment()
            
            if skip_training:
                logger.info("跳过训练步骤（--skip-training 参数）")
                logger.info(f"训练脚本已准备好: {script_file}")
                logger.info("手动运行训练:")
                logger.info(f"cd {self.project_root}")
                logger.info("./train.sh")
            else:
                # 步骤3: 运行训练
                self.run_training(script_file)
            
            logger.info("=" * 80)
            logger.info("微调流程完成！")
            logger.info("=" * 80)
            logger.info(f"项目目录: {self.project_root}")
            logger.info(f"训练数据: {training_data_file}")
            logger.info(f"测试数据: {test_data_file}")
            logger.info(f"训练脚本: {script_file}")
            logger.info(f"输出目录: {config.output_dir}")
            
        except Exception as e:
            logger.error(f"微调流程失败: {e}")
            raise

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="红外图像微小目标检测微调程序")
    
    parser.add_argument(
        "--data-root",
        type=str,
        default="/home/<USER>/Qwen/Qwen2.5-VL/tiaozhanbei_datasets",
        help="原始数据根目录路径"
    )
    
    parser.add_argument(
        "--project-root",
        type=str,
        default=None,
        help="项目根目录路径（默认为当前脚本所在目录）"
    )
    
    parser.add_argument(
        "--force-regenerate",
        action="store_true",
        help="强制重新生成训练数据"
    )
    
    parser.add_argument(
        "--skip-training",
        action="store_true",
        help="跳过训练步骤，仅进行数据预处理和环境设置"
    )
    
    parser.add_argument(
        "--preprocess-only",
        action="store_true",
        help="仅进行数据预处理"
    )
    
    parser.add_argument(
        "--setup-only",
        action="store_true",
        help="仅进行环境设置"
    )
    
    args = parser.parse_args()
    
    try:
        # 创建微调器
        finetuner = InfraredFineTuner(
            data_root=args.data_root,
            project_root=args.project_root
        )
        
        if args.preprocess_only:
            # 仅数据预处理
            finetuner.preprocess_data(args.force_regenerate)
        elif args.setup_only:
            # 仅环境设置
            finetuner.setup_training_environment()
        else:
            # 运行完整流程
            finetuner.run_full_pipeline(
                force_regenerate=args.force_regenerate,
                skip_training=args.skip_training
            )
        
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
