"""
红外视频LoRA微调 - 完全重新实现
"""

import os
import json
import logging
import torch
import numpy as np
from pathlib import Path
from transformers import (
    AutoProcessor,
    Qwen2_5_VLForConditionalGeneration,
    TrainingArguments,
    Trainer
)
from peft import LoraConfig, get_peft_model, TaskType
from torch.utils.data import Dataset
from decord import VideoReader
from PIL import Image

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class VideoDataset(Dataset):
    """视频数据集"""
    
    def __init__(self, data_list, processor, max_frames=8):
        self.data_list = data_list
        self.processor = processor
        self.max_frames = max_frames
        
        # 设置左填充
        self.processor.tokenizer.padding_side = 'left'
        
    def __len__(self):
        return len(self.data_list)
    
    def load_video(self, video_path):
        """加载视频帧"""
        try:
            if not os.path.exists(video_path):
                logger.warning(f"视频不存在: {video_path}")
                return self._dummy_frames()
            
            vr = VideoReader(video_path, num_threads=1)
            total_frames = len(vr)
            
            if total_frames == 0:
                return self._dummy_frames()
            
            # 采样帧
            if total_frames <= self.max_frames:
                indices = list(range(total_frames))
            else:
                indices = np.linspace(0, total_frames - 1, self.max_frames, dtype=int)
            
            frames = vr.get_batch(indices).asnumpy()
            pil_frames = [Image.fromarray(frame) for frame in frames]
            
            # 确保帧数一致
            while len(pil_frames) < self.max_frames:
                pil_frames.append(pil_frames[-1])
            
            return pil_frames[:self.max_frames]
            
        except Exception as e:
            logger.warning(f"视频加载失败 {video_path}: {e}")
            return self._dummy_frames()
    
    def _dummy_frames(self):
        """虚拟帧"""
        frame = Image.new('RGB', (224, 224), 'black')
        return [frame] * self.max_frames
    
    def __getitem__(self, idx):
        """获取数据项 - 只处理单个索引"""
        if isinstance(idx, (list, tuple)):
            # 如果是批量索引，递归处理
            return [self.__getitem__(i) for i in idx]
        
        if not isinstance(idx, int):
            raise TypeError(f"索引必须是整数，得到: {type(idx)}")
        
        item = self.data_list[idx]
        conversations = item['conversations']
        video_path = item['video']
        
        # 加载视频
        frames = self.load_video(video_path)
        
        # 构建文本
        user_text = conversations[0]['value'].replace('<video>\n', '').strip()
        assistant_text = conversations[1]['value'].strip()
        text = f"Human: {user_text}\nAssistant: {assistant_text}"
        
        # 处理
        try:
            inputs = self.processor(
                images=frames,
                text=text,
                padding=False,
                truncation=True,
                max_length=2048,
                return_tensors="pt"
            )
            
            input_ids = inputs['input_ids'].squeeze(0)
            attention_mask = inputs['attention_mask'].squeeze(0)
            pixel_values = inputs.get('pixel_values', torch.zeros(self.max_frames, 3, 224, 224))
            
            if pixel_values.dim() == 5:
                pixel_values = pixel_values.squeeze(0)
            
            return {
                'input_ids': input_ids,
                'attention_mask': attention_mask,
                'labels': input_ids.clone(),
                'pixel_values': pixel_values
            }
            
        except Exception as e:
            logger.error(f"处理失败 {video_path}: {e}")
            # 返回虚拟数据
            dummy_ids = torch.tensor([151643] * 50)
            return {
                'input_ids': dummy_ids,
                'attention_mask': torch.ones_like(dummy_ids),
                'labels': dummy_ids.clone(),
                'pixel_values': torch.zeros(self.max_frames, 3, 224, 224)
            }

def collate_batch(batch):
    """批量整理"""
    input_ids = [item['input_ids'] for item in batch]
    attention_masks = [item['attention_mask'] for item in batch]
    labels = [item['labels'] for item in batch]
    pixel_values = [item['pixel_values'] for item in batch]
    
    # 计算最大长度
    max_len = max(len(ids) for ids in input_ids)
    pad_token = 151643
    
    # 左填充
    padded_input_ids = []
    padded_attention_masks = []
    padded_labels = []
    
    for i in range(len(batch)):
        pad_len = max_len - len(input_ids[i])
        
        padded_input_ids.append(torch.cat([
            torch.full((pad_len,), pad_token, dtype=input_ids[i].dtype),
            input_ids[i]
        ]))
        
        padded_attention_masks.append(torch.cat([
            torch.zeros(pad_len, dtype=attention_masks[i].dtype),
            attention_masks[i]
        ]))
        
        padded_labels.append(torch.cat([
            torch.full((pad_len,), -100, dtype=labels[i].dtype),
            labels[i]
        ]))
    
    # 处理视频
    max_frames = max(pv.shape[0] for pv in pixel_values)
    padded_pixel_values = []
    
    for pv in pixel_values:
        if pv.shape[0] < max_frames:
            pad_frames = max_frames - pv.shape[0]
            padding = torch.zeros(pad_frames, pv.shape[1], pv.shape[2], pv.shape[3])
            pv = torch.cat([pv, padding], dim=0)
        padded_pixel_values.append(pv)
    
    return {
        'input_ids': torch.stack(padded_input_ids),
        'attention_mask': torch.stack(padded_attention_masks),
        'labels': torch.stack(padded_labels),
        'pixel_values': torch.stack(padded_pixel_values)
    }

def main():
    """主函数"""
    logger.info("🚀 开始视频LoRA微调...")
    
    # 配置
    model_path = "/home/<USER>/Qwen/Qwen2.5-VL/Qwen2.5-VL-7B-Instruct"
    data_path = "data/infrared_video_train.json"
    output_path = "output/video_lora_final"
    
    Path(output_path).mkdir(parents=True, exist_ok=True)
    
    # 加载数据
    logger.info("📊 加载数据...")
    with open(data_path, 'r') as f:
        train_data = json.load(f)
    logger.info(f"✅ 数据: {len(train_data)} 个样本")
    
    # 加载processor
    logger.info("🔤 加载processor...")
    processor = AutoProcessor.from_pretrained(model_path, trust_remote_code=True)
    processor.tokenizer.padding_side = 'left'
    
    # 创建数据集
    logger.info("📝 创建数据集...")
    dataset = VideoDataset(train_data, processor, max_frames=8)
    
    # 加载模型
    logger.info("🤖 加载模型...")
    model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
        model_path,
        torch_dtype=torch.bfloat16,
        device_map={"": "cuda:0"},
        trust_remote_code=True,
        attn_implementation="flash_attention_2"
    )
    
    # LoRA配置
    logger.info("⚙️ 配置LoRA...")
    lora_config = LoraConfig(
        task_type=TaskType.CAUSAL_LM,
        target_modules=["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"],
        inference_mode=False,
        r=64,
        lora_alpha=64,
        lora_dropout=0.05,
        bias="none",
    )
    
    model = get_peft_model(model, lora_config)
    model.print_trainable_parameters()
    
    # 训练参数
    training_args = TrainingArguments(
        output_dir=output_path,
        per_device_train_batch_size=1,
        gradient_accumulation_steps=16,
        num_train_epochs=1,
        learning_rate=2e-5,
        weight_decay=0.01,
        warmup_ratio=0.03,
        max_grad_norm=1.0,
        lr_scheduler_type="cosine",
        logging_steps=5,
        save_steps=50,
        save_strategy="steps",
        save_total_limit=3,
        remove_unused_columns=False,
        bf16=True,
        dataloader_num_workers=0,  # 禁用多进程
        gradient_checkpointing=False,
        report_to="none",
    )
    
    # 训练器
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=dataset,
        data_collator=collate_batch,
        processing_class=processor,
    )
    
    # 开始训练
    logger.info("🎯 开始训练...")
    logger.info(f"样本数: {len(dataset)}")
    logger.info(f"批次大小: {training_args.per_device_train_batch_size}")
    logger.info(f"梯度累积: {training_args.gradient_accumulation_steps}")
    logger.info(f"学习率: {training_args.learning_rate}")
    
    trainer.train()
    
    # 保存
    logger.info("💾 保存模型...")
    trainer.save_model()
    processor.save_pretrained(output_path)
    
    logger.info("🎉 训练完成!")
    logger.info(f"📁 保存位置: {output_path}")

if __name__ == "__main__":
    main()
