"""
基于官方实现的红外视频LoRA微调脚本
参考 /home/<USER>/Qwen/Qwen2.5-VL/qwen-vl-finetune/
"""

import os
import json
import logging
import torch
import copy
import numpy as np
from pathlib import Path
from transformers import (
    AutoProcessor,
    Qwen2_5_VLForConditionalGeneration,
    TrainingArguments,
    Trainer
)
from peft import LoraConfig, get_peft_model, TaskType
from torch.utils.data import Dataset
from decord import VideoReader
from PIL import Image

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class OfficialStyleVideoDataset(Dataset):
    """基于官方实现的视频数据集"""
    
    def __init__(self, data_list, processor, data_args):
        self.data_list = data_list
        self.processor = processor
        self.data_args = data_args
        
        # 设置左填充
        self.processor.tokenizer.padding_side = 'left'
        
    def __len__(self):
        return len(self.data_list)
    
    def process_video_frames(self, video, frame_idx, video_length):
        """基于官方实现的视频帧处理"""
        fps = len(frame_idx) / video_length
        processor = copy.deepcopy(self.processor.image_processor)
        processor.max_pixels = self.data_args.video_max_frame_pixels
        processor.min_pixels = self.data_args.video_min_frame_pixels
        
        # 处理视频帧
        video_processed = processor.preprocess(
            images=None, 
            videos=video, 
            return_tensors="pt"
        )
        
        video_tensor = video_processed["pixel_values_videos"]
        grid_thw = video_processed["video_grid_thw"][0]
        
        return video_tensor, grid_thw
    
    def video_decord(self, video_file):
        """基于官方实现的视频加载"""
        if not os.path.exists(video_file):
            logger.warning(f"视频文件不存在: {video_file}")
            return None, None
            
        try:
            vr = VideoReader(video_file, num_threads=4)
            total_frames = len(vr)
            avg_fps = vr.get_avg_fps()
            video_length = total_frames / avg_fps
            interval = self.data_args.base_interval
            
            num_frames_to_sample = round(video_length / interval)
            target_frames = min(
                max(num_frames_to_sample, self.data_args.video_min_frames), 
                self.data_args.video_max_frames
            )
            
            frame_idx = np.linspace(0, total_frames - 1, target_frames, dtype=int)
            frame_idx = np.unique(frame_idx)
            video = vr.get_batch(frame_idx).asnumpy()
            
            return self.process_video_frames(video, frame_idx, video_length)
            
        except Exception as e:
            logger.error(f"视频处理失败 {video_file}: {e}")
            return None, None
    
    def __getitem__(self, idx):
        """获取数据项"""
        if not isinstance(idx, int):
            raise TypeError(f"索引必须是整数，得到: {type(idx)}")
        
        item = self.data_list[idx]
        conversations = item['conversations']
        video_path = item['video']
        
        # 处理视频
        video_tensor, grid_thw = self.video_decord(video_path)
        
        if video_tensor is None:
            # 返回虚拟数据
            logger.warning(f"使用虚拟数据: {video_path}")
            video_tensor = torch.zeros(1, 3, 336, 336)
            grid_thw = torch.tensor([1, 24, 24])
        
        # 构建文本
        user_text = conversations[0]['value'].replace('<video>\n', '').strip()
        assistant_text = conversations[1]['value'].strip()
        
        # 使用官方的对话格式
        messages = [
            {
                "role": "user",
                "content": [
                    {"type": "video", "video": video_path},
                    {"type": "text", "text": user_text}
                ]
            },
            {
                "role": "assistant", 
                "content": assistant_text
            }
        ]
        
        # 使用processor处理
        try:
            text = self.processor.apply_chat_template(
                messages, 
                tokenize=False, 
                add_generation_prompt=False
            )
            
            # Tokenize
            tokenized = self.processor.tokenizer(
                text,
                padding=False,
                truncation=True,
                max_length=8192,  # 使用官方推荐的长度
                return_tensors="pt"
            )
            
            input_ids = tokenized['input_ids'].squeeze(0)
            attention_mask = tokenized['attention_mask'].squeeze(0)
            
            return {
                'input_ids': input_ids,
                'attention_mask': attention_mask,
                'labels': input_ids.clone(),
                'pixel_values_videos': video_tensor.squeeze(0) if video_tensor.dim() == 5 else video_tensor,
                'video_grid_thw': grid_thw.unsqueeze(0) if grid_thw.dim() == 1 else grid_thw
            }
            
        except Exception as e:
            logger.error(f"处理失败 {video_path}: {e}")
            # 返回虚拟数据
            dummy_ids = torch.tensor([151643] * 100)
            return {
                'input_ids': dummy_ids,
                'attention_mask': torch.ones_like(dummy_ids),
                'labels': dummy_ids.clone(),
                'pixel_values_videos': torch.zeros(1, 3, 336, 336),
                'video_grid_thw': torch.tensor([[1, 24, 24]])
            }

def collate_fn(batch):
    """数据整理函数"""
    input_ids = [item['input_ids'] for item in batch]
    attention_masks = [item['attention_mask'] for item in batch]
    labels = [item['labels'] for item in batch]
    pixel_values_videos = [item['pixel_values_videos'] for item in batch]
    video_grid_thws = [item['video_grid_thw'] for item in batch]
    
    # 计算最大长度
    max_length = max(len(ids) for ids in input_ids)
    pad_token_id = 151643
    
    # 左填充
    padded_input_ids = []
    padded_attention_masks = []
    padded_labels = []
    
    for i in range(len(batch)):
        pad_length = max_length - len(input_ids[i])
        
        padded_input_ids.append(torch.cat([
            torch.full((pad_length,), pad_token_id, dtype=input_ids[i].dtype),
            input_ids[i]
        ]))
        
        padded_attention_masks.append(torch.cat([
            torch.zeros(pad_length, dtype=attention_masks[i].dtype),
            attention_masks[i]
        ]))
        
        padded_labels.append(torch.cat([
            torch.full((pad_length,), -100, dtype=labels[i].dtype),
            labels[i]
        ]))
    
    # 处理视频数据
    max_frames = max(pv.shape[0] if pv.dim() > 3 else 1 for pv in pixel_values_videos)
    padded_pixel_values = []
    
    for pv in pixel_values_videos:
        if pv.dim() == 3:
            pv = pv.unsqueeze(0)
        
        if pv.shape[0] < max_frames:
            pad_frames = max_frames - pv.shape[0]
            padding = torch.zeros(pad_frames, pv.shape[1], pv.shape[2], pv.shape[3])
            pv = torch.cat([pv, padding], dim=0)
        
        padded_pixel_values.append(pv)
    
    return {
        'input_ids': torch.stack(padded_input_ids),
        'attention_mask': torch.stack(padded_attention_masks),
        'labels': torch.stack(padded_labels),
        'pixel_values_videos': torch.stack(padded_pixel_values),
        'video_grid_thw': torch.cat(video_grid_thws, dim=0)
    }

class DataArgs:
    """数据参数配置"""
    def __init__(self):
        self.video_max_frames = 8
        self.video_min_frames = 4
        self.base_interval = 4  # 官方默认值
        self.video_max_frame_pixels = 32 * 28 * 28  # 官方配置
        self.video_min_frame_pixels = 4 * 28 * 28   # 官方配置

def main():
    """主函数"""
    logger.info("🚀 开始基于官方实现的视频LoRA微调...")
    
    # 配置
    model_path = "/home/<USER>/Qwen/Qwen2.5-VL/Qwen2.5-VL-7B-Instruct"
    data_path = "data/infrared_video_train.json"
    output_path = "output/official_style_lora"
    
    Path(output_path).mkdir(parents=True, exist_ok=True)
    
    # 加载数据
    logger.info("📊 加载数据...")
    with open(data_path, 'r') as f:
        train_data = json.load(f)
    logger.info(f"✅ 数据: {len(train_data)} 个样本")
    
    # 加载processor
    logger.info("🔤 加载processor...")
    processor = AutoProcessor.from_pretrained(model_path, trust_remote_code=True)
    processor.tokenizer.padding_side = 'left'
    
    # 数据参数
    data_args = DataArgs()
    
    # 创建数据集
    logger.info("📝 创建数据集...")
    dataset = OfficialStyleVideoDataset(train_data, processor, data_args)
    
    # 加载模型
    logger.info("🤖 加载模型...")
    model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
        model_path,
        torch_dtype=torch.bfloat16,
        device_map={"": "cuda:0"},
        trust_remote_code=True,
        attn_implementation="flash_attention_2"
    )
    
    # LoRA配置（参考官方）
    logger.info("⚙️ 配置LoRA...")
    lora_config = LoraConfig(
        task_type=TaskType.CAUSAL_LM,
        target_modules=["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"],
        inference_mode=False,
        r=64,
        lora_alpha=64,
        lora_dropout=0.05,
        bias="none",
    )
    
    model = get_peft_model(model, lora_config)
    model.print_trainable_parameters()
    
    # 训练参数（参考官方配置）
    training_args = TrainingArguments(
        output_dir=output_path,
        per_device_train_batch_size=4,  # 官方配置
        gradient_accumulation_steps=4,  # 官方配置
        num_train_epochs=0.5,  # 官方配置
        learning_rate=2e-7,  # 官方配置
        weight_decay=0,  # 官方配置
        warmup_ratio=0.03,  # 官方配置
        max_grad_norm=1.0,  # 官方配置
        lr_scheduler_type="cosine",  # 官方配置
        logging_steps=1,  # 官方配置
        save_steps=1000,  # 官方配置
        save_strategy="steps",
        save_total_limit=1,  # 官方配置
        remove_unused_columns=False,
        bf16=True,  # 官方配置
        dataloader_num_workers=4,  # 官方配置
        gradient_checkpointing=True,  # 官方配置
        report_to="none",
        model_max_length=8192,  # 官方配置
    )
    
    # 训练器
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=dataset,
        data_collator=collate_fn,
        processing_class=processor,
    )
    
    # 开始训练
    logger.info("🎯 开始训练（官方配置）...")
    logger.info(f"样本数: {len(dataset)}")
    logger.info(f"批次大小: {training_args.per_device_train_batch_size}")
    logger.info(f"梯度累积: {training_args.gradient_accumulation_steps}")
    logger.info(f"学习率: {training_args.learning_rate}")
    logger.info(f"训练轮数: {training_args.num_train_epochs}")
    
    trainer.train()
    
    # 保存
    logger.info("💾 保存模型...")
    trainer.save_model()
    processor.save_pretrained(output_path)
    
    logger.info("🎉 训练完成!")
    logger.info(f"📁 保存位置: {output_path}")

if __name__ == "__main__":
    main()
